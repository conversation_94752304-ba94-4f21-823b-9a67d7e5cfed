package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.common.model.masterdata.cell.entity.DicWhStorageCell;
import com.inossem.wms.common.model.masterdata.cell.po.DicWhStorageCellSearchPO;
import com.inossem.wms.common.model.masterdata.cell.vo.DicWhStorageCellPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicWhStorageCellMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 存储单元主数据 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Service
public class DicWhStorageCellDataWrap extends BaseDataWrap<DicWhStorageCellMapper, DicWhStorageCell> {



    /**
     * 存储单元列表分页
     *
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicWhStorageCellPageVO> getDicWhStorageCellPageVOList(IPage<DicWhStorageCellPageVO> page, QueryWrapper<DicWhStorageCellSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicWhStorageCellPageVOList(page, wrapper));
    }
}
