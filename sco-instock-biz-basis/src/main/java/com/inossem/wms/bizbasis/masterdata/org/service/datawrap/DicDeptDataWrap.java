package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicDeptMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 部门 服务实现类
 */
@Service
public class DicDeptDataWrap extends BaseDataWrap<DicDeptMapper, DicDept> {

    @Autowired
    private DataFillService dataFillService;

    /**
     * 部门列表查询
     *
     * @param pageData     IPage
     * @param pageWrapper  WmsQueryWrapper
     * @param dataFillType EnumDataFillType
     */
    public void getDeptPageList(IPage<DicDeptDTO> pageData,
                                WmsQueryWrapper<MetaDataDeptOfficePO> pageWrapper,

                                EnumDataFillType dataFillType) {
        List<DicDeptDTO> inputList = this.baseMapper.getDicDeptList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        pageData.setRecords(inputList);
    }


    /**
     * 部门科室树查询
     *
     * @param po MetaDataDeptOfficePO
     */
    public List<DicDeptDTO> getDeptOfficeTree(MetaDataDeptOfficePO po) {
        return this.baseMapper.getDeptOfficeTree(po);
    }

    /**
     * 用户所属部门科室树查询
     *
     * @param po MetaDataDeptOfficePO
     */
    public List<DicDeptDTO> getUserDeptOfficeTree(MetaDataDeptOfficePO po) {
        return this.baseMapper.getUserDeptOfficeTree(po);
    }

}
