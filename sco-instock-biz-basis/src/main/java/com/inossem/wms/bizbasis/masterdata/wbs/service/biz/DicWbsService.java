package com.inossem.wms.bizbasis.masterdata.wbs.service.biz;

import com.inossem.wms.bizbasis.masterdata.wbs.service.component.DicWbsComponent;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.common.model.common.base.BizContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * WBS主数据管理
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Slf4j
@Service
public class DicWbsService {

    @Autowired
    protected DicWbsComponent dicWbsComponent;
    @Autowired
    protected HXSapIntegerfaceService hxSapIntegerfaceService;

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        dicWbsComponent.getPage(ctx);
    }

    /**
     * 同步WBS
     */
    public void sync(BizContext ctx) {
        dicWbsComponent.sync(ctx);
    }

    /**
     * WBS主数据同步-定时任务
     */
    public void handleWbsSync() {
        hxSapIntegerfaceService.synWBSInfo(new Date(), new Date());
    }
} 
