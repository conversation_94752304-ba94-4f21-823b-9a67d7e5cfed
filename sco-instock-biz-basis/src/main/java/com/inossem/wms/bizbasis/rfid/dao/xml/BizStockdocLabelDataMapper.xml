<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.rfid.dao.BizStockdocLabelDataMapper">

    <select id="selectByStockBin" parameterType="com.inossem.wms.common.model.stock.dto.StockBinDTO" resultType="com.inossem.wms.common.model.label.entity.BizStockdocLabelData">
        SELECT
            id,
            label_code,
            label_type,
            label_model,
            mat_id,
            fty_id,
            location_id,
            batch_id,
            sn_code,
            qty,
            wh_id,
            type_id,
            bin_id,
            cell_id,
            source_label_id
        FROM
            biz_stockdoc_label_data
        WHERE is_delete = 0
        <if test="matId !=null and matId !=0 ">
            AND mat_id = #{matId}
        </if>
        <if test="ftyId !=null and ftyId !=0 ">
            AND fty_id = #{ftyId}
        </if>
        <if test="locationId !=null and locationId !=0 ">
            AND location_id = #{locationId}
        </if>
        <if test="batchId !=null and batchId !=0 ">
            AND batch_id = #{batchId}
        </if>
        <if test="whId !=null and whId !=0 ">
            AND wh_id = #{whId}
        </if>
        <if test="typeId !=null and typeId !=0 ">
            AND type_id = #{typeId}
        </if>
        <if test="binId !=null and binId !=0 ">
            AND bin_id = #{binId}
        </if>
        <if test="cellId !=null and cellId !=0 ">
            AND cell_id = #{cellId}
        </if>
    </select>

    <select id="selectByStockBinList" parameterType="java.util.List" resultType="com.inossem.wms.common.model.label.entity.BizStockdocLabelData">
        SELECT
            id,
            label_code,
            label_type,
            label_model,
            mat_id,
            fty_id,
            location_id,
            batch_id,
            sn_code,
            qty,
            wh_id,
            type_id,
            bin_id,
            cell_id,
            source_label_id
        FROM
            biz_stockdoc_label_data
        WHERE is_delete = 0
            AND (mat_id,fty_id,location_id,batch_id,type_id,bin_id,cell_id) in
        <foreach collection="list" open="(" item="item" separator="," index="index" close=")">
                (
                        #{item.matId},
                        #{item.ftyId},
                        #{item.locationId},
                        #{item.batchId},

                        #{item.typeId},
                        #{item.binId},
                        #{item.cellId}
                )
        </foreach>
    </select>

    <select id="getListByTaskReqItemId"  resultType="com.inossem.wms.common.model.label.entity.BizStockdocLabelData">
        SELECT
            id,
            label_code,
            label_type,
            label_model,
            mat_id,
            fty_id,
            location_id,
            batch_id,
            sn_code,
            qty,
            wh_id,
            type_id,
            bin_id,
            cell_id,
            source_label_id
        FROM
            biz_stockdoc_label_data
        WHERE
            is_delete = 0 and
            id in
            (select r.label_id from biz_label_receipt_rel r where r.receipt_item_id = #{taskReqItemId})
    </select>

    <select id="selectByStockBinNonCellList" parameterType="java.util.List" resultType="com.inossem.wms.common.model.label.entity.BizStockdocLabelData">
        SELECT
        id,
        label_code,
        label_type,
        label_model,
        mat_id,
        fty_id,
        location_id,
        batch_id,
        sn_code,
        qty,
        wh_id,
        type_id,
        bin_id,
        cell_id,
        source_label_id
        FROM
        biz_stockdoc_label_data
        WHERE is_delete = 0
        AND (mat_id,fty_id,location_id,batch_id,type_id,bin_id) in
        <foreach collection="list" open="(" item="item" separator="," index="index" close=")">
            (
            #{item.matId},
            #{item.ftyId},
            #{item.locationId},
            #{item.batchId},

            #{item.typeId},
            #{item.binId}
            )
        </foreach>
    </select>

    <update id="deleteByIds">
        update biz_stockdoc_label_data set is_delete=#{flag} where id in
        <foreach collection="idList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>
</mapper>
