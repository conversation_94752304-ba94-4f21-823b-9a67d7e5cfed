package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.org.storagetype.dto.DicWhStorageTypeDTO;
import com.inossem.wms.common.model.org.storagetype.po.DicWhStorageTypeSavePO;
import com.inossem.wms.common.model.org.storagetype.po.DicWhStorageTypeSearchPO;
import com.inossem.wms.common.model.org.storagetype.vo.DicWhStorageTypePageVO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 存储类型表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@RestController
@Api(tags = "存储类型管理")
public class WhStorageTypeController {

    @Autowired
    private WhStorageTypeService whStorageTypeService;

    /**
     * 获取存储类型列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 存储类型集合列表
     */
    @ApiOperation(value = "获取存储类型列表", tags = {"存储类型管理"})
    @PostMapping(path = "/org/wh-storage-type/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWhStorageTypePageVO>> getPage(@RequestBody DicWhStorageTypeSearchPO po, BizContext ctx) {
        return BaseResult.success(whStorageTypeService.getPage(ctx));
    }

    /**
     * 查看存储类型详情
     * 
     * <AUTHOR>
     * @param id 存储类型Id
     * @return 存储类型详情
     */
    @ApiOperation(value = "按照存储类型编码查找存储类型", tags = {"存储类型管理"})
    @GetMapping(path = "/org/wh-storage-type/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWhStorageTypeDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(whStorageTypeService.get(ctx));
    }

    /**
     * 新增存储类型
     * 
     * <AUTHOR>
     * @param po 存储类型入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增存储类型信息", notes = "对存储类型信息进行添加", tags = {"存储类型管理"})
    @PostMapping(path = "/org/wh-storage-type", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWhStorageTypeSavePO po, BizContext ctx) {
        // 储存存地点信息
        whStorageTypeService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STORAGE_TYPE_SAVE_SUCCESS, po.getStorageTypeInfo().getTypeCode());
    }

    /**
     * 修改存储类型
     *
     * <AUTHOR>
     * @param po 存储类型入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改存储类型信息", notes = "对存储类型信息进行修改", tags = {"存储类型管理"})
    @PutMapping(path = "/org/wh-storage-type", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWhStorageTypeSavePO po, BizContext ctx) {
        // 储存存地点信息
        whStorageTypeService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STORAGE_TYPE_SAVE_SUCCESS, po.getStorageTypeInfo().getTypeCode());
    }

    /**
     * 删除存储类型
     * 
     * @param id 存储类型Id
     * <AUTHOR>
     * @param ctx 上下文对象
     * @return 删除结果
     */
    @ApiOperation(value = "按照存储类型编码删除存储类型", notes = "逻辑删除", tags = {"存储类型管理"})
    @DeleteMapping(path = "/org/wh-storage-type/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除存储类型信息
        String typeCode = whStorageTypeService.remove(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STORAGE_TYPE_DELETE_SUCCESS, typeCode);
    }

    /**
     * 存储类型导入
     * @param file 存储类型excel
     * @param ctx 上下文对象
     * <AUTHOR>
     */
    @ApiOperation(value = "存储类型导入", notes = "存储类型导入", tags = {"存储类型管理"})
    @PostMapping(path = "/org/wh-storage-type/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestPart("file") MultipartFile file, BizContext ctx) {
        // 导入存储类型信息
        whStorageTypeService.importWhStorageType(file, ctx);
        return BaseResult.success();
    }

}
