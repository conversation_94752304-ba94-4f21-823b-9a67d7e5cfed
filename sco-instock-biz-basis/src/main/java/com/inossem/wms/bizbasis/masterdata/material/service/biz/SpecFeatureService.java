package com.inossem.wms.bizbasis.masterdata.material.service.biz;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.spec.service.datawrap.BizSpecFeatureDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.spec.EnumSpecFeatureType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.spec.SpecFeatureMapVo;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureValueDTO;
import com.inossem.wms.common.model.masterdata.spec.entity.BizSpecFeature;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecFeatureSavePO;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecFeatureSearchPO;
import com.inossem.wms.common.model.masterdata.spec.vo.BizSpecFeaturePageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

/**
 * <p>
 * 特性表 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@Service
@Slf4j
public class SpecFeatureService {

    @Autowired
    protected BizSpecFeatureDataWrap bizSpecFeatureDataWrap;

    /**
     * 数据填充实现类
     */
    @Autowired
    protected DataFillService dataFillService;

    /**
     * 查询特性列表
     *
     * @param ctx-po 查询条件
     * @return 特性列表
     *
     */
    public PageObjectVO<BizSpecFeaturePageVO> getPage(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        BizSpecFeatureSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 设置查询条件 ******** */
        QueryWrapper<BizSpecFeatureSearchPO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getSpecFeatureCode()), BizSpecFeatureSearchPO::getSpecFeatureCode, po.getSpecFeatureCode())
                .like(UtilString.isNotNullOrEmpty(po.getSpecFeatureName()), BizSpecFeatureSearchPO::getSpecFeatureName, po.getSpecFeatureName());
        IPage<BizSpecFeaturePageVO> page = po.getPageObj(BizSpecFeaturePageVO.class);
        bizSpecFeatureDataWrap.getBizSpecFeaturePageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 查询特性详情
     * 
     * @param ctx-id 主键id
     * @return 特性详情
     *
     */
    public SingleResultVO<BizSpecFeatureDTO> getInfo(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询特性详情 ******** */
        BizSpecFeature bizSpecFeature = bizSpecFeatureDataWrap.getById(id);
        /* ********* 数据泛型转换 ******** */
        BizSpecFeatureDTO bizSpecFeatureDto = UtilBean.newInstance(bizSpecFeature, BizSpecFeatureDTO.class);
        /* ********* 设置特性数据类型描述 ******** */
        // specFeatureDto.setSpecFeatureTypeName(EnumSpecFeatureType.getNameByValue(UtilObject.getStringOrEmpty(specFeatureDto.getSpecFeatureType())));
        /* ********* 特性值转换 ******** */
        if (bizSpecFeatureDto.getSpecFeatureType().equals(EnumSpecFeatureType.FIELD_PULL.getValue())) {
            bizSpecFeatureDto.setBizSpecFeatureValueDTOList(JSON.parseArray(bizSpecFeatureDto.getInfo(), BizSpecFeatureValueDTO.class));
        }
        return new SingleResultVO<>(bizSpecFeatureDto);
    }

    /**
     * 新增或修改特性信息
     *
     * @param ctx-po 特性信息
     * @param ctx-cUser 当前登录人信息
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        BizSpecFeatureSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 入参非空效验 ******** */
        BizSpecFeatureDTO bizSpecFeatureDto = po.getBizSpecFeatureInfo();
        if (bizSpecFeatureDto == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 特性非空效验 ******** */
        if (UtilString.isNullOrEmpty(bizSpecFeatureDto.getSpecFeatureCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 特性描述非空效验 ******** */
        if (UtilString.isNullOrEmpty(bizSpecFeatureDto.getSpecFeatureName())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 数据类型非空效验 ******** */
        if (UtilNumber.isEmpty(bizSpecFeatureDto.getSpecFeatureType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 判断id是否为空 ******** */
        if (UtilNumber.isEmpty(bizSpecFeatureDto.getId())) {
            // 新增
            bizSpecFeatureDto.setCreateUserId(cUser.getId());
            // 校验特性编码是否已存在
            QueryWrapper<BizSpecFeature> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizSpecFeature::getSpecFeatureCode, bizSpecFeatureDto.getSpecFeatureCode());
            BizSpecFeature bizSpecFeature = bizSpecFeatureDataWrap.getOne(queryWrapper);
            if (bizSpecFeature != null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_CODE_EXIST, bizSpecFeatureDto.getSpecFeatureCode());
            }
        }
        /* ********* id是否有对应特性信息效验 ******** */
        else if (null == bizSpecFeatureDataWrap.getById(bizSpecFeatureDto.getId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        bizSpecFeatureDto.setModifyUserId(cUser.getId());
        /* ********* 转化特性值集合 ******** */
        bizSpecFeatureDto.setInfo(Const.STRING_EMPTY);
        if (bizSpecFeatureDto.getSpecFeatureType().equals(EnumSpecFeatureType.FIELD_PULL.getValue())) {
            bizSpecFeatureDto.setInfo(JSON.toJSONString(bizSpecFeatureDto.getBizSpecFeatureValueDTOList().stream().map(value -> {
                BizSpecFeatureValueDTO bizSpecFeatureValueDto = new BizSpecFeatureValueDTO();
                bizSpecFeatureValueDto.setFeatureCode(value.getFeatureCode());
                bizSpecFeatureValueDto.setFeatureValue(value.getFeatureValue());
                return bizSpecFeatureValueDto;
            }).collect(Collectors.toList())));
        }
        /* ********* 数据泛型转换 ******** */
        BizSpecFeature bizSpecFeature = UtilBean.newInstance(bizSpecFeatureDto, BizSpecFeature.class);
        /* ********* 新增或修改特性信息 ******** */
        bizSpecFeatureDataWrap.saveOrUpdate(bizSpecFeature);
    }

    /**
     * 删除特性信息
     *
     * @param ctx-id 主键Id
     *
     */
    public String remove(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 设置特性code ******** */
        String featureCode = bizSpecFeatureDataWrap.getById(id).getSpecFeatureCode();
        /* ********* 删除特性信息 ******** */
        if(bizSpecFeatureDataWrap.removeById(id)){
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SPEC_FEATURE_DELETE_FAILURE, featureCode);
        }
        return featureCode;
    }

    /**
     * 查询特性数据类型下拉
     *
     * @return 特性数据类型下拉框
     *
     */
    public MultiResultVO<SpecFeatureMapVo> getDown() {
        return new MultiResultVO<>(EnumSpecFeatureType.toList());
    }
}
