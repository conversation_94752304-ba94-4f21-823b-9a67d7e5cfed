package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.storagebin.po.DicWhStorageBinSavePO;
import com.inossem.wms.common.model.masterdata.storagebin.po.DicWhStorageBinSearchPO;
import com.inossem.wms.common.model.masterdata.storagebin.vo.DicWhStorageBinPageVO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageBinService;
import com.inossem.wms.common.model.org.section.dto.DicWhStorageSectionDTO;
import com.inossem.wms.common.model.org.section.entity.DicWhStorageSection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 仓位表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@RestController
@Api(tags = "仓位管理")
public class WhStorageBinController {

    @Autowired
    private WhStorageBinService whStorageBinService;

    /**
     * 获取仓位列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 仓位集合列表
     */
     @ApiOperation(value = "获取仓位列表", tags = {"仓位管理"})
     @PostMapping(path = "/master-data/bin/results", produces = MediaType.APPLICATION_JSON_VALUE)
     public BaseResult<PageObjectVO<DicWhStorageBinPageVO>> getPage(@RequestBody DicWhStorageBinSearchPO po, BizContext
     ctx) {
     return BaseResult.success(whStorageBinService.getPage(ctx));
     }

    /**
     * 查看仓位详情
     * 
     * <AUTHOR>
     * @param id 仓位Id
     * @return 仓位详情
     */
    @ApiOperation(value = "按照仓位编码查找仓位", tags = {"仓位管理"})
    @GetMapping(path = "/master-data/bin/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWhStorageBinDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(whStorageBinService.get(ctx));
    }

    /**
     * 新增仓位
     * 
     * <AUTHOR>
     * @param po 仓位入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增仓位信息", notes = "对仓位信息进行添加", tags = {"仓位管理"})
    @PostMapping(path = "/master-data/bin", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWhStorageBinSavePO po, BizContext ctx) {
        // 储仓位信息
        whStorageBinService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_SAVE_SUCCESS, po.getStorageBinInfo().getBinCode());
    }

    /**
     * 修改仓位
     *
     * <AUTHOR>
     * @param po 仓位入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改仓位信息", notes = "对仓位信息进行修改", tags = {"仓位管理"})
    @PutMapping(path = "/master-data/bin", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWhStorageBinSavePO po, BizContext ctx) {
        // 储仓位信息
        whStorageBinService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_SAVE_SUCCESS, po.getStorageBinInfo().getBinCode());
    }

    /**
     * 删除仓位
     * 
     * @param id 仓位Id
     * @param ctx 上下文对象
     * <AUTHOR>
     * @return 删除结果
     */
    @ApiOperation(value = "按照仓位编码删除仓位", notes = "逻辑删除", tags = {"仓位管理"})
    @DeleteMapping(path = "/master-data/bin/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除仓位信息
        String binCode = whStorageBinService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_DELETE_SUCCESS, binCode);
    }

    /**
     * 仓位主数据导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "仓位主数据导入", notes = "仓位主数据导入", tags = {"仓位管理"})
    @PostMapping(path = "/master-data/bin/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importStorageBin(@RequestPart("file") MultipartFile file, BizContext ctx) {

        whStorageBinService.importStorageBin(ctx);
        return BaseResult.success();
    }

    /**
     * 根据存储类型id获取存储区
     * @return 符合条件的存储区
     */
    @ApiOperation(value = "根据存储类型id获取存储区", tags = {"物料仓库主数据管理"})
    @GetMapping(path = "/master-data/section/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicWhStorageSection>> getSectionByTypeId(@PathVariable("id") long id) {
        return BaseResult.success(whStorageBinService.getSectionByTypeId(id));
    }

    @ApiOperation(value = "根据存储类型id获取存储区", tags = {"物料仓库主数据管理"})
    @GetMapping(path = "/master-data/over-weight", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> calculateStockBinOverWeightJob() {
        whStorageBinService.calculateStockBinOverWeightJob();
        return BaseResult.success();
    }

    /**
     * 导出Excel
     */
    @ApiOperation(value = "导出Excel", tags = {"物料仓库主数据管理"})
    @PostMapping(path = "/master-data/bin/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody DicWhStorageBinSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        whStorageBinService.export(ctx);
        return BaseResult.success();
    }

}
