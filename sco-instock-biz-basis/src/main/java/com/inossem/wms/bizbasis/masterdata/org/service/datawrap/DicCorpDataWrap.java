package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.corp.po.DicCorpSearchPO;
import com.inossem.wms.common.model.org.corp.vo.DicCorpPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicCorpMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 公司主数据表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class DicCorpDataWrap extends BaseDataWrap<DicCorpMapper, DicCorp> {

    /**
     * 公司列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicCorpPageVO> getDicCorpPageVOList(IPage<DicCorpPageVO> page, QueryWrapper<DicCorpSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicCorpPageVOList(page, wrapper));
    }
}
