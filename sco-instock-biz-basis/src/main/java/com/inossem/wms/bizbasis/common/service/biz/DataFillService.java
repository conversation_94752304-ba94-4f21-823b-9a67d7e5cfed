package com.inossem.wms.bizbasis.common.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.datafill.DataFillServiceI;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.metadata.MetadataContext;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilMetadata;
import com.inossem.wms.common.util.UtilReflect;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/2/4 13:41
 */
@Slf4j
@Component
public class DataFillService implements DataFillServiceI {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 根据填充类型填充数据
     *
     * @param dataFillType 填充类型
     * @param dataList 要被填充的对象列表
     * @param <T>
     * <AUTHOR> <<EMAIL>>
     */
    @Override
    public <T extends Object> void fillType(EnumDataFillType dataFillType, List<T> dataList) {
        if (EnumDataFillType.FILL_ATTR.getIntValue().equals(dataFillType.getIntValue())) {
            this.fillAttr(dataList);
        } else if (EnumDataFillType.FILL_SON.getIntValue().equals(dataFillType.getIntValue())) {
            this.fillSonAttrDataList(dataList);
        } else if (EnumDataFillType.FILL_RLAT.getIntValue().equals(dataFillType.getIntValue())) {
            this.fillRlatAttrDataList(dataList);
        }
    }

    /**
     * 根据填充类型填充数据
     *
     * @param dataFillType 填充类型
     * @param dataObj 要被填充的对象列表
     * @param <T>
     * <AUTHOR> <<EMAIL>>
     */
    @Override
    public <T extends Object> void fillType(EnumDataFillType dataFillType, T... dataObj) {
        if (EnumDataFillType.FILL_ATTR.getIntValue().equals(dataFillType.getIntValue())) {
            this.fillAttr(dataObj);
        } else if (EnumDataFillType.FILL_SON.getIntValue().equals(dataFillType.getIntValue())) {
            this.fillSonAttrForDataObj(dataObj);
        } else if (EnumDataFillType.FILL_RLAT.getIntValue().equals(dataFillType.getIntValue())) {
            this.fillRlatAttrForDataObj(dataObj);
        }
    }


    /**
     * 填充数据对象的关联属性和父子属性
     *
     * @param dataObj 要被填充的对象
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillAttr(T dataObj) {
        long sysStartTimeStamp = System.currentTimeMillis();
        this.fillAttr(new LinkedList<T>(){{add(dataObj);}});
        log.info("填充完成，耗时："+(System.currentTimeMillis() - sysStartTimeStamp) + "毫秒");
    }

    /**
     * 填充数据对象的关联属性和父子属性（白名单-指定属性）
     *
     * @param dataObj 要被填充的对象
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillAttrWField(T dataObj, SFunction<T, ?>... functionArray) {
        this.fillAttrWField(new LinkedList<T>(){{add(dataObj);}}, functionArray);
    }

    /**
     * 填充数据对象的关联属性和父子属性（黑名单-忽略属性）
     *
     * @param dataObj 要被填充的对象
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillAttrBField(T dataObj, SFunction<T, ?>... functionArray) {
        this.fillAttrBField(new LinkedList<T>(){{add(dataObj);}}, functionArray);
    }

    /**
     * 填充数据对象的关联属性和父子属性
     *
     * @param dataList 要被填充的对象列表
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillAttr(List<T> dataList) {
        this.fillSonAttrDataList(dataList);
        this.fillRlatAttrDataList(dataList);
    }

    /**
     * 填充数据对象的关联属性和父子属性（白名单-指定属性）
     *
     * @param dataList 要被填充的对象列表
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillAttrWField(List<T> dataList, SFunction<T, ?>... functionArray) {
        this.fillSonAttrDataListWField(dataList, functionArray);
        this.fillRlatAttrDataListWField(dataList, functionArray);
    }

    /**
     * 填充数据对象的关联属性和父子属性（黑名单-忽略属性）
     *
     * @param dataList 要被填充的对象列表
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillAttrBField(List<T> dataList, SFunction<T, ?>... functionArray) {
        this.fillSonAttrDataListBField(dataList, functionArray);
        this.fillRlatAttrDataListBField(dataList, functionArray);
    }

    /**
     * 针对单个或多个数据对象的父子属性填充方法的封装
     *
     * @param dataObj
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillSonAttrForDataObj(T dataObj) {
        this.fillSonAttrDataList(new LinkedList<T>(){{add(dataObj);}});
    }

    /**
     * 针对单个数据对象的父子属性填充方法的封装（白名单-指定属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillSonAttrForDataObjWField(T dataObj, SFunction<T, ?>... functionArray) {
        this.fillSonAttrDataListWField(new LinkedList<T>(){{add(dataObj);}}, functionArray);
    }

    /**
     * 针对单个数据对象的父子属性填充方法的封装（黑名单-忽略属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillSonAttrForDataObjBField(T dataObj, SFunction<T, ?>... functionArray) {
        this.fillSonAttrDataListBField(new LinkedList<T>(){{add(dataObj);}}, functionArray);
    }

    /**
     * 填充父子字段数据列表
     *
     * @param dataList
     * @param <T>
     */
    @Override
    public <T> void fillSonAttrDataList(List<T> dataList) {
        if (UtilCollection.isEmpty(dataList)) {
            return;
        }

        // 反射出对象的所有属性
        List<Field> fieldList = UtilReflect.getAllFields(dataList.get(0).getClass());

        // 填充父子字段数据列表
        this.fillSonAttrDataList(dataList, fieldList);
    }

    /**
     * 填充父子字段数据列表（白名单-指定属性）
     *
     * @param dataList
     * @param fieldArray
     * @param <T>
     */
    @Override
    public <T> void fillSonAttrDataListWField(List<T> dataList, SFunction<T, ?>... fieldArray) {
        // 根据指定属性获取属性列表
        List<Field> fieldList = UtilReflect.getFieldList(fieldArray);

        // 填充父子字段数据列表
        this.fillSonAttrDataList(dataList, fieldList);
    }

    /**
     * 填充父子字段数据列表（黑名单-忽略属性）
     *
     * @param dataList
     * @param functionArray
     * @param <T>
     */
    @Override
    public <T> void fillSonAttrDataListBField(List<T> dataList, SFunction<T, ?>... functionArray) {

        if (UtilCollection.isEmpty(dataList)) {
            return;
        }

        // 取得排除忽略属性之外的属性列表
        List<Field> fieldList = this.getUnIgnoreFieldList(dataList.get(0).getClass(), functionArray);

        // 填充父子字段数据列表
        this.fillSonAttrDataList(dataList, fieldList);
    }

    /**
     * 针对单个数据对象的关联属性填充方法的封装
     *
     * @param dataObj
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillRlatAttrForDataObj(T dataObj) {
        this.fillRlatAttrDataList(new LinkedList<T>(){{add(dataObj);}});
    }

    /**
     * 针对单个数据对象的关联属性填充方法的封装（白名单-指定属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillRlatAttrForDataObjWField(T dataObj, SFunction<T, ?>... functionArray) {
        this.fillRlatAttrDataListWField(new LinkedList<T>(){{add(dataObj);}}, functionArray);
    }

    /**
     * 针对单个数据对象的关联属性填充方法的封装（黑名单-忽略属性）
     *
     * @param dataObj
     * @param functionArray
     * @param <T>
     * <AUTHOR>
     */
    @Override
    public <T> void fillRlatAttrForDataObjBField(T dataObj, SFunction<T, ?>... functionArray) {
        this.fillRlatAttrDataListBField(new LinkedList<T>(){{add(dataObj);}}, functionArray);
    }

    /**
     * 填充关联字段数据列表
     *
     * @param dataList
     * @param <T>
     */
    @Override
    public <T> void fillRlatAttrDataList(List<T> dataList) {
        if (UtilCollection.isEmpty(dataList)) {
            return;
        }

        // 反射出对象的所有属性
        List<Field> fieldList = UtilReflect.getAllFields(dataList.get(0).getClass());

        // 填充关联字段数据列表
        this.fillRlatAttrDataList(dataList, fieldList);
    }

    /**
     * 填充关联字段数据列表（白名单-指定属性）
     *
     * @param dataList
     * @param functionArray
     * @param <T>
     */
    @Override
    public <T> void fillRlatAttrDataListWField(List<T> dataList, SFunction<T, ?>... functionArray) {
        // 根据指定属性获取属性列表
        List<Field> fieldList = UtilReflect.getFieldList(functionArray);

        // 填充关联字段数据列表
        this.fillRlatAttrDataList(dataList, fieldList);
    }

    /**
     * 填充关联字段数据列表（黑名单-忽略属性）
     *
     * @param dataList
     * @param functionArray
     * @param <T>
     */
    @Override
    public <T> void fillRlatAttrDataListBField(List<T> dataList, SFunction<T, ?>... functionArray) {

        if (UtilCollection.isEmpty(dataList)) {
            return;
        }

        // 取得排除忽略属性之外的属性列表
        List<Field> fieldList = this.getUnIgnoreFieldList(dataList.get(0).getClass(), functionArray);

        // 填充关联字段数据列表
        this.fillRlatAttrDataList(dataList, fieldList);
    }

    /**
     * 填充父子字段数据列表
     *
     * @param dataList
     * @param <T>
     */
    private <T> void fillSonAttrDataList(List<T> dataList, List<Field> fieldList) {

        if (UtilCollection.isEmpty(dataList) || UtilCollection.isEmpty(fieldList)) {
            return;
        }

        // 遍历所有属性
        for (Field field : fieldList) {
            // 父子属性填充注解
            SonAttr sonAttr = field.getAnnotation(SonAttr.class);

            // 如果属性未标记父子属性注解，则不需要处理
            if (sonAttr == null) {
                continue;
            }

            // 子表表名
            String tableName = sonAttr.sonTbName();

            // 子表的mybatisPlusService
            IService mPlusService = MetadataContext.getMplusService(tableName);

            // 父id列表
            List idList = dataList.stream().filter(obj -> UtilReflect.getValueByField(sonAttr.mainTbPkAttrName(), obj) != null)
                    .map(obj -> UtilReflect.getValueByField(sonAttr.mainTbPkAttrName(), obj)).distinct().collect(Collectors.toList());

            if (UtilCollection.isEmpty(idList)) {
                continue;
            }

            QueryWrapper queryWrapper = new QueryWrapper();

            if(null == MetadataContext.getEntityClass(tableName)){
                throw new NullPointerException("没有" + tableName + "表");
            }
            queryWrapper.in(UtilMetadata.getColName(sonAttr.sonTbFkAttrName(), MetadataContext.getEntityClass(tableName)), idList);
            if (StringUtils.isNotEmpty(sonAttr.limitAttr())) {
                String[] attrAndValue = sonAttr.limitAttr().split("=");
                String attrName = attrAndValue[0];
                String attrValue = attrAndValue[1];
                String[] tableFieldValueArray = attrValue.split(",");
                List<T> tableDataList = dataList.stream()
                        .filter(obj -> Arrays.asList(tableFieldValueArray).contains(UtilReflect.getValueByField(attrName, obj) + ""))
                        .collect(Collectors.toList());
                if(UtilCollection.isEmpty(tableDataList)){
                    continue;
                }
            }

            // 其他子表属性限制条件
            if (sonAttr.sonTbFkOtherAttrLimit() != null && sonAttr.sonTbFkOtherAttrLimit()[0].length() > 0) {
                for (String attrPairs : sonAttr.sonTbFkOtherAttrLimit()) {
                    try {
                        String[] attrAndValue = attrPairs.split("=");
                        String attrName = attrAndValue[0];
                        String attrValue = attrAndValue[1];
                        if (!attrValue.contains(",")) {
                            queryWrapper.eq(UtilMetadata.getColName(attrName, MetadataContext.getEntityClass(tableName)), attrValue);
                        } else {
                            List<String> values = Stream.of(attrValue.split(",")).collect(Collectors.toList());
                            queryWrapper.in(UtilMetadata.getColName(attrName, MetadataContext.getEntityClass(tableName)), values);
                        }
                    } catch (RuntimeException re) {
                        throw new IllegalArgumentException("SonAttr注解sonTbFkOtherAttrLimit配置错误" + sonAttr.sonTbFkOtherAttrLimit());
                    }
                }
            }

            // 子数据列表
            List<Object> sonDataList = mPlusService.list(queryWrapper);

            // 子数据列表排序
            sonDataList =
                    sonDataList.stream().sorted(Comparator.comparing(obj -> UtilReflect.getValueByField("id", obj))).collect(Collectors.toList());

            // 子数据类型
            Class sonDataClazz = null;

            if (Collection.class.isAssignableFrom(field.getType())) {
                sonDataClazz = UtilMetadata.getParameterizedTypeClass((ParameterizedType)field.getGenericType(), 0);
            } else if (Object.class.isAssignableFrom(field.getType())) {
                sonDataClazz = field.getType();
            } else {
                System.out.println("父子属性填充出现异常数据=======》" + field.getType());
            }

            List<Object> sonDataVoList = UtilCollection.toList(sonDataList, sonDataClazz);

            // 子数据父id与子数据列表对应的Map
            Map<Long, List<Object>> sonDataMap =
                    sonDataVoList.stream().collect(Collectors.groupingBy(obj -> UtilReflect.getValueByField(sonAttr.sonTbFkAttrName(), obj)));

            // 遍历数据列表，填充子数据
            for (Object object : dataList) {
                // 从查询回的map中根据父id取出子数据
                List<Object> sonData = sonDataMap.get(UtilReflect.getValueByField(sonAttr.mainTbPkAttrName(), object));

                // list返回对象仅有一个则转换为对应的实体
                if (UtilCollection.isEmpty(sonData)) {
                    continue;
                }

                if (Collection.class.isAssignableFrom(field.getType())) {
                    UtilReflect.setValueByField(object, field, sonData);
                } else if (Object.class.isAssignableFrom(field.getType())) {
                    UtilReflect.setValueByField(object, field, sonData.get(0));
                }
            }

            // 递归填充父子属性
            this.fillSonAttrDataList(sonDataVoList);
        }
    }

    /**
     * 填充关联属性数据列表
     *
     * @param dataList 数据列表
     * @param fieldList 要填充的属性列表
     * @param <T>
     */
    private <T> void fillRlatAttrDataList(List<T> dataList, List<Field> fieldList){
        if (UtilCollection.isEmpty(dataList) || UtilCollection.isEmpty(fieldList)) {
            return;
        }

        // 遍历所有属性
        for (Field field : fieldList) {
            // 关联属性填充注解
            RlatAttr rlatAttr = field.getAnnotation(RlatAttr.class);

            // 如果属性未标记关联属性填充注解，则递归处理子属性
            if (rlatAttr == null) {
                // TODO 递归所有属性进行自动填充性能问题未验证 start
                List<Object> attrDataList = new LinkedList<>();
                dataList.stream().filter(obj -> UtilReflect.getValueByField(field.getName(), obj) != null).forEach(obj -> {
                    if (Collection.class.isAssignableFrom(field.getType())) {
                        attrDataList.addAll(UtilReflect.getValueByField(field.getName(), obj));
                    } else if (Object.class.isAssignableFrom(field.getType())) {
                        attrDataList.add(UtilReflect.getValueByField(field.getName(), obj));
                    }
                });

                // 递归处理子属性数据
                this.fillRlatAttrDataList(attrDataList);
                // TODO 递归所有属性进行自动填充性能问题未验证 end

                continue;
            }

            // 关联表表名
            String[] tables = rlatAttr.rlatTableName();

            // 如果是单表则直接处理，如果是多表，需要解析表达式筛选数据之后分别处理
            if (tables.length == 1) {
                this.fillRlatAttrDataList(dataList, field, rlatAttr, tables[0]);
            } else {
                for (String tableExps : tables) {
                    String tableName = tableExps.split(":")[0];
                    String tableFieldName = tableExps.split(":")[1].split("=")[0];
                    String tableFieldValue = tableExps.split(":")[1].split("=")[1];
                    String[] tableFieldValueArray = tableFieldValue.split(",");
                    List<T> tableDataList = dataList.stream()
                            .filter(obj -> Arrays.asList(tableFieldValueArray).contains(UtilReflect.getValueByField(tableFieldName, obj) + ""))
                            .collect(Collectors.toList());
                    this.fillRlatAttrDataList(tableDataList, field, rlatAttr, tableName);
                }
            }
        }
    }

    /**
     * 填充关联属性数据列表
     *
     * @param dataList 数据列表
     * @param field 要填充的属性
     * @param rlatAttrFill 关联属性信息
     * @param tableName 关联表表名
     * @param <T>
     */
    private <T> void fillRlatAttrDataList(List<T> dataList, Field field, RlatAttr rlatAttrFill, String tableName) {

        if (UtilCollection.isEmpty(dataList)) {
            return;
        }

        // 关联表的mybatisPlusService
        BaseDataWrap mPlusService = MetadataContext.getMplusService(tableName);

        // 关联id列表
        List idList = dataList.stream().filter(obj -> UtilReflect.getValueByField(field.getName(), obj) != null)
                .map(obj -> UtilReflect.getValueByField(field.getName(), obj)).distinct().collect(Collectors.toList());

        if (UtilCollection.isEmpty(idList)) {
            return;
        }

        // 关联数据
        List<Object> relationDataList;
        switch (tableName) {
            case Const.CACHE_DIC_MATERIAL:
                relationDataList = (List<Object>) dictionaryService.getMatListCacheByMatIdList(idList);
                break;
            case Const.CACHE_DIC_UNIT:
                relationDataList = (List<Object>) dictionaryService.getUnitCacheByIds(idList);
                break;
            case Const.CACHE_DIC_FACTORY:
                relationDataList = (List<Object>) dictionaryService.getFtyCacheByIds(idList);
                break;
            case Const.CACHE_DIC_STOCK_LOCATION:
                relationDataList = (List<Object>) dictionaryService.getLocationCacheByIds(idList);
                break;
            case Const.CACHE_DIC_WH:
                relationDataList = (List<Object>) dictionaryService.getWhCacheByIds(idList);
                break;
            case Const.CACHE_DIC_WH_STORAGE_TYPE:
                relationDataList = (List<Object>) dictionaryService.getStorageTypeCacheByIds(idList);
                break;
            case Const.CACHE_DIC_WH_STORAGE_SECTION:
                relationDataList = (List<Object>) dictionaryService.getStorageSectionCacheByIds(idList);
                break;
            case Const.CACHE_DIC_WH_STORAGE_BIN:
                relationDataList = (List<Object>) dictionaryService.getBinCacheByIds(idList);
                break;
            case Const.CACHE_SYS_USER:
                relationDataList = (List<Object>) dictionaryService.getSysUserCacheByIds(idList);
                break;
            case Const.CACHE_DIC_PURCHASE_PACKAGE:
                relationDataList = (List<Object>) dictionaryService.getPurchasePackageCacheByIds(idList);
                break;
            case Const.CACHE_DIC_MARGIN_CATEGORY:
                relationDataList = (List<Object>) dictionaryService.getMarginCategoryCacheByIds(idList);
                break;
            case Const.CACHE_MATERIAL_CGN:
                relationDataList = (List<Object>) dictionaryService.getMaterialCgnCacheByIds(idList);
                break;
            default:
                relationDataList = mPlusService.selectByIdsIgnoreDelete(idList);
                break;
        }
        // 关联id与关联数据对应的Map
        Map<Long, Object> relationDataMap = new HashMap<>();
        if(UtilCollection.isNotEmpty(relationDataList)){
            relationDataMap = relationDataList.stream().collect(Collectors.toMap(obj -> UtilReflect.getValueByField("id", obj), obj -> obj,(v1, v2)->v1));
        }

        // 关联值是整个对象填充的对象列表(准备收集起来进行二次填充),处理注解中sourceAttr设置为[*]的情况
        List<Object> rlatValueObjList = new LinkedList<>();

        // 遍历数据列表，填充关联数据
        for (Object object : dataList) {
            // 从查询回的map中根据关联id取出关联数据
            Object relationData = relationDataMap.get(UtilReflect.getValueByField(field.getName(), object));

            if (relationData == null) {
                continue;
            }

            // 源属性集合与目标属性集合
            String[] sourceAttrList = rlatAttrFill.sourceAttrName().split(",");
            String[] targetAttrList = rlatAttrFill.targetAttrName().split(",");

            for (int i = 0; i < sourceAttrList.length; i++) {
                // 要填充的值对象
                Object fillValueObj;

                // 如果是直接填充对象,则需要把查询出来的entity转成dto对象
                if ("*".equals(sourceAttrList[i])) {
                    // 查询出来的entity对象
                    fillValueObj = relationData;

                    // 目标属性的class(DTO的class)
                    Class targetAttrClass = UtilReflect.getField(targetAttrList[i], object.getClass()).getType();

                    // entity对象转为dto对象
                    fillValueObj = UtilBean.newInstance(fillValueObj, targetAttrClass);

                    // 收集dto对象进行二次填充
                    rlatValueObjList.add(fillValueObj);
                } else {
                    fillValueObj = UtilReflect.getValueByFieldNullReturnNull(sourceAttrList[i], relationData);
                }
                if (null != fillValueObj) {
                    UtilReflect.setValueByField(object, targetAttrList[i], fillValueObj);
                }
            }
        }

        // 如果dto对象列表有值,则递归填充
        if (UtilCollection.isNotEmpty(rlatValueObjList)) {
            this.fillAttr(rlatValueObjList);
        }
    }

    /**
     * 取得排除忽略属性之外的属性列表
     * 反射出该class的所有属性，排除掉忽略属性函数数组中指定的属性之后返回剩余的属性
     *
     * @param clazz 要获取属性的类
     * @param ignoreFieldFunctionArray 忽略属性函数数组
     * @return
     */
    private List<Field> getUnIgnoreFieldList(Class clazz, SFunction<?, ?>... ignoreFieldFunctionArray) {

        // 要忽略的属性列表
        List<Field> ignoreFieldList = UtilReflect.getFieldList(ignoreFieldFunctionArray);

        List<String> ignoreFieldNameList = UtilCollection.isEmpty(ignoreFieldList) ? new LinkedList<>() : ignoreFieldList.stream().map(Field::getName).collect(Collectors.toList());

        // 反射出对象的所有属性
        List<Field> fieldList = UtilReflect.getAllFields(clazz);

        // 排除要忽略的属性
        fieldList = fieldList.stream().filter(obj -> !ignoreFieldNameList.contains(obj.getName())).collect(Collectors.toList());

        return fieldList;
    }
}
