package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.org.factory.entity.DicFactory;
import com.inossem.wms.common.model.org.factory.po.DicFactorySearchPO;
import com.inossem.wms.common.model.org.factory.vo.DicFactoryPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicFactoryMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工厂主数据表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class DicFactoryDataWrap extends BaseDataWrap<DicFactoryMapper, DicFactory> {

    /**
     * 工厂列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicFactoryPageVO> getDicFactoryPageVOList(IPage<DicFactoryPageVO> page, QueryWrapper<DicFactorySearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicFactoryPageVOList(page, wrapper));
    }
}
