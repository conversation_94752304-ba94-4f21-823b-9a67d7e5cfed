package com.inossem.wms.bizbasis.sap.restful.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.masterdata.asset.service.datawrap.DicAssetDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialGroupDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialTypeDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicUnitDataWrap;
import com.inossem.wms.bizbasis.masterdata.wbs.service.datawrap.DicWbsDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.sap.HXSapConst;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.apply.EnumReceiveType;
import com.inossem.wms.common.enums.sap.EnumPurchaseOrderType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.masterdata.asset.entity.DicAsset;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.base.entity.DicMaterialGroup;
import com.inossem.wms.common.model.masterdata.mat.base.entity.DicMaterialType;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.supplier.dto.DicSupplierDTO;
import com.inossem.wms.common.model.masterdata.wbs.entity.DicWbs;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.model.sap.purchase.*;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.sap.SapApiCallProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 华信SAP接口服务类
 * 负责与SAP系统进行数据交互，包括物料、供应商、WBS、资产等主数据同步
 * 
 * 主要功能：
 * 1. 供应商信息同步 ok
 * 2. WBS信息同步
 * 3. 资产卡片信息同步
 * 4. 物料主数据同步 ok
 * 5. 物料凭证过账
 * 6. 采购订单同步
 * 7. 采购订单删除同步
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
@Slf4j
public class HXSapIntegerfaceService {

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected SapApiCallProxy sapApiCallProxy;

    @Autowired
    protected DicWbsDataWrap dicWbsDataWrap;

    @Autowired
    protected DicAssetDataWrap dicAssetDataWrap;

    @Autowired
    protected DicMaterialDataWrap dicMaterialDataWrap;

    @Autowired
    protected DicUnitDataWrap dicUnitDataWrap;

    @Autowired
    protected DicMaterialGroupDataWrap dicMaterialGroupDataWrap;

    @Autowired
    protected DicMaterialTypeDataWrap dicMaterialTypeDataWrap;

    @Autowired
    protected EditCacheService editCacheService;


    /**
     * 调用SAP接口，签名转换
     */
    private JsonObject callHXSapAPI(String erpUrl, JsonObject jsonObject) {
        try {
            JsonObject params = buildRequestParams(jsonObject);
            return sapApiCallProxy.callHXSapApi(erpUrl, params);
        } catch (Exception e) {
            log.error("调用SAP接口失败，错误信息：{}", e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, e.getMessage());
        }
    }

    /**
     * 获取SAP接口调用的通用请求头参数(后来接口又改了，暂时无意义，后续有公共参数可以使用)
     * 包含接口调用的基础信息：UUID、时间戳、接口名称、系统标识
     * 
     * @return JsonObject 包含通用参数的JSON对象
     */
    private JsonObject getHeadParams(String receiptId, String receiptCode, String receiptType) {
        return Optional.of(new JsonObject())
            .map(params -> {
                // params.addProperty("UUID", UUID.randomUUID().toString());
                // params.addProperty("TIMESTAMP", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                // params.addProperty("IFNAME", "WMS002");
                    // params.addProperty("SYS_ID", "WMS");
                
                // SAP日志所需参数
                JsonObject receiptParams = new JsonObject();
                receiptParams.addProperty("receiptId", receiptId);
                receiptParams.addProperty("receiptCode", receiptCode);
                receiptParams.addProperty("receiptType", receiptType);
                receiptParams.addProperty("interfaceType", "10");
                BizCommonService.setReceiptParam(params, receiptParams);
                return params;
            })
            .orElse(new JsonObject());
    }

    /**
     * 统一处理SAP返回结果
     * 检查接口调用状态，非成功状态抛出异常
     *
     * @param response SAP接口返回的JSON响应
     * @param operationType 操作类型，用于日志记录
     * @throws WmsException 当接口调用失败时抛出异常
     */
    private void handleSapResponse(JsonObject response, String operationType) {
        if (response.get("success").getAsBoolean()) {
            JsonObject data = response.get("data").getAsJsonObject();
            JsonObject returnObject = new JsonObject();
            if (data.has("E_RETURN")) {
                returnObject = data.get("E_RETURN").getAsJsonObject();
            } else if(data.has("RETURN")){
                // 供应商接口特殊， 不知道为啥返了个集合
                if(data.get("RETURN") instanceof JsonObject){
                    returnObject = data.get("RETURN").getAsJsonObject();
                } else if(data.get("RETURN") instanceof JsonArray) {
                    JsonArray jsonArray = data.get("RETURN").getAsJsonArray();
                    if (jsonArray.isJsonNull() || jsonArray.size() == 0) {
                        returnObject.addProperty("TYPE", "E");
                        returnObject.addProperty("MESSAGE", "SAP接口返回数据格式错误");
                    } else {
                        returnObject = jsonArray.get(0).getAsJsonObject();
                    }
                } else {
                    returnObject.addProperty("TYPE", "E");
                    returnObject.addProperty("MESSAGE", "SAP接口返回数据格式错误");
                }
            } else {
                returnObject.addProperty("TYPE", "E");
                returnObject.addProperty("MESSAGE", "SAP接口返回数据格式错误");
            }
            response.addProperty("TYPE",UtilObject.getStringOrEmpty(returnObject.get("TYPE")));

            if (operationType.equals("物料凭证过账") || operationType.equals("发票预制")) {
                // 过账不统一抛出异常  由各个业务自己处理
                return;
            }
            if (!Const.ERP_RETURN_TYPE_S.equals(returnObject.get("TYPE").getAsString())) {
                String errorMsg = UtilObject.getStringOrEmpty(returnObject.get("MESSAGE").getAsString());
                log.error("SAP接口调用失败 - 操作类型: {}, 错误信息: {}", operationType, errorMsg);
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, errorMsg);
            }
            
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, response.get("message").getAsString()==null?"调用失败，SAP返回信息为空":response.get("message").getAsString());
        }
    }


    /**************************************************** 供应商信息同步 ****************************************************/
    /**
     * 同步供应商信息到SAP
     *
     * @param dicSupplierDTO 供应商信息DTO
     * @throws WmsException 接口调用失败时抛出异常
     */
    public void synSupplierInfo(DicSupplierDTO dicSupplierDTO) {


        log.info("开始同步供应商信息到SAP, 供应商编码: {}", dicSupplierDTO.getSupplierCode());
        
        JsonObject params = getHeadParams(dicSupplierDTO.getId().toString(), dicSupplierDTO.getSupplierCode(), EnumReceiptType.SYN_SUPPLIER.getValue().toString());
        buildSupplierParams(params, dicSupplierDTO);
        String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.SUPPLIER_INFO;
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);

        checkSupplierResponse(returnObject);

        handleSapResponse(returnObject, "供应商信息同步");
        
        log.info("供应商信息同步成功, 供应商编码: {}", dicSupplierDTO.getSupplierCode());

    }

    private void checkSupplierResponse(JsonObject returnObject) {
        // 根目录下存在 success, message , data 
        if (returnObject.has("success") && returnObject.has("message") && returnObject.has("data")) {
            return;
        }

        throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, "SAP接口返回格式错误");
    }

    /**
     * 构建供应商同步参数
     */
    private void buildSupplierParams(JsonObject params, DicSupplierDTO dicSupplierDTO) {
        JsonObject dataJsonObject = new JsonObject();

        dataJsonObject.addProperty("BUKRS", "1104"); // 公司代码 华信1104
        dataJsonObject.addProperty("EKORG", "1104"); // 采购组织 默认1104
        dataJsonObject.addProperty("KTOKK", "Y100"); // 采购组 固定“Y100”YEC一般供应商
        dataJsonObject.addProperty("NAME1", dicSupplierDTO.getSupplierName()); // 供应商名称
        dataJsonObject.addProperty("SORT1", ""); // 供应商类型
        dataJsonObject.addProperty("STREET", dicSupplierDTO.getAddress()); // 详细地址 供应商地址信息
        dataJsonObject.addProperty("POST_CODE1", "000000"); // 邮政编码 固定10010
        dataJsonObject.addProperty("CITY1", dicSupplierDTO.getArea()); // 省份
        dataJsonObject.addProperty("SPRAS", "EN"); // 语言 默认传“EN”英文
        dataJsonObject.addProperty("AKONT", "2202000000"); // [30756]【供应商管理】入参AKONT固定传2202000000
        dataJsonObject.addProperty("WAERS", ""); // 订单币种 传空
        dataJsonObject.addProperty("LIFNR", dicSupplierDTO.getSupplierCode()); // 供应商编码
        dataJsonObject.addProperty("COUNTRY", dicSupplierDTO.getCountry()); // 国家
        dataJsonObject.addProperty("STCEG", dicSupplierDTO.getCreditCode()); // 税号
        dataJsonObject.addProperty("STENR", dicSupplierDTO.getCreditCode()); // 税号
        // dataJsonObject.addProperty("SPERM1", "X"); // 对公司代码过帐冻结

        // 下面的字段是最初接口文档中的字段，基于最新接口文档以舍弃， 注释留作备用
        // dataJsonObject.addProperty("TYPE", "2"); // 合作伙伴类别 固定“2”组织
        // params.addProperty("RLTYP", "FLYV00"); // 业务代表类别
        // dataJsonObject.addProperty("BU_GROUP", "Y100"); // 分组 固定“Y100”YEC一般供应商
        // dataJsonObject.addProperty("MOB_NUMBER", dicSupplierDTO.getUserList().get(0).getPhoneNumber()); // 联系电话
        // dataJsonObject.addProperty("SMTP_ADDR", dicSupplierDTO.getUserList().get(0).getEmail()); // 企业邮箱
        // dataJsonObject.addProperty("TAXTYPE", ""); // 税号类别 传空
        // dataJsonObject.addProperty("XBLCK", "空"); // 供应商状态 冻结状态X，启用状态“空”
        // dataJsonObject.addProperty("ZTERM", ""); // 付款条件 传空
        // dataJsonObject.addProperty("REPRF", "X"); // 检查重复发票校验 默认“X”
        // dataJsonObject.addProperty("EKGRP", "110"); // 采购组 默认110
        // dataJsonObject.addProperty("WEBRE", "X"); // 基于收货的发票校验 默认“X”
        

        params.add("I_VENDOR", dataJsonObject);
        
    }


    /**************************************************** WBS信息同步 ****************************************************/
    /**
     * 同步WBS信息
     * 从SAP系统获取最新的WBS信息并更新到WMS系统
     *
     * @throws WmsException 当接口调用失败或数据处理异常时抛出
     */
    public void synWBSInfo(Date syncStartTime, Date syncEndTime) {
        log.info("开始同步WBS信息");
        
        JsonObject params = getHeadParams("WBS同步", "WBS同步", EnumReceiptType.SYN_WBS.getValue().toString());
        buildWbsParams(params, syncStartTime, syncEndTime);

        String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.WBS_INFO;
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);

        checkWbsResponse(returnObject);

        handleSapResponse(returnObject, "WBS信息同步");

        List<DicWbs> dicWbsList = parseWbsResponse(returnObject.get("data").getAsJsonObject().get("E_WBS").getAsJsonArray());
                
        if(CollectionUtils.isNotEmpty(dicWbsList)) {
            dicWbsDataWrap.saveOrUpdateBatch(dicWbsList);
            log.info("WBS信息同步成功, 同步数量: {}", dicWbsList.size());
        }

    }

    /**
     * 构建WBS同步参数
     */
    private void buildWbsParams(JsonObject params, Date syncStartTime, Date syncEndTime) {
        // 同步时间处理
        String startDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String endDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        if (UtilObject.isNotNull(syncStartTime)) {
            startDate = new SimpleDateFormat("yyyyMMdd").format(syncStartTime);
        }
        if (UtilObject.isNotNull(syncEndTime)) {
            endDate = new SimpleDateFormat("yyyyMMdd").format(syncEndTime);
        }

        params.addProperty("I_BUKRS", "1104");

        // 构建日期范围参数
        JsonArray dateRangeArray = new JsonArray();
        JsonObject dateRange = new JsonObject();
        dateRange.addProperty("SIGN", "I");
        dateRange.addProperty("OPTION", "BT"); 
        dateRange.addProperty("LOW", startDate);
        dateRange.addProperty("HIGH", endDate);
        dateRangeArray.add(dateRange);

        params.add("I_DATE_RANGE", dateRangeArray);

    }

    private void checkWbsResponse(JsonObject returnObject) {
        // 根目录下存在 success, message , data ,data 下存在 E_WBS 否测抛出异常
        if (returnObject.has("success") && returnObject.has("message") && returnObject.has("data")) {
            if (returnObject.get("data").getAsJsonObject().has("E_WBS")) {
                return;
            }
        }

        throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, "SAP接口返回格式错误");
    }
    
    /**
     * 解析WBS返回数据
     */
    private List<DicWbs> parseWbsResponse(JsonArray dataList) {
        // SAP同步过来的WBS数据集合
        List<DicWbs> dicWbsList = new ArrayList<>();
        
        if (dataList != null) {
            for (int i = 0; i < dataList.size(); i++) {
                JsonObject item = dataList.get(i).getAsJsonObject();
                DicWbs dicWbs = new DicWbs();
                
                // 设置WBS编码和名称
                dicWbs.setWbsCode(item.get("POSID").getAsString());
                dicWbs.setWbsName(item.get("POST1").getAsString());
                // 设置地点
                dicWbs.setLocation(item.get("STORT").getAsString());
                
                // 设置创建和修改时间
                dicWbs.setCreateTime(new Date());
                dicWbs.setModifyTime(new Date());
                
                dicWbsList.add(dicWbs);
            }
        }

        // 待保存的WBS数据集合
        List<DicWbs> wbsSaveList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(dicWbsList)) {
            // 获取所有WBS编码
            List<String> wbsCodeList = dicWbsList.stream()
                .map(DicWbs::getWbsCode)
                .collect(Collectors.toList());

            // 一次性查询所有已存在的WBS数据
            QueryWrapper<DicWbs> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(DicWbs::getWbsCode, wbsCodeList);
            List<DicWbs> oldWbsList = dicWbsDataWrap.list(queryWrapper);

            // 构建已存在WBS的Map，key为wbsCode
            Map<String, DicWbs> oldWbsMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oldWbsList)) {
                oldWbsList.forEach(wbs -> 
                    oldWbsMap.put(wbs.getWbsCode(), wbs)
                );
            }

            // 处理每个WBS数据
            dicWbsList.forEach(wbs -> {
                DicWbs oldWbs = oldWbsMap.get(wbs.getWbsCode());

                if (oldWbs != null) {
                    // 更新已存在的WBS数据
                    wbs.setId(oldWbs.getId());
                    wbs.setCreateTime(oldWbs.getCreateTime());
                    wbs.setCreateUser(oldWbs.getCreateUser());
                }
                wbsSaveList.add(wbs);
            });
        }

        return wbsSaveList;
    }

    /**************************************************** 资产卡片信息同步 ****************************************************/
    /**
     * 同步资产卡片信息
     *
     * @throws WmsException 接口调用失败时抛出异常
     */
    public void synAssetCardInfo(Date syncStartTime, Date syncEndTime) {
        JsonObject params = getHeadParams("资产卡片同步", "资产卡片同步", EnumReceiptType.SYN_ASSET_CARD.getValue().toString());
        buildAssetCardParams(params, syncStartTime, syncEndTime);

        String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.ASSET_CARD_INFO;
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);

        checkAssetCardResponse(returnObject);
        handleSapResponse(returnObject, "资产卡片信息同步");

        List<DicAsset> dicAssetCardList = parseAssetCardResponse(returnObject.get("data").getAsJsonObject().get("ET_OUT").getAsJsonArray());

        if(CollectionUtils.isNotEmpty(dicAssetCardList)) {
            dicAssetDataWrap.saveOrUpdateBatch(dicAssetCardList);
            log.info("资产卡片信息同步成功, 同步数量: {}", dicAssetCardList.size());
        }

    }

    /**
     * 构建资产卡片同步参数
     */
    private void buildAssetCardParams(JsonObject params, Date syncStartTime, Date syncEndTime) {
        // 同步时间处理
        String startDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String endDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        if (UtilObject.isNotNull(syncStartTime)) {
            startDate = new SimpleDateFormat("yyyyMMdd").format(syncStartTime);
        }
        if (UtilObject.isNotNull(syncEndTime)) {
            endDate = new SimpleDateFormat("yyyyMMdd").format(syncEndTime);
        }

        params.addProperty("I_BUKRS", "1104");

        // 构建日期范围参数
        JsonArray dateRangeArray = new JsonArray();
        JsonObject dateRange = new JsonObject();
        dateRange.addProperty("SIGN", "I");
        dateRange.addProperty("OPTION", "BT");
        dateRange.addProperty("LOW", startDate);
        dateRange.addProperty("HIGH", endDate);
        dateRangeArray.add(dateRange);

        params.add("IT_ERSDA_RANGE", dateRangeArray);
    }

    private void checkAssetCardResponse(JsonObject returnObject) {
        // 根目录下存在 success, message , data ,data 下存在 ET_ROWS 否测抛出异常
        if (returnObject.has("success") && returnObject.has("message") && returnObject.has("data")) {
            if (returnObject.get("data").getAsJsonObject().has("ET_OUT")) {
                return;
            }
        }
    }

    /**
     * 解析资产卡片返回数据
     */
    private List<DicAsset> parseAssetCardResponse(JsonArray dataList) {
        // SAP同步过来的资产数据集合
        List<DicAsset> dicAssetCardList = new ArrayList<>();
        
        for(int i = 0; i < dataList.size(); i++) {
            JsonObject data = dataList.get(i).getAsJsonObject();
            DicAsset dicAssetCard = new DicAsset();
            
            // 设置资产编码和子编号
            dicAssetCard.setAssetCode(data.get("ANLN1").getAsString().replaceFirst("^0+(?!$)", "")); 
            dicAssetCard.setAssetSubCode(data.get("ANLN2").getAsString()); 
            
            // 设置资产类型
            dicAssetCard.setAssetType(data.get("ANLKL").getAsString().replaceFirst("^0+(?!$)", "")); 
            
            // 设置资产名称
            dicAssetCard.setAssetName(data.get("TXT50").getAsString());
            
            // 设置创建和修改时间
            dicAssetCard.setCreateTime(new Date());
            dicAssetCard.setModifyTime(new Date());
            
            dicAssetCardList.add(dicAssetCard);
        }

        // 待保存的资产数据集合
        List<DicAsset> assetSaveList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(dicAssetCardList)) {
            // 构建联合主键条件列表
            List<Map<String, String>> conditions = dicAssetCardList.stream()
                .map(asset -> {
                    Map<String, String> condition = new HashMap<>();
                    condition.put("asset_code", asset.getAssetCode());
                    condition.put("asset_sub_code", asset.getAssetSubCode());
                    return condition;
                })
                .collect(Collectors.toList());

            // 一次性查询所有已存在的资产数据
            List<DicAsset> oldAssetList = dicAssetDataWrap.listByMaps(conditions);

            // 构建已存在资产的Map，key为assetCode+assetSubCode
            Map<String, DicAsset> oldAssetMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oldAssetList)) {
                oldAssetList.forEach(asset -> 
                    oldAssetMap.put(asset.getAssetCode() + "_" + asset.getAssetSubCode(), asset)
                );
            }

            // 处理每个资产数据
            dicAssetCardList.forEach(asset -> {
                String key = asset.getAssetCode() + "_" + asset.getAssetSubCode();
                DicAsset oldAsset = oldAssetMap.get(key);

                if (oldAsset != null) {
                    // 更新已存在的资产数据
                    asset.setId(oldAsset.getId());
                    asset.setCreateTime(oldAsset.getCreateTime());
                    asset.setCreateUser(oldAsset.getCreateUser());
                }
                assetSaveList.add(asset);
            });
        }

        return assetSaveList;
    }

    /**************************************************** 物料信息同步 ****************************************************/
    /**
     * 同步物料信息
     * 从SAP系统获取物料主数据并更新到WMS系统
     * 支持增量同步和全量同步
     *
     * @param matCodeList 物料编码列表，为空时执行全量同步
     * @param syncStartTime 同步开始时间，用于增量同步
     * @param syncEndTime 同步结束时间，用于增量同步
     * @throws WmsException 当接口调用失败或数据处理异常时抛出
     */
    public void synMaterialInfo(List<String> matCodeList, Date syncStartTime, Date syncEndTime) {
        JsonObject params = getHeadParams("物料同步", "物料同步", EnumReceiptType.SYN_MATERIAL.getValue().toString());
        buildMaterialParams(params, matCodeList, syncStartTime, syncEndTime);

        // 发起接口调用
        String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.SYN_MATERIAL;
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);

        checkMaterialResponse(returnObject);

        handleSapResponse(returnObject, "物料信息同步");

        // JsonArray dataList = formatMaterialResponse(returnObject.getJsonObject("data").getJsonArray("ET_ROWS"));

        List<DicMaterialDTO> materialSaveList = parseMaterialResponse(returnObject.get("data").getAsJsonObject().get("ET_ROWS").getAsJsonArray());
        
        if (materialSaveList.size() > 0) {
            // 保存物料
            dicMaterialDataWrap.saveOrUpdateBatchDto(materialSaveList);
            // 刷新缓存
            editCacheService.refreshMatCacheByMatIdList(materialSaveList.stream().map(p -> p.getId()).collect(Collectors.toList()));
        }
        
    }
    
    /**
     * 构建物料同步参数
     */
    private void buildMaterialParams(JsonObject params, List<String> matCodeList, Date syncStartTime, Date syncEndTime) {

        // 同步时间处理
        String startDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String endDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        if (UtilObject.isNotNull(syncStartTime)) {
            startDate = new SimpleDateFormat("yyyyMMdd").format(syncStartTime);
        }
        if (UtilObject.isNotNull(syncEndTime)) {
            endDate = new SimpleDateFormat("yyyyMMdd").format(syncEndTime);
        }

        // 物料创建日期范围
        JsonArray ersdaRange = new JsonArray();
        JsonObject ersdaRangeItem = new JsonObject();
        ersdaRangeItem.addProperty("SIGN", "I");
        ersdaRangeItem.addProperty("OPTION", "BT"); 
        ersdaRangeItem.addProperty("LOW", startDate);
        ersdaRangeItem.addProperty("HIGH", endDate);
        ersdaRange.add(ersdaRangeItem);
        params.add("IT_ERSDA_RANGE", ersdaRange);
 
        // 指定物料同步，支持批量
        if (UtilCollection.isNotEmpty(matCodeList)) {
            JsonArray matRange = new JsonArray();
            for (String matCode : matCodeList) {
                JsonObject matRangeItem = new JsonObject();
                matRangeItem.addProperty("SIGN", "I");
                matRangeItem.addProperty("OPTION", "EQ");
                matRangeItem.addProperty("LOW", matCode);
                matRange.add(matRangeItem);
            }
            params.add("IT_MATNR_RANGE", matRange);
        }
        
        // 工厂范围固定入参
        JsonArray plantRange = new JsonArray();
        JsonObject plantRangeItem = new JsonObject();
        plantRangeItem.addProperty("SIGN", "I"); // 固定值 I
        plantRangeItem.addProperty("OPTION", "EQ"); // 固定值 EQ
        plantRangeItem.addProperty("LOW", "1104"); // 固定工厂编码 1104
        plantRange.add(plantRangeItem);
        params.add("IT_PLANT_RANGE", plantRange);
        
    }   

    /**
     * 校验物料同步返回数据
     * @param dataList
     * @return
     */
    private void checkMaterialResponse(JsonObject returnObject) {
        // 根目录下存在 success, message , data ,data 下存在 ET_ROWS 否测抛出异常
        if (returnObject.has("success") && returnObject.has("message") && returnObject.has("data")) {
            if (returnObject.get("data").getAsJsonObject().has("ET_ROWS")) {
                return;
            }
        }

        throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, "SAP接口返回格式错误");
    }

    /**
     * 解析物料返回数据
     */
    private List<DicMaterialDTO> parseMaterialResponse(JsonArray dataList) {
        // SAP同步过来的物料数据集合
        List<DicMaterialDTO> materialDTOList = new ArrayList<>();

        // 计量单位
        Set<DicUnit> unitSet = new HashSet<>();
        Map<String, Long> unitCodeMap = new HashMap<>();

        // 物料组
        Set<DicMaterialGroup> materialGroupSet = new HashSet<>();
        Map<String, Long> groupCodeMap = new HashMap<>();

        // 物料类型
        Set<DicMaterialType> materialTypeSet = new HashSet<>();
        Map<String, Long> typeCodeMap = new HashMap<>();

        for (int i = 0; i < dataList.size(); i++) {
            JsonObject maraObj = dataList.get(i).getAsJsonObject();

            // 物料主数据保存/更新
            DicMaterialDTO materialDTO = new DicMaterialDTO();
            materialDTO.setMatCode(maraObj.get("MATNR").getAsString()); // 物料编码
            materialDTO.setMatName(maraObj.get("MATL_DESC").getAsString()); // 物料描述（短文本）
            materialDTO.setMatNameEn(maraObj.get("MATL_DESC_E").getAsString()); // 物料描述（英文）

            materialDTO.setUnitCode(maraObj.get("MEINS").getAsString()); // 计量单位编码
            materialDTO.setUnitName(maraObj.get("MEINS_OUT").getAsString()); // 计量单位描述

            materialDTO.setMatGroupCode(maraObj.get("MATKL").getAsString()); // 物料组编码
            materialDTO.setMatGroupName(maraObj.get("WGBEZ").getAsString()); // 物料组描述

            materialDTO.setMatTypeCode(maraObj.get("MTART").getAsString()); // 物料类型编码
            // materialDTO.setMatTypeName(maraObj.getString("MTBEZ")); // 物料类型描述

            materialDTO.setExtBasicMaterial(maraObj.get("BISMT").getAsString()); // 旧物料号

            materialDTO.setExtManufacturerPartNumber(maraObj.get("MFRPN").getAsString()); // 制造商零件编号
            materialDTO.setExtMainMaterial(maraObj.get("WRKST").getAsString()); // 旧物料号
            materialDTO.setExtIndustryStandardDesc(maraObj.get("NORMT").getAsString()); // 行业标准描述

            materialDTOList.add(materialDTO);

            // 计量单位
            String meins = maraObj.get("MEINS").getAsString();
            if (UtilString.isNotNullOrEmpty(meins) && UtilNumber.isEmpty(getUnitId(unitCodeMap, meins))) {
                // 缓存中不存在此计量单位数据，需要新增
                DicUnit unit = new DicUnit();
                unit.setUnitCode(meins); // 计量单位编码
                unit.setUnitName(maraObj.get("MEINS_OUT").getAsString()); // 计量单位描述
                unitSet.add(unit);
            }
            // 物料组
            String matkl = maraObj.get("MATKL").getAsString();
            if (UtilString.isNotNullOrEmpty(matkl) && UtilNumber.isEmpty(getMaraGroupId(groupCodeMap, matkl))) {
                // 缓存中不存在此物料组数据，需要新增
                DicMaterialGroup materialGroup = new DicMaterialGroup();
                materialGroup.setMatGroupCode(maraObj.get("MATKL").getAsString()); // 物料组编码
                materialGroup.setMatGroupName(maraObj.get("WGBEZ").getAsString()); // 物料组描述
                materialGroupSet.add(materialGroup);
            }
            // 物料类型
            String mtart = maraObj.get("MTART").getAsString();
            if (UtilString.isNotNullOrEmpty(mtart) && UtilNumber.isEmpty(getMaraTypeId(typeCodeMap, mtart))) {
                // 缓存中不存在此物料类型数据，需要新增
                DicMaterialType materialType = new DicMaterialType();
                materialType.setMatTypeCode(maraObj.get("MTART").getAsString()); // 物料类型编码
                // materialType.setMatTypeName(maraObj.getString("MTBEZ")); // 物料类型描述
                materialTypeSet.add(materialType);
            }
        } 

        // 在更新物料主数据、工厂物料主数据、项目工厂物料主数据之前，先对基础主数据进行保存/更新
        if (UtilCollection.isNotEmpty(unitSet)) {
            // 保存计量单位（仅缓存中不存在时）
            dicUnitDataWrap.saveBatch(unitSet);
            // 刷新缓存
            editCacheService.refreshUnitCache();
        }
        if (UtilCollection.isNotEmpty(materialGroupSet)) {
            // 保存物料组（仅缓存中不存在时）
            dicMaterialGroupDataWrap.saveBatch(materialGroupSet);
            // 刷新缓存
            editCacheService.refreshDicMaterialGroupCache();
        }
        if (UtilCollection.isNotEmpty(materialTypeSet)) {
            // 保存物料类型（仅缓存中不存在时）
            dicMaterialTypeDataWrap.saveBatch(materialTypeSet);
            // 刷新缓存
            editCacheService.refreshDicMaterialTypeCache();
        }


        // 待保存的新增物料集合
        List<DicMaterialDTO> materialSaveList = new ArrayList<>();

        // 批量保存和更新物料主数据
        if (UtilCollection.isNotEmpty(materialDTOList)) {

            // 待处理的物料清单
            List<String> maraCodeList = new ArrayList<>();

            // 回填 物料-记录单位id 物料组id 物料类型id
            materialDTOList.forEach(p -> {
                Long unitId = getUnitId(unitCodeMap, p.getUnitCode());
                p.setUnitId(UtilNumber.isNotEmpty(unitId) ? unitId : 0L); // 计量单位id
                Long maraGroupId = getMaraGroupId(groupCodeMap, p.getMatGroupCode());
                p.setMatGroupId(UtilNumber.isNotEmpty(maraGroupId) ? maraGroupId : 0L); // 物料组id
                Long maraTypeId = getMaraTypeId(typeCodeMap, p.getMatTypeCode());
                p.setMatTypeId(UtilNumber.isNotEmpty(maraTypeId) ? maraTypeId : 0L); // 物料类型id
                maraCodeList.add(p.getMatCode());

            });

            //查询已有物料
            QueryWrapper<DicMaterial> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(DicMaterial::getMatCode, maraCodeList);
            List<DicMaterial> oldMaterialList = dicMaterialDataWrap.list(queryWrapper);

            Map<String, DicMaterial> oldMatMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oldMaterialList)) {
                oldMaterialList.forEach(p -> oldMatMap.put(p.getMatCode(), p));
            }

            materialDTOList.forEach(p -> {
                DicMaterial dicMara = oldMatMap.get(p.getMatCode());

                // 更新物
                if (dicMara != null) {
                    DicMaterialDTO dto = new DicMaterialDTO();
                    dto.setId(dicMara.getId());
                    dto.setMatName(p.getMatName());
                    dto.setMatNameEn(p.getMatNameEn());
                    dto.setUnitId(p.getUnitId());
                    dto.setMatGroupId(p.getMatGroupId());
                    dto.setMatTypeId(p.getMatTypeId());
                    dto.setExtBasicMaterial(p.getExtBasicMaterial());

                    materialSaveList.add(dto);
                } else {
                //新增物料
                    materialSaveList.add(p);
                }
            });
        }

        return materialSaveList;
    }


    /**
     * 格式化返回物料列表的中英文数据
     */
    private JsonArray formatMaterialResponse(JsonArray originalDataList) {
        // 对dataList进行处理,去除重复的MATNR数据,并根据MAKT-SPRAS区分中英文描述
        Map<String, JsonObject> materialMap = new HashMap<>();
        Map<String, String> materialDescMap = new HashMap<>();
        
        for (int i = 0; i < originalDataList.size(); i++) {
            JsonObject maraObj = originalDataList.get(i).getAsJsonObject();
            String matnr = maraObj.get("MATNR").getAsString();
            String spras = maraObj.get("MAKT-SPRAS").getAsString();
            String maktx = maraObj.get("MATL_DESC").getAsString();
            
            // 如果materialMap中不存在该物料,则保存第一条记录
            if (!materialMap.containsKey(matnr)) {
                materialMap.put(matnr, maraObj);
            }
            
            // 根据SPRAS保存中英文描述
            if ("ZH".equals(spras)) {
                materialDescMap.put(matnr + "_ZH", maktx);
            } else if ("EN".equals(spras)) {
                materialDescMap.put(matnr + "_EN", maktx);
            }
        }
        
        JsonArray dataList = new JsonArray();
        for (Map.Entry<String, JsonObject> entry : materialMap.entrySet()) {
            String matnr = entry.getKey();
            JsonObject maraObj = entry.getValue();
            
            // 设置中英文描述
            String zhDesc = materialDescMap.get(matnr + "_ZH");
            String enDesc = materialDescMap.get(matnr + "_EN");
            
            maraObj.addProperty("MATL_DESC", UtilString.isNotNullOrEmpty(zhDesc) ? zhDesc : "");
            maraObj.addProperty("MAKTX_EN", UtilString.isNotNullOrEmpty(enDesc) ? enDesc : "");
            
            dataList.add(maraObj);
        }

        return dataList;
    }

    
    /**
     * 获取计量单位id
     */
    private Long getUnitId(Map<String, Long> unitCodeMap, String unitCode) {
        Long id = unitCodeMap.get(unitCode);
        if (id == null) {
            id = dictionaryService.getUnitIdCacheByCode(unitCode);
            unitCodeMap.put(unitCode, id);
        }
        return id;
    }

    /**
     * 获取物料组id
     */
    private Long getMaraGroupId(Map<String, Long> groupCodeMap, String maraGroupCode) {
        Long id = groupCodeMap.get(maraGroupCode);
        if (id == null) {
            id = dictionaryService.getMatGroupIdByMatGroupCode(maraGroupCode);
            groupCodeMap.put(maraGroupCode, id);
        }
        return id;
    }

    /**
     * 获取物料类型id
     */
    private Long getMaraTypeId(Map<String, Long> typeCodeMap, String typeCode) {
        Long id = typeCodeMap.get(typeCode);
        if (id == null) {
            id = dictionaryService.getMatTypeIdByMatTypeCode(typeCode);
            typeCodeMap.put(typeCode, id);
        }
        return id;
    }


    /**************************************************************************
     ***************     物料凭证过账      **************
     **************************************************************************/

    /**
     * 物料凭证过账
     * 将WMS系统的物料凭证信息同步到SAP系统
     *
     * @param header 过账单据抬头信息
     * @param isWriteOff 是否为冲销操作
     * @return HXPostingReturn SAP返回结果
     * @throws WmsException 当参数校验失败或接口调用异常时抛出
     */
    public HXPostingReturn posting(HXPostingHeader header, boolean isWriteOff) {
        // 公共必输参数校验
        validatePostingHeader(header);
        validatePostingItems(header.getItems());
        // 特殊必输参数校验与格式化
        validateSpecialFields(header, header.getItems(), isWriteOff);
        
        HXPostingReturn erpReturnObj = new HXPostingReturn();
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            erpReturnObj = this.postingBySap(header);
        } else {
            // 设置默认返回值
            erpReturnObj.setSuccess("S"); // 默认成功
            erpReturnObj.setReturnMessage("离线模式,不调用SAP接口"); // 默认消息
            erpReturnObj.setMatDocCode(String.valueOf(10000000 + (int)(Math.random() * 90000000))); // 物料凭证编号随机生成8位数字
            erpReturnObj.setMatDocYear(2024); // 物料凭证年度置空
            erpReturnObj.setWmsReceiptCode(""); // 设置WMS单据编号
            erpReturnObj.setWmsReceiptRid(""); // WMS单据行项目置空
        }
        return erpReturnObj;
    }

    /**
     * 物料凭证冲销
     * 将WMS系统的物料凭证信息同步到SAP系统
     *
     * @param header 过账单据抬头信息
     * @param isWriteOff 是否为冲销操作
     * @return HXPostingReturn SAP返回结果
     * @throws WmsException 当参数校验失败或接口调用异常时抛出
     */
    public HXPostingReturn writeOff(HXPostingHeader header, boolean isWriteOff) {
        // 公共必输参数校验
        validatePostingHeader(header);
        validatePostingItems(header.getItems());
        // 特殊必输参数校验与格式化
        validateSpecialFields(header, header.getItems(), isWriteOff);

        HXPostingReturn erpReturnObj = new HXPostingReturn();
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            erpReturnObj = this.writeOffBySap(header);
        } else {
            // 设置默认返回值
            erpReturnObj.setSuccess("S"); // 默认成功
            erpReturnObj.setReturnMessage("离线模式,不调用SAP接口"); // 默认消息
            erpReturnObj.setMatDocCode(String.valueOf(10000000 + (int)(Math.random() * 90000000))); // 物料凭证编号随机生成8位数字
            erpReturnObj.setMatDocYear(2024); // 物料凭证年度置空
            erpReturnObj.setWmsReceiptCode(""); // 设置WMS单据编号
            erpReturnObj.setWmsReceiptRid(""); // WMS单据行项目置空
        }
        return erpReturnObj;
    }

    /**
     * 调用SAP进行物料凭证过账
     */
    private HXPostingReturn writeOffBySap(HXPostingHeader header) {
        // 构建请求参数
        JsonObject params = getHeadParams(header.getReceiptId().toString(), header.getReceiptCode(), header.getReceiptType().toString());

        // 物料凭证号  物料凭证年份
        params.addProperty("MATERIALDOCUMENT", header.getItems().get(0).getMatDocCode());
        params.addProperty("MATDOCUMENTYEAR", header.getItems().get(0).getMatDocYear());
        params.addProperty("GOODSMVT_PSTNG_DATE",header.getPostingDate()); // 冲销过账日期
        //params.addProperty("GOODSMVT_PR_UNAME", ""); //冲销人
        // 行项目参数
        JsonArray itemParams = new JsonArray();
        header.getItems().forEach(item -> {
            JsonObject itemParam = new JsonObject();
            itemParam.addProperty("MATDOC_ITEM", item.getReceiptRid()); // 物料凭证行项目
            itemParams.add(itemParam);
        });
        params.add("GOODSMVT_MATDOCITEM", itemParams);

        // 调用SAP接口
        String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.CANCEL;
        log.debug("SAP过账入参：{}", params);
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);
        log.debug("SAP过账出参：{}", returnObject);

        // 处理返回结果
        JsonObject data = returnObject.get("data").getAsJsonObject();
        JsonObject head = data.get("GOODSMVT_HEADRET").getAsJsonObject();
        JsonArray RETURN_Array = data.get("RETURN").getAsJsonArray();
        JsonObject RETURN_Object = RETURN_Array.get(0).getAsJsonObject();
        HXPostingReturn erpReturnObj = new HXPostingReturn();
        erpReturnObj.setSuccess(RETURN_Object.get("TYPE").getAsString());
        erpReturnObj.setReturnMessage(RETURN_Object.get("MESSAGE").getAsString());
        erpReturnObj.setMatDocCode(head.get("MAT_DOC").getAsString());
        erpReturnObj.setMatDocYear(head.get("DOC_YEAR").getAsInt());
        return erpReturnObj;
    }

    /**
     * 调用SAP进行物料凭证过账
     */
    private HXPostingReturn postingBySap(HXPostingHeader header) {
        // 构建请求参数
        JsonObject params = assembleSapParams(header, header.getItems());
        
        // 调用SAP接口
        String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.POSTING;
        log.debug("SAP过账入参：{}", params);
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);
        log.debug("SAP过账出参：{}", returnObject);
        
        // 处理返回结果
        return handlePostingResponse(returnObject);
    }


    private void checkPostingResponse(JsonObject returnObject) {
        // 根目录下存在 success, message , data 
        if (returnObject.has("success") && returnObject.has("message") && returnObject.has("data")) {
            return;
        }
        throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, "SAP接口返回格式错误");
    }   

    /**
     * 处理过账返回结果
     */
    private HXPostingReturn handlePostingResponse(JsonObject returnObject) {
        HXPostingReturn erpReturnObj = new HXPostingReturn();
        if (returnObject != null) {

            checkPostingResponse(returnObject);

            this.handleSapResponse(returnObject, "物料凭证过账");

            erpReturnObj.setSuccess(UtilObject.getStringOrEmpty(returnObject.get("TYPE"))); // 返回状态
            JsonObject data = returnObject.get("data").getAsJsonObject();
            if(data.get("RETURN") instanceof JsonObject){
                erpReturnObj.setReturnMessage(data.get("RETURN").getAsJsonObject().get("MESSAGE").getAsString()); // 返回消息
                erpReturnObj.setSuccess(data.get("RETURN").getAsJsonObject().get("TYPE").getAsString()); // 返回消息
            } else if(data.get("RETURN") instanceof JsonArray) {
                JsonArray jsonArray = data.get("RETURN").getAsJsonArray();
                if (!jsonArray.isJsonNull() && jsonArray.size() > 0) {
                    erpReturnObj.setReturnMessage(jsonArray.get(0).getAsJsonObject().get("MESSAGE").getAsString()); // 返回消息
                    erpReturnObj.setSuccess(jsonArray.get(0).getAsJsonObject().get("TYPE").getAsString()); // 返回消息
                }
            }
            erpReturnObj.setMatDocCode(data.get("MATERIALDOCUMENT").getAsString()); // 物料凭证编号
            erpReturnObj.setMatDocYear(data.get("MATDOCUMENTYEAR").getAsInt()); // 物料凭证年度
            erpReturnObj.setWmsReceiptCode(""); // wms单据编号
            erpReturnObj.setWmsReceiptRid(""); // wms单据行项目
        } else {
            erpReturnObj.setSuccess("E");
            erpReturnObj.setReturnMessage("SAP返回结果为空");
        }

        return erpReturnObj;
    }



    /**
     * 校验过账单据抬头必填字段
     * 包括过账日期、凭证日期、参考凭证号等
     *
     * @param header 过账单据抬头信息
     * @throws WmsException 当必填字段为空时抛出异常
     */
    private void validatePostingHeader(HXPostingHeader header) {
        if(header == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "抬头参数不能为空");
        }
        
        if(UtilString.isNullOrEmpty(header.getPostingDate())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "过账日期不能为空");
        }
        
        if(UtilString.isNullOrEmpty(header.getDocDate())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "凭证日期不能为空");
        }
        
        if(UtilString.isNullOrEmpty(header.getReceiptCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "参考凭证号不能为空");
        }
        
        if(header.getReceiptType() == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "参考凭证类型不能为空");
        }
    }

    /**
     * 校验过账单据行项目必填字段
     * 包括单据行项目号、物料编码、工厂、库存地点等
     *
     * @param items 过账单据行项目列表
     * @throws WmsException 当必填字段为空时抛出异常
     */
    private void validatePostingItems(List<HXPostingItem> items) {
        if(CollectionUtils.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "行项目不能为空");
        }
        // 校验通用必输字段
        items.stream()
            .filter(item -> UtilString.isNullOrEmpty(item.getReceiptRid()) 
                || UtilString.isNullOrEmpty(item.getMatCode())
                || UtilString.isNullOrEmpty(item.getFtyCode()) 
                || UtilString.isNullOrEmpty(item.getLocationCode1())
                || UtilString.isNullOrEmpty(item.getQty())
                || UtilString.isNullOrEmpty(item.getUnitCode()))
            .findFirst()
            .ifPresent(item -> {
                if(UtilString.isNullOrEmpty(item.getReceiptRid())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "单据行项目号不能为空");
                }
                // if(UtilString.isNullOrEmpty(item.getMatCode())) {
                //     throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "物料编码不能为空");
                // }
                if(UtilString.isNullOrEmpty(item.getFtyCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "工厂不能为空");
                }
                // if(UtilString.isNullOrEmpty(item.getLocationCode1())) {
                //     throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "库存地点不能为空");
                // }
                if(UtilString.isNullOrEmpty(item.getQty())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "数量不能为空");
                }
                if(UtilString.isNullOrEmpty(item.getUnitCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "计量单位不能为空");
                }
            });
    }


    /**
     * 根据单据类型校验和处理特殊字段
     * 针对不同类型的单据(采购入库、油品入库、采购退货等)进行特殊字段的校验和处理
     *
     * @param header 过账单据抬头信息
     * @param items 过账单据行项目列表
     * @param isWriteOff 是否为冲销操作
     * @throws WmsException 当特殊字段校验失败时抛出异常
     */
    private void validateSpecialFields(HXPostingHeader header, List<HXPostingItem> items, boolean isWriteOff) {
        Integer receiptType = header.getReceiptType();

        items.forEach(item -> {
            // 采购入库或合同收货也走101 过账
            if (EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType) || (EnumReceiptType.CONTRACT_RECEIVING.getValue().equals(receiptType))) {
                header.setGmCode("01");
                if (UtilString.isNullOrEmpty(item.getPurchaseOrderCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "采购订单编号不能为空");
                }
                if (UtilString.isNullOrEmpty(item.getPurchaseOrderItemCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "采购订单行项目号不能为空");
                }

                item.setMoveType(isWriteOff ? "102" : "101"); // 过账101 冲销102
                item.setRemark(item.getRemark() == null ? "" : item.getRemark());
                item.setMvtInd("B"); // 采购订单收货默认填B
                
                item.setFtyCode2(""); // 空值
                item.setLocationCode2(""); // 空值
                item.setCostCenter(""); // 空值
                item.setWbsCode(""); // 空值
                if(!isWriteOff) {
                    item.setMatDocCode(""); // 非冲销不需要物料凭证
                    item.setMatDocYear(""); // 非冲销不需要物料凭证年份
                }

            }

            // 采购退货
            else if (EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue().equals(receiptType)) {
                header.setGmCode("01");
                if (UtilString.isNullOrEmpty(item.getPurchaseOrderCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "采购订单编号不能为空");
                }
                if (UtilString.isNullOrEmpty(item.getPurchaseOrderItemCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "采购订单行项目号不能为空");
                }

                item.setMoveType(isWriteOff ? "102" : "101"); // 过账101 冲销102
                item.setRemark(item.getRemark() == null ? "" : item.getRemark());
                item.setMvtInd("B"); // 采购订单收货默认填B
                item.setFtyCode2(""); // 空值
                item.setLocationCode2(""); // 空值
                item.setInvoiceCode(""); // 空值
                item.setInvoiceDate(""); // 空值
                item.setCostCenter(""); // 空值
                item.setWbsCode(""); // 空值
                if (!isWriteOff) {
                    item.setMatDocCode(""); // 非冲销不需要物料凭证
                    item.setMatDocYear(""); // 非冲销不需要物料凭证年份
                    item.setReceiptRid(""); // 非冲销不需要单据行项目
                }
            }

            // 领料出库
            else if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(receiptType)) {
                header.setGmCode("03");
                // 判断领用类型 部门-》成本中心  生产-》项目
                if (EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(header.getReceiveType())) {
                    // 生产-》项目
                    if (UtilString.isNullOrEmpty(item.getWbsCode())) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "WBS编号不能为空");
                    }
                    item.setMoveType(isWriteOff ? "222" : "221"); // 过账221 冲销222
                    item.setCostCenter(""); // 空值
                } else if (EnumReceiveType.DEPT_REQ_USE.getValue().equals(header.getReceiveType())) {
                    // 部门-》成本中心 
                    if (UtilString.isNullOrEmpty(item.getCostCenter())) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "成本中心不能为空");
                    }
                    item.setMoveType(isWriteOff ? "202" : "201"); // 过账201 冲销202
                    item.setWbsCode(item.getWbsCode() == null ? "" : item.getWbsCode());
                }
                

                
                item.setRemark(item.getRemark() == null ? "" : item.getRemark());

                item.setMvtInd(""); // 空值
                item.setFtyCode2(""); // 空值
                item.setLocationCode2(""); // 空值
                item.setInvoiceCode(""); // 空值
                item.setInvoiceDate(""); // 空值
                item.setPurchaseOrderCode(""); // 空值
                item.setPurchaseOrderItemCode(""); // 空值
                
                if (!isWriteOff) {
                    item.setMatDocCode(""); // 非冲销不需要物料凭证
                    item.setMatDocYear(""); // 非冲销不需要物料凭证年份
                    item.setReceiptRid(""); // 非冲销不需要单据行项目
                }
            }

            // 领料退库
            else if (EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue().equals(receiptType)) {
                header.setGmCode("03");
                // 判断领用类型 部门-》成本中心 生产-》项目
                if (EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(header.getReceiveType())) {
                    // 生产-》项目
                    if (UtilString.isNullOrEmpty(item.getWbsCode())) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "WBS编号不能为空");
                    }
                    item.setMoveType(isWriteOff ? "221" : "222"); // 过账221 冲销222
                    item.setCostCenter(""); // 空值
                } else {
                    // 部门-》成本中心
                    if (UtilString.isNullOrEmpty(item.getCostCenter())) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "成本中心不能为空");
                    }
                    item.setMoveType(isWriteOff ? "201" : "202"); // 过账201 冲销202
                    item.setWbsCode(item.getWbsCode() == null ? "" : item.getWbsCode());
                }

                item.setRemark(item.getRemark() == null ? "" : item.getRemark());

                item.setMvtInd(""); // 空值
                item.setFtyCode2(""); // 空值
                item.setLocationCode2(""); // 空值
                item.setInvoiceCode(""); // 空值
                item.setInvoiceDate(""); // 空值
                item.setPurchaseOrderCode(""); // 空值
                item.setPurchaseOrderItemCode(""); // 空值

                if (!isWriteOff) {
                    item.setMatDocCode(""); // 非冲销不需要物料凭证
                    item.setMatDocYear(""); // 非冲销不需要物料凭证年份
                    item.setReceiptRid(""); // 非冲销不需要单据行项目
                }
            }

            // 报废出库
            else if (EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue().equals(receiptType)) {
                // if (UtilString.isNullOrEmpty(item.getCostCenter())) {
                //     throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "成本中心不能为空");
                // }
                header.setGmCode("03");
                item.setMoveType(isWriteOff ? "552" : "551"); // 过账551 冲销552
                item.setRemark(item.getRemark() == null ? "" : item.getRemark());

                item.setMvtInd(""); // 空值
                item.setFtyCode2(""); // 空值
                item.setLocationCode2(""); // 空值
                item.setInvoiceCode(""); // 空值
                item.setInvoiceDate(""); // 空值
                item.setPurchaseOrderCode(""); // 空值
                item.setPurchaseOrderItemCode(""); // 空值
                item.setWbsCode(""); // 空值
                item.setCostCenter("110420001"); // 成本中心随便传了个固定值，说是实际不使用但是又必须要传， 囧
                if (!isWriteOff) {
                    item.setMatDocCode(""); // 非冲销不需要物料凭证
                    item.setMatDocYear(""); // 非冲销不需要物料凭证年份
                    item.setReceiptRid(""); // 非冲销不需要单据行项目
                }
            }

            // 转储
            else if (EnumReceiptType.STOCK_TRANSPORT.getValue().equals(receiptType)) {
                header.setGmCode("04");
                if (UtilString.isNullOrEmpty(item.getLocationCode2())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "目标库存地点不能为空");
                }

                item.setMoveType("311"); // 过账311
                item.setRemark(item.getRemark() == null ? "" : item.getRemark());
                item.setFtyCode2(item.getFtyCode()); // 项目当前仅支持一个工厂

                item.setMvtInd(""); // 空值
                item.setInvoiceCode(""); // 空值
                item.setInvoiceDate(""); // 空值
                item.setPurchaseOrderCode(""); // 空值
                item.setPurchaseOrderItemCode(""); // 空值
                item.setCostCenter(""); // 空值
                item.setWbsCode(""); // 空值
                if (!isWriteOff) {
                    item.setMatDocCode(""); // 非冲销不需要物料凭证
                    item.setMatDocYear(""); // 非冲销不需要物料凭证年份
                    item.setReceiptRid(""); // 非冲销不需要单据行项目
                }
            }
        });
    }


    /**
     * 组装SAP接口参数
     * 将WMS系统的过账信息转换为SAP接口所需的参数格式
     *
     * @param header 过账单据抬头信息
     * @param items 过账单据行项目列表
     * @return JsonObject SAP接口参数
     */
    private JsonObject assembleSapParams(HXPostingHeader header, List<HXPostingItem> items) {
        // 通用输入参数
        JsonObject params = getHeadParams(header.getReceiptId().toString(), header.getReceiptCode(), header.getReceiptType().toString());
        
        params.addProperty("CHECK", ""); // 固定结构

        // 固定结构
        JsonObject goodsmvtCode = new JsonObject();
        goodsmvtCode.addProperty("GM_CODE", header.getGmCode());
        params.add("GOODSMVT_CODE", goodsmvtCode);

        // 抬头参数
        JsonObject headerParams = new JsonObject();
        headerParams.addProperty("PSTNG_DATE", header.getPostingDate()); // 过账日期
        headerParams.addProperty("DOC_DATE", header.getDocDate()); // 凭证日期
        headerParams.addProperty("HEADER_TXT", header.getReceiptCode()); // 参考凭证号
        params.add("GOODSMVT_HEADER", headerParams);

        // wms单号
        params.addProperty("ZMVTID", header.getReceiptCode());
        
        // 行项目参数
        JsonArray itemParams = new JsonArray();
        items.forEach(item -> {
            JsonObject itemParam = new JsonObject();
            
            itemParam.addProperty("ZEILE", item.getReceiptRid()); // 单据行项目号
            itemParam.addProperty("MATERIAL", item.getMatCode()); // 物料编码  
            itemParam.addProperty("PLANT", item.getFtyCode()); // 工厂
            itemParam.addProperty("STGE_LOC", item.getLocationCode1()); // 库存地点1
            itemParam.addProperty("MOVE_PLANT", item.getFtyCode2()); // 目标工厂
            itemParam.addProperty("MOVE_STLOC", item.getLocationCode2()); // 目标库存地点
            itemParam.addProperty("MOVE_TYPE", item.getMoveType()); // 移动类型
            itemParam.addProperty("ENTRY_QNT", item.getQty()); // 数量
            itemParam.addProperty("ENTRY_UOM", item.getUnitCode()); // 计量单位
            itemParam.addProperty("PO_NUMBER", item.getPurchaseOrderCode()); // 采购订单编号
            itemParam.addProperty("PO_ITEM", item.getPurchaseOrderItemCode()); // 采购凭证的项目编号
            itemParam.addProperty("ZFPH", item.getInvoiceCode()); // 发票号
            itemParam.addProperty("ZFPRQ", item.getInvoiceDate()); // 发票日期
            itemParam.addProperty("COSTCENTER", item.getCostCenter()); // 成本中心
            itemParam.addProperty("WBS_ELEM", item.getWbsCode()); // WBS编号
            itemParam.addProperty("SGTXT", item.getRemark()); // 备注
            itemParam.addProperty("MVT_IND", item.getMvtInd()); // 移动类型标识 ，只有采购入库有值 B

            itemParam.addProperty("BATCH", ""); // 批次
            itemParam.addProperty("STCK_TYPE", ""); // 库存类型

            itemParam.addProperty("REF_DOC", item.getMatDocCode()); // 物料凭证编号
            itemParam.addProperty("REF_DOC_YR", item.getMatDocYear()); // 物料凭证年份
            itemParam.addProperty("REF_DOC_IT", item.getReceiptRid()); // 物料凭证行项目

            if (UtilString.isNotNullOrEmpty(item.getAssetCode())) {
                itemParam.addProperty("ASSET_NO", item.getAssetCode()); // 主资产号
                itemParam.addProperty("SUB_NUMBER", item.getAssetSubCode()); // 资产子编号
            }

            itemParams.add(itemParam);
        });
        params.add("GOODSMVT_ITEM", itemParams);

        return params;
    }


    /**************************************************************************
     ***************     采购订单创建      **************
     **************************************************************************/

    /**
     * 创建采购订单
     * 在SAP系统中创建新的采购订单
     *
     * @param header 采购订单抬头信息
     * @return HXPurchaseOrderReturn SAP返回结果
     * @throws WmsException 当参数校验失败或接口调用异常时抛出
     */
    public HXPurchaseOrderReturn createPurchaseOrder(HXPurchaseOrderHeader header) {
        // 公共必输参数校验
        validatePurchaseOrderHeader(header);
        validatePurchaseOrderItems(header.getItems());
        
        HXPurchaseOrderReturn erpReturnObj = new HXPurchaseOrderReturn();
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            // 构建请求参数
            JsonObject params = assemblePurchaseOrderParams(header);
            
            // 调用SAP接口
            String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.CREATE_PURCHASE_ORDER;
            log.debug("SAP创建采购订单入参：{}", params);
            JsonObject returnObject = this.callHXSapAPI(erpUrl, params);
            log.debug("SAP创建采购订单出参：{}", returnObject);
            
            // 处理返回结果
            erpReturnObj = handlePurchaseOrderResponse(returnObject);
        } else {
            // 本地处理
            erpReturnObj.setPurchaseOrderCode(String.valueOf(10000000 + (int) (Math.random() * 90000000)));
            erpReturnObj.setSuccess("S"); // 默认成功
            erpReturnObj.setReturnMessage("离线模式,不调用SAP接口"); // 默认消息
        }   
        return erpReturnObj;
    }

    /**
     * 校验采购订单抬头必填字段
     * 包括采购订单类型等
     *
     * @param header 采购订单抬头信息
     * @throws WmsException 当必填字段为空时抛出异常
     */
    private void validatePurchaseOrderHeader(HXPurchaseOrderHeader header) {
        if(header == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "抬头参数不能为空");
        }
        
        if(UtilString.isNullOrEmpty(header.getPurchaseOrderType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "采购订单类型不能为空");
        }
    }

    /**
     * 校验采购订单行项目必填字段
     *
     * @param items 采购订单行项目列表
     * @throws WmsException 当必填字段为空时抛出异常
     */
    private void validatePurchaseOrderItems(List<HXPurchaseOrderItem> items) {
        if(CollectionUtils.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "行项目不能为空");
        }
    }

    /**
     * 组装采购订单接口参数
     * 将WMS系统的采购订单信息转换为SAP接口所需的参数格式
     *
     * @param header 采购订单抬头信息
     * @return JsonObject SAP接口参数
     */
    private JsonObject assemblePurchaseOrderParams(HXPurchaseOrderHeader header) {
        // 通用输入参数
        JsonObject params = getHeadParams( "采购订单创建", header.getReferenceReceiptCode() ,EnumReceiptType.PURCHASE_RECEIPT_CREATE.getValue().toString());
        
        // 抬头参数
        JsonObject headerParams = new JsonObject();
        headerParams.addProperty("EBELN",""); // 采购订单编号, 固定空值
        headerParams.addProperty("BSART", header.getPurchaseOrderType()); // 采购订单类型 ， 逻辑在外部处理，值域ZY01-ZY06
        headerParams.addProperty("BEDAT", UtilDate.convertDateToDateStr(new Date())); // 凭证日期 ， 当前日期
        headerParams.addProperty("LIFNR", header.getSupplierCode()); // 供应商编码
        headerParams.addProperty("EKORG", "1104"); // 采购组织
        headerParams.addProperty("EKGRP", "110"); // 采购组
        headerParams.addProperty("BUKRS", "1104"); // 公司代码
        headerParams.addProperty("ZTERM", header.getPaymentTerms()); // 付款条件
        headerParams.addProperty("KUFIX", ""); // 固定汇率, 固定空值
        headerParams.addProperty("WAERS", header.getCurrency()); // 货币
        params.add("HEAD", headerParams);

        // 参考单号
        params.addProperty("ZMM_CONTRACT", header.getReferenceReceiptCode());
        // 离岸采购相关费用和运输相关费用
        JsonArray condParams = new JsonArray();
        // 行项目参数
        JsonArray itemParams = new JsonArray();
        header.getItems().forEach(item -> {
            JsonObject itemParam = new JsonObject();
            
            // 基本信息
            itemParam.addProperty("EBELP", item.getItemCode()); // 行项目号
            itemParam.addProperty("BAPICUREXT", item.getNetPrice()); // 单据不含税

            // 逻辑 ZY01为空， ZY02 固定A ， ZY03-05 取传入的值， ZY06为空
            String accountCategory = "";
            if(header.getPurchaseOrderType().equals("ZY01") || header.getPurchaseOrderType().equals("ZY06")) {
                accountCategory = "";
            } else if(header.getPurchaseOrderType().equals("ZY02")) {
                accountCategory = "A";
            } else {
                accountCategory = item.getAccountCategory();
            }

            itemParam.addProperty("KNTTP", accountCategory); // 科目分配类别

//            boolean isProject = header.getPurchaseOrderType().equals("ZY03") || header.getPurchaseOrderType().equals("ZY05");
//            itemParam.addProperty("PSTYP", isProject ? "D" : ""); // 项目类别
            itemParam.addProperty("PSTYP", ""); // 项目类别

            itemParam.addProperty("MATNR", item.getMatCode()); // 物料编码
            itemParam.addProperty("TXZ01", item.getMatName()); // 物料描述
            itemParam.addProperty("MATKL", item.getMaterialGroup()); // 物料组
            itemParam.addProperty("BSTMG", item.getQty()); // 数量
            itemParam.addProperty("BSTME", item.getUnitCode()); // 计量单位
            itemParam.addProperty("EEIND", item.getDeliveryDate()); // 交货日期
            // itemParam.addProperty("NETPR", item.getNetPrice()); // 采购单价不含税
            
            // ZT01 关税
            if(UtilString.isNotNullOrEmpty(item.getTariff())) {
                JsonObject zt01 = new JsonObject();
                zt01.addProperty("EBELP", item.getItemCode());
                zt01.addProperty("COND_TYPE", "ZT01"); 
                zt01.addProperty("COND_VALUE", item.getTariff());
                zt01.addProperty("CURRENCY", item.getTariffCurrency());
                zt01.addProperty("LIFNR", item.getSupplierCode1());
                condParams.add(zt01);
            }
            
            // ZT02 附加关税
            if (UtilString.isNotNullOrEmpty(item.getAdditionalTariff())) {
                JsonObject zt02 = new JsonObject();
                zt02.addProperty("EBELP", item.getItemCode());
                zt02.addProperty("COND_TYPE", "ZT02");
                zt02.addProperty("COND_VALUE", item.getAdditionalTariff());
                zt02.addProperty("CURRENCY", item.getAdditionalTariffCurrency());
                zt02.addProperty("LIFNR", item.getSupplierCode2());
                condParams.add(zt02);
            }
            
            // ZT03 调节税
            if(UtilString.isNotNullOrEmpty(item.getAdjustmentTax())) {
                JsonObject zt03 = new JsonObject();
                zt03.addProperty("EBELP", item.getItemCode());
                zt03.addProperty("COND_TYPE", "ZT03");
                zt03.addProperty("COND_VALUE", item.getAdjustmentTax());
                zt03.addProperty("CURRENCY", item.getAdjustmentTaxCurrency());
                zt03.addProperty("LIFNR", item.getSupplierCode3());
                condParams.add(zt03);
            }
            
            // ZT04 联邦消费税
            if(UtilString.isNotNullOrEmpty(item.getFederalConsumptionTax())) {
                JsonObject zt04 = new JsonObject();
                zt04.addProperty("EBELP", item.getItemCode());
                zt04.addProperty("COND_TYPE", "ZT04");
                zt04.addProperty("COND_VALUE", item.getFederalConsumptionTax());
                zt04.addProperty("CURRENCY", item.getFederalConsumptionTaxCurrency());
                zt04.addProperty("LIFNR", item.getSupplierCode4());
                condParams.add(zt04);
            }
            
            // ZT05 逾期罚款
            if(UtilString.isNotNullOrEmpty(item.getOverduePenalty())) {
                JsonObject zt05 = new JsonObject();
                zt05.addProperty("EBELP", item.getItemCode());
                zt05.addProperty("COND_TYPE", "ZT05");
                zt05.addProperty("COND_VALUE", item.getOverduePenalty());
                zt05.addProperty("CURRENCY", item.getOverduePenaltyCurrency());
                zt05.addProperty("LIFNR", item.getSupplierCode5());
                condParams.add(zt05);
            }

            // ZT06 发票缺失
            if(UtilString.isNotNullOrEmpty(item.getInvoiceMissing())) {
                JsonObject zt05 = new JsonObject();
                zt05.addProperty("EBELP", item.getItemCode());
                zt05.addProperty("COND_TYPE", "ZT06");
                zt05.addProperty("COND_VALUE", item.getInvoiceMissing());
                zt05.addProperty("CURRENCY", item.getInvoiceMissingCurrency());
                zt05.addProperty("LIFNR", item.getSupplierCode6());
                condParams.add(zt05);
            }

            // ZT07 GD提交费
            if(UtilString.isNotNullOrEmpty(item.getGdSubmissionFee())) {
                JsonObject zt05 = new JsonObject();
                zt05.addProperty("EBELP", item.getItemCode());
                zt05.addProperty("COND_TYPE", "ZT07");
                zt05.addProperty("COND_VALUE", item.getGdSubmissionFee());
                zt05.addProperty("CURRENCY", item.getGdSubmissionFeeCurrency());
                zt05.addProperty("LIFNR", item.getSupplierCode7());
                condParams.add(zt05);
            }

            // ZT08 印花税
            if(UtilString.isNotNullOrEmpty(item.getStampDuty())) {
                JsonObject zt05 = new JsonObject();
                zt05.addProperty("EBELP", item.getItemCode());
                zt05.addProperty("COND_TYPE", "ZT08");
                zt05.addProperty("COND_VALUE", item.getStampDuty());
                zt05.addProperty("CURRENCY", item.getStampDutyCurrency());
                zt05.addProperty("LIFNR", item.getSupplierCode8());
                condParams.add(zt05);
            }

            // ZT09 基建税
            if(UtilString.isNotNullOrEmpty(item.getConstructionTax())) {
                JsonObject zt05 = new JsonObject();
                zt05.addProperty("EBELP", item.getItemCode());
                zt05.addProperty("COND_TYPE", "ZT09");
                zt05.addProperty("COND_VALUE", item.getConstructionTax());
                zt05.addProperty("CURRENCY", item.getConstructionTaxCurrency());
                zt05.addProperty("LIFNR", item.getSupplierCode9());
                condParams.add(zt05);
            }

            // ZT10 检验检测费
            if(UtilString.isNotNullOrEmpty(item.getInspectionFee())) {
                JsonObject zt05 = new JsonObject();
                zt05.addProperty("EBELP", item.getItemCode());
                zt05.addProperty("COND_TYPE", "ZT10");
                zt05.addProperty("COND_VALUE", item.getInspectionFee());
                zt05.addProperty("CURRENCY", item.getInspectionFeeCurrency());
                zt05.addProperty("LIFNR", item.getSupplierCode10());
                condParams.add(zt05);
            }
            
            // ZF01 海/空运费
            if(UtilString.isNotNullOrEmpty(item.getSeaAirFreight())) {
                JsonObject zf01 = new JsonObject();
                zf01.addProperty("EBELP", item.getItemCode());
                zf01.addProperty("COND_TYPE", "ZF01");
                zf01.addProperty("COND_VALUE", item.getSeaAirFreight());
                zf01.addProperty("CURRENCY", item.getSeaAirFreightCurrency());
                zf01.addProperty("LIFNR", item.getSupplierCode11());
                condParams.add(zf01);
            }
            
            // ZF02 内陆运输费
            if(UtilString.isNotNullOrEmpty(item.getInlandTransportFee())) {
                JsonObject zf02 = new JsonObject();
                zf02.addProperty("EBELP", item.getItemCode());
                zf02.addProperty("COND_TYPE", "ZF02");
                zf02.addProperty("COND_VALUE", item.getInlandTransportFee());
                zf02.addProperty("CURRENCY", item.getInlandTransportFeeCurrency());
                zf02.addProperty("LIFNR", item.getSupplierCode12());
                condParams.add(zf02);
            }
            
            // ZF03 出口报关服务费
            if(UtilString.isNotNullOrEmpty(item.getExportCustomsServiceFee())) {
                JsonObject zf03 = new JsonObject();
                zf03.addProperty("EBELP", item.getItemCode());
                zf03.addProperty("COND_TYPE", "ZF03");
                zf03.addProperty("COND_VALUE", item.getExportCustomsServiceFee());
                zf03.addProperty("CURRENCY", item.getExportCustomsServiceFeeCurrency());
                zf03.addProperty("LIFNR", item.getSupplierCode13());
                condParams.add(zf03);
            }
            
            // ZF04 进口清关服务费
            if(UtilString.isNotNullOrEmpty(item.getImportCustomsServiceFee())) {
                JsonObject zf04 = new JsonObject();
                zf04.addProperty("EBELP", item.getItemCode());
                zf04.addProperty("COND_TYPE", "ZF04");
                zf04.addProperty("COND_VALUE", item.getImportCustomsServiceFee());
                zf04.addProperty("CURRENCY", item.getImportCustomsServiceFeeCurrency());
                zf04.addProperty("LIFNR", item.getSupplierCode14());
                condParams.add(zf04);
            }
            
            // ZF05 进口拖车押车费
            if(UtilString.isNotNullOrEmpty(item.getImportTruckRentalFee())) {
                JsonObject zf05 = new JsonObject();
                zf05.addProperty("EBELP", item.getItemCode());
                zf05.addProperty("COND_TYPE", "ZF05");
                zf05.addProperty("COND_VALUE", item.getImportTruckRentalFee());
                zf05.addProperty("CURRENCY", item.getImportTruckRentalFeeCurrency());
                zf05.addProperty("LIFNR", item.getSupplierCode15());
                condParams.add(zf05);
            }

            // 其他基本信息
            itemParam.addProperty("PEINH", "1"); // 价格单位
            itemParam.addProperty("WAERS", header.getCurrency()); // 币种
            itemParam.addProperty("WERKS", item.getFactory()); // 工厂
            itemParam.addProperty("LGORT", item.getStockLocation()); // 库存地点
            itemParam.addProperty("BEDNR", item.getRequirementTrackingNumber()); // 需求跟踪号
            itemParam.addProperty("MWSKZ", item.getTaxCode()); // 税码
            itemParam.addProperty("LOEKZ", ""); // 采购订单删除标识
            if(header.getPurchaseOrderType().equals(EnumPurchaseOrderType.SERVICE_PO)){
                // ZY01
                itemParam.addProperty("WEBRE", item.getMaterialGroup().equals("Y201") ? "" : "X"); // 基于收货的发票校验
            }else{
                // 非ZY01 固定值
                itemParam.addProperty("WEBRE", "X"); // 基于收货的发票校验
            }


            itemParam.addProperty("RETPO", header.getPurchaseOrderType().equals("ZY06") ? "X" : ""); // 退货项目标识
            itemParam.addProperty("UMSON", ""); // 免费项目标识
            itemParam.addProperty("ELIKZ", ""); // 收货已完成标识
            itemParam.addProperty("ANLN1", item.getAssetCardNumber()); // 资产卡片号
            itemParam.addProperty("KOSTL", item.getCostCenter()); // 成本中心
            itemParam.addProperty("PS_POSID", item.getProjectCode()); // 项目编码WBS
            
            itemParams.add(itemParam);
        });
        params.add("ITEM", itemParams);
        if (condParams != null && condParams.size() > 0) {
            params.add("COND", condParams);
        }
        return params;
    }


    private void checkPurchaseOrderResponse(JsonObject returnObject) {
        // 根目录下存在 success, message , data 
        if (returnObject.has("success") && returnObject.has("message") && returnObject.has("data")) {
            return;
        }

        throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, "SAP接口返回格式错误");
    }

    /**
     * 处理采购订单返回结果
     * 解析SAP返回的结果，转换为WMS系统可用的格式
     *
     * @param returnObject SAP返回的JSON结果
     * @return HXPurchaseOrderReturn 处理后的返回结果
     * @throws WmsException 当SAP返回错误状态时抛出异常
     */
    private HXPurchaseOrderReturn handlePurchaseOrderResponse(JsonObject returnObject) {
        HXPurchaseOrderReturn erpReturnObj = new HXPurchaseOrderReturn();
        if (returnObject != null) {
            checkPurchaseOrderResponse(returnObject);

            this.handleSapResponse(returnObject, "创建采购订单");

            JsonObject data = returnObject.get("data").getAsJsonObject();
            erpReturnObj.setSuccess("S"); // 返回状态
            if (data.get("RETURN") instanceof JsonObject) {
                erpReturnObj.setReturnMessage(data.get("RETURN").getAsJsonObject().get("MESSAGE").getAsString()); // 返回消息
            } else if (data.get("RETURN") instanceof JsonArray) {
                JsonArray jsonArray = data.get("RETURN").getAsJsonArray();
                if (!jsonArray.isJsonNull() || jsonArray.size() > 0) {
                    erpReturnObj.setReturnMessage(jsonArray.get(0).getAsJsonObject().get("MESSAGE").getAsString()); // 返回消息
                }
            }
            if(data.has("EBELN")){
                erpReturnObj.setPurchaseOrderCode(data.get("EBELN").getAsString()); // 采购订单编号
            }else{
                erpReturnObj.setPurchaseOrderCode(""); // 采购订单编号
            }
        } else {
            erpReturnObj.setSuccess("E");
            erpReturnObj.setReturnMessage("SAP返回结果为空");
        }
        
        return erpReturnObj;
    }

    /**************************************************************************
     ***************     采购订单删除      **************
     **************************************************************************/

    /**
     * 删除采购订单
     * 在SAP系统中删除指定的采购订单
     *
     * @param header 采购订单删除抬头信息
     * @return HXPurchaseOrderReturn SAP返回结果
     * @throws WmsException 当参数校验失败或接口调用异常时抛出
     */
    public HXPurchaseOrderReturn deletePurchaseOrder(HXPurchaseOrderDeleteHeader header) {
        // 参数校验
        validatePurchaseOrderDeleteHeader(header);
        validatePurchaseOrderDeleteItems(header.getItems());

        HXPurchaseOrderReturn erpReturnObj = new HXPurchaseOrderReturn();
        // 调用SAP
        if (UtilConst.getInstance().isErpSyncMode()) {
            // 构建请求参数
            JsonObject params = assemblePurchaseOrderDeleteParams(header);

            // 调用SAP接口
            String erpUrl = UtilConst.getInstance().getErpUrl() + HXSapConst.CREATE_PURCHASE_ORDER;
            log.debug("SAP删除采购订单入参：{}", params);
            JsonObject returnObject = this.callHXSapAPI(erpUrl, params);
            log.debug("SAP删除采购订单出参：{}", returnObject);

            // 处理返回结果
            erpReturnObj = handlePurchaseOrderResponse(returnObject);
        } else {
            // 本地处理
            erpReturnObj.setPurchaseOrderCode("4500002512");
            erpReturnObj.setSuccess("S"); // 默认成功
            erpReturnObj.setReturnMessage("离线模式,不调用SAP接口"); // 默认消息
        }
        return erpReturnObj;
    }

    /**
     * 校验采购订单删除抬头必填字段
     * 包括采购订单号等
     *
     * @param header 采购订单删除抬头信息
     * @throws WmsException 当必填字段为空时抛出异常
     */
    private void validatePurchaseOrderDeleteHeader(HXPurchaseOrderDeleteHeader header) {
        if(header == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "抬头参数不能为空");
        }

        if(UtilString.isNullOrEmpty(header.getPurchaseOrderCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "采购订单号不能为空");
        }
    }

    /**
     * 校验采购订单删除行项目必填字段
     * 包括行项目号等
     *
     * @param items 采购订单删除行项目列表
     * @throws WmsException 当必填字段为空时抛出异常
     */
    private void validatePurchaseOrderDeleteItems(List<HXPurchaseOrderDeleteItem> items) {
        if(CollectionUtils.isEmpty(items)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "行项目不能为空");
        }

        items.forEach(item -> {
            if(UtilString.isNullOrEmpty(item.getItemCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST_WITH_DESC, "行项目号不能为空");
            }
        });
    }

    /**
     * 组装采购订单删除接口参数
     * 将WMS系统的采购订单删除信息转换为SAP接口所需的参数格式
     *
     * @param header 采购订单删除抬头信息
     * @return JsonObject SAP接口参数
     */
    private JsonObject assemblePurchaseOrderDeleteParams(HXPurchaseOrderDeleteHeader header) {
        // 通用输入参数
        JsonObject params = getHeadParams( header.getPurchaseOrderCode(),  header.getPurchaseOrderCode() , EnumReceiptType.PURCHASE_RECEIPT_DELETE.getValue().toString());

        // 抬头参数
        JsonObject headerParams = new JsonObject();
        headerParams.addProperty("EBELN", header.getPurchaseOrderCode()); // 采购订单编号, 固定空值
        // headerParams.addProperty("BSART", header.getPurchaseOrderType()); // 采购订单类型 ， 逻辑在外部处理，值域ZY01-ZY06
        // headerParams.addProperty("BEDAT", UtilDate.convertDateToDateStr(new Date())); // 凭证日期 ， 当前日期
        // headerParams.addProperty("LIFNR", header.getSupplierCode()); // 供应商编码
        // headerParams.addProperty("EKORG", "1104"); // 采购组织
        // headerParams.addProperty("EKGRP", "110"); // 采购组
        // headerParams.addProperty("BUKRS", "1104"); // 公司代码
        // headerParams.addProperty("ZTERM", header.getPaymentTerms()); // 付款条件
        // headerParams.addProperty("KUFIX", ""); // 固定汇率, 固定空值
        // headerParams.addProperty("WAERS", header.getCurrency()); // 货币
        params.add("HEAD", headerParams);

        // 行项目参数
        JsonArray itemParams = new JsonArray();
        header.getItems().forEach(item -> {
            JsonObject itemParam = new JsonObject();
            itemParam.addProperty("EBELP", item.getItemCode()); // 行项目号
            // itemParam.addProperty("MATNR", item.getMatCode()); // 物料编码
            itemParam.addProperty("ELOEK", item.getDeleteFlag()); // 删除标识
            itemParam.addProperty("ELIKZ", item.getReceiptCompletedFlag());
            itemParam.addProperty("WERKS", item.getFtyCode()); // 工厂
            itemParams.add(itemParam);
        });
        params.add("ITEM", itemParams);

        return params;
    }


    /**************************************************************************
     ***************     签名方法，调用sap之前需要调用      **************
     **************************************************************************/

    private static final String CONCAT_KEY = "\n";
    private static final String INPUT_PARAMS = "input_params";
    private static final String SIGNATURE_PARAMS = "signature";
    private static final String SAP_PARAMS = "sap_params";
    private static final String SIGNATURE_CREATED = "signature_created";

    /**
     * 根据请求参数中的key排序，并将对应的value转换为字符串
     *
     * @param requestParams 请求参数
     * @return String 返回转换后的结果
     */
    public String sortToStr(JsonObject requestParams) {
        // 对参数进行排序
        Set<String> paramKeys = requestParams.keySet();
        String[] sortParamKeys = new String[paramKeys.size()];
        Arrays.sort(paramKeys.toArray(sortParamKeys));
        StringBuffer paramValueStr = new StringBuffer();

        // 将请求参数json转换为字符串，用于签名
        for (String paramKey : sortParamKeys) {
            if (SIGNATURE_CREATED.equals(paramKey)) {
                paramValueStr.append(CONCAT_KEY).append(requestParams.get(paramKey).getAsLong());
                continue;
            }
            paramValueStr.append(CONCAT_KEY).append(requestParams.get(paramKey).toString());
        }
        // 删除头部的连接符
        String paramValue = null;
        if (paramValueStr.toString().startsWith(CONCAT_KEY) && paramValueStr.length() >= 1) {
            paramValue = paramValueStr.substring(1);
        }
        return paramValue;
    }

    /**
     * 通过秘钥对字符串进行签名
     *
     * @param params 需要签名的字符串
     * @param secret 签名秘钥（指SAP中间件分配的系统秘钥
     * @return 返回签名后的结果
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws UnsupportedEncodingException
     */
    public String signature(String params, String secret)
            throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        Mac hmacSHA256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256");
        hmacSHA256.init(secretKey);
        return Base64.encodeBase64String(hmacSHA256.doFinal(params.getBytes("UTF-8")));

    }

    /**
     * 构建接口请求体的内容，使用系统当前时间作为时间戳进行构建
     * 
     * @param sapParams  SAP BAPI函数的参数内容
     * @return 返回调用接口的请求体内容
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws UnsupportedEncodingException
     */
    public JsonObject buildRequestParams(JsonObject sapParams)
            throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        // 时间戳
        Long timeStamp = System.currentTimeMillis() / 1000;
        // Long timeStamp = 1629706324L;

        log.debug("   时间戳:{} \n", timeStamp);
        // 接口请求的输入参数
        JsonObject inputParams = new JsonObject();
        inputParams.add(SAP_PARAMS, sapParams);
        // 接口请求参数
        JsonObject requestParams = new JsonObject();
        requestParams.add(INPUT_PARAMS, inputParams);

        requestParams.addProperty(SIGNATURE_CREATED, timeStamp);
        // 将请求参数转换为String,用于签名使用
        String requestParamsStr = sortToStr(requestParams);
        log.debug("1、需要签名的字符串:{} \n", requestParamsStr.replace(CONCAT_KEY, "\n"));
        // 将请求参数进行签名
        String signature = signature(requestParamsStr, UtilConst.getInstance().getSecret());
        // String signature = signature(requestParamsStr, "324687b1df114179bfe7");
        log.debug("2、参数签名结果:{} \n", signature);
        requestParams.addProperty(SIGNATURE_PARAMS, signature);
        log.debug("3、请求参数格式如下:{} \n", requestParams);
        // System.out.printf("3、请求参数格式如下:%s \n", requestParams);
        return requestParams;
    }




    /**
     * 测试验证签名方法是否符合预期
     * 时间戳:1629706324
     * 1、需要签名的字符串:{"sap_params":{"COMPANYCODEID":"2802","NAME":"数科","PRNNUM":200}}
     * 1629706324
     * 2、参数签名结果:sS331PN9yfEq8cE/GkEEv9rJtFiwyh/Cexdv5nFNjPY=
     * 3、请求参数格式如下:{"input_params":{"sap_params":{"COMPANYCODEID":"2802","NAME":"数科","PRNNUM":200}},"signature_created":1629706324,"signature":"sS331PN9yfEq8cE/GkEEv9rJtFiwyh/Cexdv5nFNjPY="}
     * 
     * @param appSecret
     * @throws Exception
     */
    // public static void main(String[] args) throws Exception {
    //     try {
    //         // SAP BAPI 函数对应的参数内容JSON
    //         JsonObject sapParams = new JsonObject();
    //         sapParams.addProperty("COMPANYCODEID", "2802");
    //         sapParams.addProperty("NAME", "数科");
    //         sapParams.addProperty("PRNNUM", 200);

    //         buildRequestParams(sapParams);

    //     } catch (Exception e) {
    //         System.out.println("Error");
    //         e.printStackTrace();
    //     }
    // }


    /**
     * 调用SAP进行物料凭证过账
     */
    public HXPostingReturn postingBySap(JsonObject params, String url) {

        // 调用SAP接口
        String erpUrl = UtilConst.getInstance().getErpUrl() + url;
        log.debug("SAP过账入参：{}", params);
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);
        log.debug("SAP过账出参：{}", returnObject);

        // 处理返回结果
        return handleInvoicePostingResponse(returnObject);
    }


    /**
     * 处理过账返回结果
     */
    public HXPostingReturn handleInvoicePostingResponse(JsonObject returnObject) {
        HXPostingReturn erpReturnObj = new HXPostingReturn();
        if (returnObject != null) {

            checkPostingResponse(returnObject);

            this.handleSapResponse(returnObject, "发票预制");
            // 返回状态
            erpReturnObj.setSuccess(UtilObject.getStringOrEmpty(returnObject.get("TYPE")));
            JsonObject data = returnObject.get("data").getAsJsonObject();
            if (data.get("RETURN") instanceof JsonObject) {
                erpReturnObj.setReturnMessage(data.get("RETURN").getAsJsonObject().get("MESSAGE").getAsString());

                erpReturnObj.setSuccess(data.get("RETURN").getAsJsonObject().get("TYPE").getAsString());

            } else if (data.get("RETURN") instanceof JsonArray) {
                JsonArray jsonArray = data.get("RETURN").getAsJsonArray();
                if (!jsonArray.isJsonNull() && jsonArray.size() > 0) {
                    erpReturnObj.setReturnMessage(jsonArray.get(0).getAsJsonObject().get("MESSAGE").getAsString());
                    erpReturnObj.setSuccess(jsonArray.get(0).getAsJsonObject().get("TYPE").getAsString());
                }
            }
            // 发票凭证编号
            if (UtilObject.isNotNull(data.get("INVOICEDOCNUMBER"))) {
                erpReturnObj.setInvoiceDocCode(data.get("INVOICEDOCNUMBER").getAsString());
            }
            // 发票凭证年度
            if (UtilObject.isNotNull(data.get("FISCALYEAR"))) {
                erpReturnObj.setInvoiceDocYear(data.get("FISCALYEAR").getAsString());
            }
        } else {
            erpReturnObj.setSuccess("E");
            erpReturnObj.setReturnMessage("SAP返回结果为空");
        }

        return erpReturnObj;
    }

}

