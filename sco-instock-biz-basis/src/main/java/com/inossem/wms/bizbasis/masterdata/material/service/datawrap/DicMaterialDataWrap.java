package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;

import java.util.List;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.material.dao.DicMaterialMapper;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialListVO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;

/**
 * <p>
 * 物料描述表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Service
public class DicMaterialDataWrap extends BaseDataWrap<DicMaterialMapper, DicMaterial> {

    /**
     * 物料列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicMaterialPageVO> getDicMaterialPageVOList(IPage<DicMaterialPageVO> page, QueryWrapper<DicMaterialSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicMaterialPageVOList(page, wrapper));
    }

    /**
     * 物料列表
     *
     * @param wrapper 查询条件
     * @return List<DicMaterialListVO>
     */
    public List<DicMaterialListVO> getDicMaterialVOList(QueryWrapper<DicMaterialSearchPO> wrapper) {
        return this.baseMapper.selectDicMaterialVOList(wrapper);
    }

    /**
     * 物料列表
     *
     * @param wrapper 查询条件  新增条件 receiptCode
     * @return List<DicMaterialListVO>
     */
    public List<DicMaterialListVO> getDicMaterialVOListByReceiptCode(QueryWrapper<DicMaterialSearchPO> wrapper) {
        return this.baseMapper.selectDicMaterialVOListByReceiptCode(wrapper);
    }

    public List<DicMaterial> findByName(String name) {
        QueryWrapper<DicMaterial> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DicMaterial::getIsDelete, EnumRealYn.FALSE.getIntValue()).like(DicMaterial::getMatName, name).last("limit 30");
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 需求计划物料分页查询
     * 
     * @param page 分页对象
     * @param matGroupId 物料组ID
     * @param matCode 物料编码
     * @param matName 物料名称
     * @return 分页结果
     */
    public IPage<DicMaterialListVO> getDemandPlanMaterialPage(
            IPage<DicMaterialListVO> page,
            String matGroupName,
            String matCode,
            String matName) {
        return baseMapper.getDemandPlanMaterialPage(page, matGroupName, matCode, matName);
    }
}
