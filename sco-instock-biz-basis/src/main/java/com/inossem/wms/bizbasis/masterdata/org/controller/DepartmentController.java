package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.bizbasis.masterdata.org.service.biz.DepartmentService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 部门主数据Controller
 *
 * <AUTHOR>
 * @date 2022/05/07 10:08
 **/
@RestController
@Api(tags = "主数据-部门管理")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 部门主数据-查询
     *
     * @param po  MetaDataDeptOfficePO
     * @param ctx 入参上下文 {"po":"保存工器具入库表单参数"}
     */
    @ApiOperation(value = "获取部门列表", tags = {"部门主数据管理"})
    @PostMapping(value = "/dept-master-data/dept/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicDeptDTO>> getResult(@RequestBody MetaDataDeptOfficePO po, BizContext ctx) {
        departmentService.getResult(ctx);
        PageObjectVO<DicDeptDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询部门类型
     *
     * @return 部门类型列表
     *
     */
    @ApiOperation(value = "查询部门类型", tags = {"部门主数据管理"}, notes = "部门主数据管理")
    @GetMapping(path = "/dept-master-data/dept/type", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicDeptDTO>> getDeptType() {
        return BaseResult.success(departmentService.getDeptType());
    }

    /**
     * 查询所有部门
     *
     * @return 查询所有部门
     *
     */
    @ApiOperation(value = "查询所有部门", tags = {"部门主数据管理"}, notes = "部门主数据管理")
    @GetMapping(path = "/dept-master-data/dept/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicDept>> getAllDept() {
        return BaseResult.success(departmentService.getAllDept());
    }


    /**
     * 部门主数据-详情
     *
     * @param id 公司Id
     * @return 公司详情
     */
    @ApiOperation(value = "按照部门id查找部门", tags = {"部门主数据管理"})
    @GetMapping(path = "/dept-master-data/dept/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicDeptDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(departmentService.get(ctx));
    }

    /**
     * 部门主数据-新增部门
     *
     * @param po  保存工器具入库表单参数
     * @param ctx 入参上下文 {"po":"保存工器具入库表单参数"}
     */
    @ApiOperation(value = "新增部门", tags = {"部门主数据管理"})
    @PostMapping(value = "/dept-master-data/dept", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody MetaDataDeptOfficePO po, BizContext ctx) {
        departmentService.addOrUpdate(ctx);
        return BaseResult.success();
    }

    /**
     * 部门主数据-修改部门
     *
     * @param po  保存工器具入库表单参数
     * @param ctx 入参上下文 {"po":"保存工器具入库表单参数"}
     */
    @ApiOperation(value = "修改部门", tags = {"部门主数据管理"})
    @PutMapping(value = "/dept-master-data/dept", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> update(@RequestBody MetaDataDeptOfficePO po, BizContext ctx) {
        departmentService.addOrUpdate(ctx);
        return BaseResult.success();
    }

}
