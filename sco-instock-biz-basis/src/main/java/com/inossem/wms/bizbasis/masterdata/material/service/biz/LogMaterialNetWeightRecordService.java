package com.inossem.wms.bizbasis.masterdata.material.service.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.LogMaterialNetWeightRecordDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.LogMaterialNetWeightRecordDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.mat.info.entity.LogMaterialNetWeightRecord;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.LogMaterialNetWeightRecordVO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <p>
 * 物料净重变更记录 service
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
@Service
@Slf4j
public class LogMaterialNetWeightRecordService {
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private LogMaterialNetWeightRecordDataWrap logMaterialNetWeightRecordDataWrap;

    /**
     * 物料净重变更记录-分页
     *
     * @param ctx 入参上下文 {@link LogMaterialNetWeightRecordSearchPO : 物料净重变更记录查询对象}
     */
    public void getPage(BizContext ctx) {
        // 入参上下文
        LogMaterialNetWeightRecordSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 组装查询条件
        WmsQueryWrapper<LogMaterialNetWeightRecordSearchPO> pageWrapper = this.setQueryWrapper(po);
        // 分页处理
        IPage<LogMaterialNetWeightRecordVO> page = po.getPageObj(LogMaterialNetWeightRecordVO.class);
        // 获取物料净重变更记录分页数据
        logMaterialNetWeightRecordDataWrap.getLogMaterialNetWeightRecordList(page, pageWrapper);

        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 验证物料主数据重量容差
     *
     * @param ctx 入参上下文 {@link DicMaterialSearchPO : 入参}
     */
    public void checkWeightTolerance(BizContext ctx) {
        // 入参上下文
        DicMaterialSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isEmpty(po.getMatId())
                || UtilNumber.isEmpty(po.getNetWeight())
                || UtilNumber.isEmpty(po.getWeightTolerance())
                || UtilNumber.isEmpty(po.getVariableWeight())
                || UtilNumber.isEmpty(po.getSubmitQty())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(po.getMatId());
        if (UtilObject.isNull(dicMaterialDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_ALERT_NOT_EXIST, dicMaterialDTO.getMatCode());
        }

        // 源标准重量
        BigDecimal sourceNetWeight = po.getNetWeight();
        // 新标准重量【变化重量/作业数量】
        BigDecimal targetNetWeight = po.getVariableWeight().divide(po.getSubmitQty(), BigDecimal.ROUND_HALF_UP);
        // 【((|新标准重量 - 源标准重量|) / 源标准重量) * 100 <= 重量容差】
        boolean flag = (((targetNetWeight.subtract(sourceNetWeight).abs()).divide(sourceNetWeight, BigDecimal.ROUND_HALF_UP)).multiply(new BigDecimal("100"))).compareTo(po.getWeightTolerance()) <= BigDecimal.ROUND_UP;

        if (!flag) {
            dicMaterialDTO.setIsBeyondWeightTolerance(Boolean.TRUE);
            dicMaterialDTO.setNetWeight(targetNetWeight);
        } else {
            dicMaterialDTO.setIsBeyondWeightTolerance(Boolean.FALSE);
        }

        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, dicMaterialDTO);
    }

    /**
     * 根据物料主数据净重、新增净重变更记录
     *
     * @param ctx 入参上下文 {@link DicMaterialDTO : "物料主数据"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateNetWeight(BizContext ctx) {
        // 入参上下文
        DicMaterialDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilNumber.isEmpty(po.getId())
                || UtilNumber.isEmpty(po.getNetWeight())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicMaterialDTO dicMaterialDTO = dictionaryService.getMatCacheById(po.getId());
        if (UtilObject.isNull(dicMaterialDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_NOT_EXIST, dicMaterialDTO.getMatCode());
        }

        // 更新物料主数据
        materialService.updateMaterial(po);
        // 保存重量容差记录
        this.saveLogMaterialNetWeightRecord(new LogMaterialNetWeightRecordDTO().setMatId(po.getId()).setNetWeight(po.getNetWeight()).setCreateUserId(user.getId()));
    }

    /**
     * 物料净重变更记录-保存
     *
     * @param ctx 入参上下文 {@link LogMaterialNetWeightRecordDTO : 物料净重变更记录}
     */
    public void save(BizContext ctx) {
        // 入参上下文
        LogMaterialNetWeightRecordDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存物料净重变更记录
        this.saveLogMaterialNetWeightRecord(po.setCreateUserId(user.getId()));
    }

    /**
     * 保存物料净重变更记录
     *
     * @param logMaterialNetWeightRecordDTO 物料净重变更记录
     */
    private void saveLogMaterialNetWeightRecord(LogMaterialNetWeightRecordDTO logMaterialNetWeightRecordDTO) {
        if (UtilObject.isNotNull(logMaterialNetWeightRecordDTO)) {
            logMaterialNetWeightRecordDataWrap.saveDto(logMaterialNetWeightRecordDTO);
        }
    }

    /**
     * 设置列表查询条件
     *
     * @param po 查询条件
     * @return WmsQueryWrapper<LogMaterialNetWeightRecordSearchPO>
     */
    public WmsQueryWrapper<LogMaterialNetWeightRecordSearchPO> setQueryWrapper(LogMaterialNetWeightRecordSearchPO po) {
        if (null == po) {
            po = new LogMaterialNetWeightRecordSearchPO();
        }

        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getStartTime());
        }
        if (UtilObject.isNotNull(po.getEndTime())) {
            endTime = UtilLocalDateTime.getEndTime(po.getEndTime());
        }

        WmsQueryWrapper<LogMaterialNetWeightRecordSearchPO> wrapper = new WmsQueryWrapper<>();
        // 物料编码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()), LogMaterialNetWeightRecordSearchPO::getMatCode, DicMaterial.class, po.getMatCode());
        // 时间段
        wrapper.lambda().between((UtilObject.isNotNull(startTime)), LogMaterialNetWeightRecordSearchPO::getCreateTime, LogMaterialNetWeightRecord.class, startTime, endTime);
        return wrapper;
    }
}
