package com.inossem.wms.bizbasis.masterdata.wbs.service.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.wbs.service.datawrap.DicWbsDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.wbs.entity.DicWbs;
import com.inossem.wms.common.model.masterdata.wbs.po.DicWbsSearchPO;
import com.inossem.wms.common.util.UtilString;

import lombok.extern.slf4j.Slf4j;

/**
 * WBS主数据管理
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Slf4j
@Component
public class DicWbsComponent {

    @Autowired
    protected DicWbsDataWrap dicWbsDataWrap;

    @Autowired
    protected HXSapIntegerfaceService hxSapIntegerfaceService;

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 获取查询条件
        DicWbsSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        // 构建查询条件
        QueryWrapper<DicWbs> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getWbsCode()), DicWbs::getWbsCode, po.getWbsCode())
            .like(UtilString.isNotNullOrEmpty(po.getWbsName()), DicWbs::getWbsName, po.getWbsName())
            .eq(UtilString.isNotNullOrEmpty(po.getLocation()), DicWbs::getLocation, po.getLocation());
            
        // 执行查询
        IPage<DicWbs> page = dicWbsDataWrap.page(po.getPageObj(DicWbs.class), queryWrapper);
        
        // 设置返回结果
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 同步WBS
     */
    public void sync(BizContext ctx) {
        LogMaterialNetWeightRecordSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("开始同步WBS数据");
        hxSapIntegerfaceService.synWBSInfo(po.getStartTime(), po.getEndTime());
        log.info("WBS数据同步完成");
    }
} 