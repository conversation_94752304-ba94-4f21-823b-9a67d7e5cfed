package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.location.po.DicStockLocationSavePO;
import com.inossem.wms.common.model.org.location.po.DicStockLocationSearchPO;
import com.inossem.wms.common.model.org.location.vo.DicStockLocationPageVO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.StockLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 库存地点主数据表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@RestController
@Api(tags = "库存地点管理")
public class StockLocationController {

    @Autowired
    private StockLocationService stockLocationService;

    /**
     * 获取库存地点列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 库存地点集合列表
     */
    @ApiOperation(value = "获取库存地点列表", tags = {"库存地点管理"})
    @PostMapping(path = "/org/location/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicStockLocationPageVO>> getPage(@RequestBody DicStockLocationSearchPO po, BizContext ctx) {
        return BaseResult.success(stockLocationService.getPage(ctx));
    }

    /**
     * 查看库存地点详情
     * 
     * <AUTHOR>
     * @param id 库存地点Id
     * @return 库存地点详情
     */
    @ApiOperation(value = "按照库存地点id查找库存地点", tags = {"库存地点管理"})
    @GetMapping(path = "/org/location/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicStockLocationDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(stockLocationService.get(ctx));
    }

    /**
     * 新增库存地点
     * 
     * <AUTHOR>
     * @param po 库存地点入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增库存地点信息", notes = "对库存地点信息进行添加、修改", tags = {"库存地点管理"})
    @PostMapping(path = "/org/location", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicStockLocationSavePO po, BizContext ctx) {
        // 储存存地点信息
        stockLocationService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_SAVE_SUCCESS, po.getLocationInfo().getLocationCode());
    }

    /**
     * 修改库存地点
     *
     * <AUTHOR>
     * @param po 库存地点入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改库存地点信息", notes = "对库存地点信息进行添加、修改", tags = {"库存地点管理"})
    @PutMapping(path = "/org/location", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicStockLocationSavePO po, BizContext ctx) {
        // 储存存地点信息
        stockLocationService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_SAVE_SUCCESS, po.getLocationInfo().getLocationCode());
    }

    /**
     * 删除库存地点
     * 
     * @param id 库存地点Id
     * <AUTHOR>
     * @param ctx 上下文对象
     * @return 删除结果
     */
    @ApiOperation(value = "按照库存地点编码删除库存地点", notes = "逻辑删除", tags = {"库存地点管理"})
    @DeleteMapping(path = "/org/location/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除库存地点信息
        String locationCode = stockLocationService.remove(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_DELETE_SUCCESS, locationCode);
    }

    /**
     * 库存地点导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "库存地点导入", notes = "库存地点导入", tags = {"库存地点管理"})
    @PostMapping(path = "/org/location/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importLocation(@RequestPart("file") MultipartFile file, BizContext ctx) {
        // 导入库存地点信息
        stockLocationService.importLocation(ctx);
        return BaseResult.success();
    }

}
