package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicStockLocationMapper;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.model.org.location.po.DicStockLocationFreeSearchPO;
import com.inossem.wms.common.model.org.location.po.DicStockLocationSearchPO;
import com.inossem.wms.common.model.org.location.vo.DicStockLocationPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 库存地点主数据表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class DicStockLocationDataWrap extends BaseDataWrap<DicStockLocationMapper, DicStockLocation> {

    /**
     * 根据用户id查询库存地点
     *
     * @param userId
     * @return java.util.List<com.inossem.wms.common.model.org.location.entity.DicStockLocation>
     * @date 2021/3/15 11:00
     * <AUTHOR>
     */
    public List<DicStockLocation> getLocationByUserId(Long userId) {
        return baseMapper.getLocationByUserId(userId);
    }

    /**
     *根据选择工厂获取当前登录人库存地点信息
     * @param po
     * @return
     */
    public List<DicStockLocation> getLocationByFtyId(DicStockLocationFreeSearchPO po) {
        return baseMapper.getLocationByFtyId(po);
    }


    /**
     * 库存地点列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicStockLocationPageVO> getDicStockLocationPageVOList(IPage<DicStockLocationPageVO> page,
        QueryWrapper<DicStockLocationSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicStockLocationPageVOList(page, wrapper));
    }

    /**
     * 获取全部库存地点
     *
     * @return List<DicStockLocationDTO>
     */
    public List<DicStockLocationDTO> getAll() {
        return this.baseMapper.getAll();
    }
}
