package com.inossem.wms.bizbasis.erp.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.sap.SapConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.util.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

public class SapInterfaceUtil {

    /**
     * 获取请求报文  模拟接口  真实接口通用
     * @param postingItem
     * @return
     */
   public static JSONObject getRequestParams (String postingItem){
       JSONObject params = new JSONObject();
       List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
       JSONObject firstObject =jsonObjectList.get(0);
       Integer receiptType=  firstObject.getInteger("receiptType");
       if(UtilNumber.isNotEmpty(receiptType)) {
            if(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue().equals(receiptType)){
                 params=getUnitizedArrivalRegisterParams(postingItem); //成套设备到货登记
            }else if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue().equals(receiptType)){
                JSONObject inspectInputItem=firstObject.getJSONObject("inspectInputItem");
                if(UtilObject.isNotNull(inspectInputItem)){
                    params=getUnitizedStockInputInspectParams(postingItem);//成套设备验收入库
                }
            }else if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(receiptType)){
                JSONObject inspectInputItem=firstObject.getJSONObject("inspectInputItem");
                if(UtilObject.isNotNull(inspectInputItem)){
                    params=getUnitizedStockInputInspectWriteOffParams(postingItem);//成套设备验收入库冲销
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NOTICE.getValue().equals(receiptType)){
                JSONObject numberInconformityItem=firstObject.getJSONObject("numberInconformityItem");
                if(UtilObject.isNotNull(numberInconformityItem)){
                    params=getUnitizedInconformityNumberNoticeParams(postingItem);//成套设备数量差异通知
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NAINTAIN.getValue().equals(receiptType)){
                JSONObject numberInconformityItem=firstObject.getJSONObject("numberInconformityItem");
                if(UtilObject.isNotNull(numberInconformityItem)){
                    params=getUnitizedInconformityNumberMaintainParams(postingItem);//成套设备数量差异处置
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue().equals(receiptType)){
                JSONObject qualifiedInconformityItem=firstObject.getJSONObject("qualifiedInconformityItem");
                if(UtilObject.isNotNull(qualifiedInconformityItem)){
                    params=getUnitizedInconformityNoticeParams(postingItem); //成套设备不符合项通知
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue().equals(receiptType)){
                JSONObject qualifiedInconformityItem=firstObject.getJSONObject("qualifiedInconformityItem");
                if(UtilObject.isNotNull(qualifiedInconformityItem)){
                    params=getUnitizedInconformityMaintainParams(postingItem);//成套设备不符合项处置
                }
            }else if (EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ.getValue().equals(receiptType)){
                params=getUnitizedStockReturnMatReqParams(postingItem);//成套设备领料退库
            }else if (EnumReceiptType.ARRIVAL_REGISTER.getValue().equals(receiptType)){
                 params=getArrivalRegisterParams(postingItem);//到货登记
            }else if (EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType)){
                params=getStockInputInspectParams(postingItem);//验收入库
            }else if (EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(receiptType)){
                params=getStockInputInspectWriteOffParams(postingItem);//验收入库-冲销
            }else if (EnumReceiptType.INCONFORMITY_NAINTAIN.getValue().equals(receiptType)){
                params=getInconformityNaintainParams(postingItem);//不符合项处置
            }else if (EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue().equals(receiptType)){
                params=getStockOutputPurchaseReturnParams(postingItem);//采购退货单
            }else if (EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue().equals(receiptType)){
                params=getStockReturnMatReqParams(postingItem);//领料退库单
            }else if (EnumReceiptType.STOCK_RETURN_TRANSFER.getValue().equals(receiptType)){
                params=getStockReturnTransferParams(postingItem);//退转库入库
            }else if (EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue().equals(receiptType)){
                params=getUnitizedStockReturnOldInputParams(postingItem);//退旧入库
            }else if (EnumReceiptType.LEISURE_INPUT.getValue().equals(receiptType)){
                params=getUnitizedStockLeisureInputParams(postingItem);//闲置入库
            }else if (EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue().equals(receiptType)){
                params=getStockOutputScrapParams(postingItem);//报废出库单
            }else if (EnumReceiptType.STOCK_TRANSPORT.getValue().equals(receiptType)){
                params=getStockTransportParams(postingItem);//转储
            }else if (EnumReceiptType.STOCK_TRANSFER.getValue().equals(receiptType)){
                params=getStockTransferParams(postingItem);//转性
            }else if (EnumReceiptType.STOCK_TRANSPORT_MAT.getValue().equals(receiptType)){
                params=getStockTransportMatParams(postingItem);//物料转码
            }else if (EnumReceiptType.STOCK_FREEZE_SCRAP.getValue().equals(receiptType)) {
                params=getStockFreezeScrapParams(postingItem);//报废冻结
            } else if (EnumReceiptType.UNITIZED_STOCK_UNFREEZE_SCRAP.getValue().equals(receiptType)){
                params=getUnitizedStockUnFreezeScrapParams(postingItem);//成套解冻
            } else if (EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue().equals(receiptType)){
                params=getUnitizedStockFreezeScrapParams(postingItem);//成套冻结
            }else if (EnumReceiptType.STOCK_FREEZE_EXPIRE.getValue().equals(receiptType)){
                params=getStockFreezeExpireParams(postingItem);//过期冻结
            }else if (EnumReceiptType.UNITIZED_STOCK_TRANSPORT.getValue().equals(receiptType)){
                params=getUnitizedStockTransportParams(postingItem);//成套转储
            }else if (EnumReceiptType.UNITIZED_STOCK_TRANSFER.getValue().equals(receiptType)){
                params=getUnitizedStockTransferParams(postingItem);//成套转性
            }else if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(receiptType)){
                params=SapInterfaceOtherUtil.getSynCreateStockBinInfoParams(postingItem);//领料出库
            }else if (EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue().equals(receiptType)){
                params=SapInterfaceOtherUtil.getSynCreateStockBinInfoParams(postingItem);//成套领料出库
            }
       }else{
           //成套设备相关
           JSONObject arrivalRegisterItem=firstObject.getJSONObject("arrivalRegisterItem");
           JSONObject inspectInputItem=firstObject.getJSONObject("inspectInputItem");
           JSONObject numberInconformityItem=firstObject.getJSONObject("numberInconformityItem");
           JSONObject qualifiedInconformityItem=firstObject.getJSONObject("qualifiedInconformityItem");
           if(UtilObject.isNotNull(arrivalRegisterItem) &&  arrivalRegisterItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())){
               params=getUnitizedArrivalRegisterParams(postingItem); //成套设备到货登记  冲销
           }else if(UtilObject.isNotNull(inspectInputItem) &&  inspectInputItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())){
               params=getUnitizedStockInputInspectParams(postingItem);//成套设备验收入库
           }else if(UtilObject.isNotNull(inspectInputItem) &&  inspectInputItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())){
               params=getUnitizedStockInputInspectWriteOffParams(postingItem);//成套设备验收入库 冲销
           }else if(UtilObject.isNotNull(numberInconformityItem) &&  numberInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NOTICE.getValue())){
               params=getUnitizedInconformityNumberNoticeParams(postingItem);//成套设备数量差异通知
           }else if(UtilObject.isNotNull(numberInconformityItem) &&  numberInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NAINTAIN.getValue())){
               params=getUnitizedInconformityNumberMaintainParams(postingItem);//成套设备数量差异处置
           }else if(UtilObject.isNotNull(qualifiedInconformityItem) &&  qualifiedInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())){
               params=getUnitizedInconformityNoticeParams(postingItem); //成套设备不符合项通知
           }else if(UtilObject.isNotNull(qualifiedInconformityItem) &&  qualifiedInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())){
               params=getUnitizedInconformityMaintainParams(postingItem);//成套设备不符合项处置
           }
       }
       return params;
   }


    /**
     * 解析返回结果
     * @param postingItem
     * @param erpReturnObj
     * @param returnObject
     */
    public static void delRespondObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject ){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer receiptType=  firstObject.getInteger("receiptType");
        if(UtilNumber.isNotEmpty(receiptType)) {
            if(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue().equals(receiptType)){
                delUnitizedArrivalRegisterObject(postingItem,erpReturnObj,returnObject); //成套设备到货登记
            }else if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue().equals(receiptType)){
                JSONObject inspectInputItem=firstObject.getJSONObject("inspectInputItem");
                if(UtilObject.isNotNull(inspectInputItem)){
                    delUnitizedStockInputInspectObject(postingItem,erpReturnObj,returnObject);//成套设备验收入库
                }
            }else if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(receiptType)){
                JSONObject inspectInputItem=firstObject.getJSONObject("inspectInputItem");
                if(UtilObject.isNotNull(inspectInputItem)){
                    delUnitizedStockInputInspectWriteOffObject(postingItem,erpReturnObj,returnObject);//成套设备验收入库冲销
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NOTICE.getValue().equals(receiptType)){
                JSONObject numberInconformityItem=firstObject.getJSONObject("numberInconformityItem");
                if(UtilObject.isNotNull(numberInconformityItem)){
                    delUnitizedInconformityNumberNoticeObject(postingItem,erpReturnObj,returnObject);//成套设备数量差异通知
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NAINTAIN.getValue().equals(receiptType)){
                JSONObject numberInconformityItem=firstObject.getJSONObject("numberInconformityItem");
                if(UtilObject.isNotNull(numberInconformityItem)){
                    delUnitizedInconformityNumberMaintainObject(postingItem,erpReturnObj,returnObject);//成套设备数量差异处置
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue().equals(receiptType)){
                JSONObject qualifiedInconformityItem=firstObject.getJSONObject("qualifiedInconformityItem");
                if(UtilObject.isNotNull(qualifiedInconformityItem)){
                    delUnitizedInconformityNoticeObject(postingItem,erpReturnObj,returnObject); //成套设备不符合项通知
                }
            }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue().equals(receiptType)){
                JSONObject qualifiedInconformityItem=firstObject.getJSONObject("qualifiedInconformityItem");
                if(UtilObject.isNotNull(qualifiedInconformityItem)){
                    delUnitizedInconformityMaintainObject(postingItem,erpReturnObj,returnObject);//成套设备不符合项处置
                }
            }else if (EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ.getValue().equals(receiptType)){
                delUnitizedStockReturnMatReqObject(postingItem,erpReturnObj,returnObject);//成套设备领料退库
            }else if (EnumReceiptType.ARRIVAL_REGISTER.getValue().equals(receiptType)){
                delArrivalRegisterObject(postingItem,erpReturnObj,returnObject);//到货登记
            }else if (EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType)){
                delStockInputInspectObject(postingItem,erpReturnObj,returnObject);//验收入库
            }else if (EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(receiptType)){
                delStockInputInspectWriteOffObject(postingItem,erpReturnObj,returnObject);//验收入库-冲销
            }else if (EnumReceiptType.INCONFORMITY_NAINTAIN.getValue().equals(receiptType)){
                delInconformityNaintainObject(postingItem,erpReturnObj,returnObject);//不符合项处置
            }else if (EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue().equals(receiptType)){
                delStockOutputPurchaseReturnObject(postingItem,erpReturnObj,returnObject);//采购退货单
            }else if (EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue().equals(receiptType)){
                delStockReturnMatReqObject(postingItem,erpReturnObj,returnObject);//领料退库单
            }else if (EnumReceiptType.STOCK_RETURN_TRANSFER.getValue().equals(receiptType)){
                delStockReturnTransferObject(postingItem,erpReturnObj,returnObject);//退转库入库
            }else if (EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue().equals(receiptType)){
                delStockReturnOldObject(postingItem,erpReturnObj,returnObject);//退旧入库
            }else if (EnumReceiptType.LEISURE_INPUT.getValue().equals(receiptType)){
                delStockLeisureInputObject(postingItem,erpReturnObj,returnObject);//闲置入库
            }else if (EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue().equals(receiptType)){
                delStockOutputScrapObject(postingItem,erpReturnObj,returnObject);//报废出库单
            }else if (EnumReceiptType.STOCK_TRANSPORT.getValue().equals(receiptType)){
                delStockTransportObject(postingItem,erpReturnObj,returnObject);//转储
            }else if (EnumReceiptType.STOCK_TRANSFER.getValue().equals(receiptType)){
                delStockTransferObject(postingItem,erpReturnObj,returnObject);//转性
            }else if (EnumReceiptType.STOCK_TRANSPORT_MAT.getValue().equals(receiptType)){
                delStockTransportMatObject(postingItem,erpReturnObj,returnObject);//物料转码
            }else if (EnumReceiptType.STOCK_FREEZE_SCRAP.getValue().equals(receiptType)){
                delStockFreezeScrapObject(postingItem,erpReturnObj,returnObject);//报废冻结
            }else if (EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue().equals(receiptType)){
                delUnitizedStockFreezeObject(postingItem,erpReturnObj,returnObject);//成套设备物资冻结
            }else if (EnumReceiptType.UNITIZED_STOCK_UNFREEZE_SCRAP.getValue().equals(receiptType)){
                delUnitizedStockUnFreezeObject(postingItem,erpReturnObj,returnObject);//成套设备物资解冻
            }else if (EnumReceiptType.STOCK_FREEZE_EXPIRE.getValue().equals(receiptType)){
                delStockFreezeExpireObject(postingItem,erpReturnObj,returnObject);//过期冻结
            }else if (EnumReceiptType.UNITIZED_STOCK_TRANSPORT.getValue().equals(receiptType)){
                delUnitizedStockTransportObject(postingItem,erpReturnObj,returnObject);//成套转储
            }else if (EnumReceiptType.UNITIZED_STOCK_TRANSFER.getValue().equals(receiptType)){
                delUnitizedStockTransferObject(postingItem,erpReturnObj,returnObject);//成套转性
            }
        }else{
            JSONObject arrivalRegisterItem=firstObject.getJSONObject("arrivalRegisterItem");
            JSONObject inspectInputItem=firstObject.getJSONObject("inspectInputItem");
            JSONObject numberInconformityItem=firstObject.getJSONObject("numberInconformityItem");
            JSONObject qualifiedInconformityItem=firstObject.getJSONObject("qualifiedInconformityItem");
            if(UtilObject.isNotNull(arrivalRegisterItem) &&  arrivalRegisterItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue())){
                delUnitizedArrivalRegisterObject(postingItem,erpReturnObj,returnObject); //成套设备到货登记
            }else if(UtilObject.isNotNull(inspectInputItem) &&  inspectInputItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())){
                delUnitizedStockInputInspectObject(postingItem,erpReturnObj,returnObject);//成套设备验收入库
            }else if(UtilObject.isNotNull(inspectInputItem) &&  inspectInputItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue())){
                delUnitizedStockInputInspectWriteOffObject(postingItem,erpReturnObj,returnObject);//成套设备验收入库 冲销
            }else if(UtilObject.isNotNull(numberInconformityItem) &&  numberInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NOTICE.getValue())){
                delUnitizedInconformityNumberNoticeObject(postingItem,erpReturnObj,returnObject);//成套设备数量差异通知
            }else if(UtilObject.isNotNull(numberInconformityItem) &&  numberInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NAINTAIN.getValue())){
                delUnitizedInconformityNumberMaintainObject(postingItem,erpReturnObj,returnObject);//成套设备数量差异处置
            }else if(UtilObject.isNotNull(qualifiedInconformityItem) &&  qualifiedInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())){
                delUnitizedInconformityNoticeObject(postingItem,erpReturnObj,returnObject); //成套设备不符合项通知
            }else if(UtilObject.isNotNull(qualifiedInconformityItem) &&  qualifiedInconformityItem.getInteger("receiptType").equals(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue())){
                delUnitizedInconformityMaintainObject(postingItem,erpReturnObj,returnObject);//成套设备不符合项处置
            }
        }
    }


    /**
     *  到货登记
     * @param postingItem
     * @return
     */
    public static JSONObject getArrivalRegisterParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", item.getString("rid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
                // 104 - 冲销
            if ( UtilString.isNotNullOrEmpty(item.getString("matDocCode"))){
                sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                sapItem.put("ENTRY_QNT", item.getString("writeOffQty")); // 数量
                sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
            }else{
                sapItem.put("MOVE_TYPE", "103"); // 移动类型
            }
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  验收入库
     * @param postingItem
     * @return
     */
    public static JSONObject getStockInputInspectParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", item.getString("rid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
            sapItem.put("MOVE_TYPE", "105"); // 移动类型
            sapItem.put("REF_DOC_YR", item.getIntValue("preMatDocYear")); // 参考凭证年度
            sapItem.put("REF_DOC", item.getString("preMatDocCode")); // 参考凭证号
            sapItem.put("REF_DOC_IT", item.getIntValue("preMatDocRid")); // 参考凭证行项目号
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  验收入库-冲销
     * @param postingItem
     * @return
     */
    public static JSONObject getStockInputInspectWriteOffParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        if (UtilString.isNotNullOrEmpty(jsonObjectList.get(0).getString("writeOffMatDocCode"))) {
            paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("deliveryWriteOffPostingDate")); // 冲销-过账日期
            paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("deliveryWriteOffDocDate")); // 冲销-凭证日期
        }
        else {
            paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("writeOffPostingDate")); // 冲销-过账日期
            paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("writeOffDocDate")); // 冲销-凭证日期
        }
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("preReceiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("preReceiptRid")); // istock行号
            sapItem.put("ZHXH", item.getString("preReceiptRid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
            // // 104 - 冲销
            if ( UtilString.isNotNullOrEmpty(item.getString("writeOffMatDocCode"))) {
                sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
                sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear103")); // 参考凭证年度
                sapItem.put("REF_DOC", item.getString("matDocCode103")); // 参考凭证号
                sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid103")); // 参考凭证行项目号
            }  else    {
            // 验收入库 106
                sapItem.put("MOVE_TYPE", "106"); // 移动类型
                sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear103")); // 参考凭证年度
                sapItem.put("REF_DOC", item.getString("matDocCode103")); // 参考凭证号
                sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid103")); // 参考凭证行项目号
            }
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  不符合项处置
     * @param postingItem
     * @return
     */
    public static JSONObject getInconformityNaintainParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();

        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("writeOffPostingDate")); // 冲销-过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("writeOffDocDate")); // 冲销-凭证日期

        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", item.getString("rid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
            // 104 - 冲销
            sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
            sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
            sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
            sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  采购退货单
     * @param postingItem
     * @return
     */
    public static JSONObject getStockOutputPurchaseReturnParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            // json转list
            JSONArray jsonArray = item.getJSONArray("binDTOList");
            if(jsonArray!=null){
                for(int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject sapItem = new JSONObject();
                    sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                    sapItem.put("ZHXH", jsonObject.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", jsonObject.getJSONObject("batchInfo").getString("batchCode")); // 批次号
                    sapItem.put("MOVE_TYPE", "101"); // 移动类型
                    sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                    sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                    sapItem.put("SPEC_STOCK", jsonObject.getJSONObject("batchInfo").getString("specStock")); // 特殊库存标识
                    sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                    sapItem.put("ENTRY_QNT", jsonObject.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                    sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                    sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                    sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                    sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                    sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                    sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                    sapItem.put("RESERV_NO",  item.getString("reserveReceiptCode")); // 预留/相关需求的编号
                    sapItem.put("RES_ITEM", item.getString("reserveReceiptRid")); // 预留/相关需求的项目编号
                    sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                    sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                    sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                    sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                    sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                    sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                    sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                    sapItem.put("WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    paramsOfItem.add(sapItem);
                }
            }
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  领料退库单
     * @param postingItem
     * @return
     */
    public static JSONObject getStockReturnMatReqParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type =SapConst.TYPE_TWO;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本
        if(StringUtils.isNotEmpty(firstObject.getString("receiptNum"))){
            paramsOfHeader.put("HEADER_TXT",firstObject.getString("receiptNum"));
        }

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            // json转list
            JSONArray jsonArray = item.getJSONArray("itemInfoList");
            if(jsonArray!=null){
                for(int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject sapItem = new JSONObject();
                    sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                    sapItem.put("ZHXH", jsonObject.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", jsonObject.getJSONObject("batchInfo").getString("batchCode")); // 批次号
                    sapItem.put("MOVE_TYPE", Const.STRING_EMPTY); // 移动类型
                    sapItem.put("ZTKBS", "X"); // 退库标识 X
                    sapItem.put("XSTOB", Const.STRING_EMPTY); // 退库标识 X
                    sapItem.put("VENDOR", jsonObject.getJSONObject("batchInfo").getString("supplierCode")); // 供应商
                    sapItem.put("REF_DOC_YR", item.getIntValue("preMatDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("preMatDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", jsonObject.getIntValue("preMatDocRid")); // 参考凭证行项目号
                    sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                    sapItem.put("SPEC_STOCK", jsonObject.getJSONObject("batchInfo").getString("specStock")); // 特殊库存标识
                    sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                    sapItem.put("ENTRY_QNT", jsonObject.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                    sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                    sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                    sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                    sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                    sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                    sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                    sapItem.put("RESERV_NO",  item.getString("reserveReceiptCode")); // 预留/相关需求的编号
                    sapItem.put("RES_ITEM", item.getString("reserveReceiptRid")); // 预留/相关需求的项目编号
                    sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                    sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                    sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                    sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                    sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                    sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                    sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                    sapItem.put("WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    paramsOfItem.add(sapItem);
                }
            }
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  退转库入库
     * @param postingItem
     * @return
     */
    public static JSONObject getStockReturnTransferParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_TWO;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            // json转list
            JSONArray jsonArray = item.getJSONArray("itemInfoList");
            if(jsonArray!=null){
                for(int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject sapItem = new JSONObject();
                    sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                    sapItem.put("ZHXH", jsonObject.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", jsonObject.getJSONObject("batchInfo").getString("batchCode")); // 批次号
                    sapItem.put("MOVE_TYPE", "Y91"); // 移动类型
                    sapItem.put("VENDOR", jsonObject.getJSONObject("batchInfo").getString("supplierCode")); // 供应商
                    sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                    sapItem.put("SPEC_STOCK", jsonObject.getJSONObject("batchInfo").getString("specStock")); // 特殊库存标识
                    sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                    sapItem.put("ENTRY_QNT", jsonObject.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                    sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                    sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                    sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                    sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                    sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                    sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                    sapItem.put("RESERV_NO",  item.getString("reserveReceiptCode")); // 预留/相关需求的编号
                    sapItem.put("RES_ITEM", item.getString("reserveReceiptRid")); // 预留/相关需求的项目编号
                    sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                    sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                    sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                    sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                    sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                    sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                    sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                    sapItem.put("WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    paramsOfItem.add(sapItem);
                }
            }
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  退旧入库
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockReturnOldInputParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_TWO;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", item.getString("rid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("MOVE_TYPE", "Y91"); // 移动类型
            sapItem.put("VENDOR", Const.STRING_EMPTY); // 供应商
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", Const.STRING_EMPTY); // 特殊库存标识
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", Const.STRING_EMPTY); // 采购订单号
            sapItem.put("PO_ITEM", Const.STRING_EMPTY); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO",  Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
            sapItem.put("EXPIRYDATE", Const.STRING_EMPTY); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", Const.STRING_EMPTY); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  闲置入库
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockLeisureInputParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_TWO;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", item.getString("rid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("MOVE_TYPE", "Y91"); // 移动类型
            sapItem.put("VENDOR", Const.STRING_EMPTY); // 供应商
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", Const.STRING_EMPTY); // 特殊库存标识
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_QNT", item.getString("qty")); // 数量
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", Const.STRING_EMPTY); // 采购订单号
            sapItem.put("PO_ITEM", Const.STRING_EMPTY); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO",  Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
            sapItem.put("EXPIRYDATE", Const.STRING_EMPTY); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", Const.STRING_EMPTY); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  报废出库单
     * @param postingItem
     * @return
     */
    public static JSONObject getStockOutputScrapParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_TWO;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            // json转list
            JSONArray jsonArray = item.getJSONArray("binDTOList");
            if(jsonArray!=null){
                for(int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject sapItem = new JSONObject();
                    sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                    sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                    sapItem.put("ZHXH", jsonObject.getString("bid")); // instock配货号
                    sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
                    sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                    sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                    sapItem.put("BATCH", jsonObject.getJSONObject("batchInfo").getString("batchCode")); // 批次号
                    if (jsonObject.getJSONObject("batchInfo").getString("specStock").equals("Q")) {
                        sapItem.put("MOVE_TYPE", "555Q"); // 移动类型
                    }else {
                        sapItem.put("MOVE_TYPE", "555"); // 移动类型
                    }
                    sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                    sapItem.put("SPEC_STOCK", jsonObject.getJSONObject("batchInfo").getString("specStock")); // 特殊库存标识
                    sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                    sapItem.put("ENTRY_QNT", jsonObject.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
                    sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                    sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                    sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                    sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                    sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                    sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                    sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                    sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                    sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                    sapItem.put("RESERV_NO",  item.getString("reserveReceiptCode")); // 预留/相关需求的编号
                    sapItem.put("RES_ITEM", item.getString("reserveReceiptRid")); // 预留/相关需求的项目编号
                    sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                    sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                    sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                    sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                    sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                    sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                    sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                    sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                    sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                    sapItem.put("WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    sapItem.put("EXPIRYDATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 货架寿命到期日
                    sapItem.put("PROD_DATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 生产日期
                    sapItem.put("VAL_WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                    paramsOfItem.add(sapItem);
                }
            }
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 成套设备到货登记
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedArrivalRegisterParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("arrivalRegisterItem");
        if(UtilObject.isNotNull(firstItem)){ //冲销
            return getUnitizedArrivalRegisterWriteOffParams(postingItem);
        }

        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(item -> {
            JSONObject sapItem = new JSONObject();
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", item.getString("rid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂   !!! 这里的工厂和库存地点 SAP 说不会记录, 后续验收入库时才会记录具体的工厂和库存地点!!!
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点   !!! 这里的工厂和库存地点 SAP 说不会记录, 后续验收入库时才会记录具体的工厂和库存地点!!!
            sapItem.put("BATCH", item.getString("batchCode")); // 批次号
            sapItem.put("ENTRY_QNT", item.getString("billQty")); // 数量
            sapItem.put("MOVE_TYPE", "103"); // 移动类型
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }
    /**
     * 成套设备到货登记 冲销
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedArrivalRegisterWriteOffParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("arrivalRegisterItem");
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        if (UtilString.isNotNullOrEmpty(firstItem.getString("matDocCode"))) {
            paramsOfHeader.put("PSTNG_DATE", firstObject.getString("aWriteOffPostingDate")); // 冲销-过账日期
            paramsOfHeader.put("DOC_DATE", firstObject.getString("aWriteOffDocDate")); // 冲销-凭证日期
        }
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstItem.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(waybill -> {
            JSONObject sapItem = new JSONObject();
            JSONObject item=waybill.getJSONObject("arrivalRegisterItem");
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", waybill.getString("arrivalRegisterItemRid")); // istock行号
            sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
            JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
            if (batchInfoObj != null) {
                sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
            }
            sapItem.put("ENTRY_QNT", waybill.getBigDecimal("aWriteOffQty").multiply(waybill.getBigDecimal("price"))
                    .add(waybill.getBigDecimal("remainder"))); // 数量
            sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
            sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
            sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
            sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 成套设备数量差异通知
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedInconformityNumberNoticeParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("numberInconformityItem");
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("nWriteOffPostingDate")); // 冲销-过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("nWriteOffDocDate")); // 冲销-凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstItem.getInteger("receiptType");
        String  callReceiptCode=firstItem.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(waybill -> {
            JSONObject sapItem = new JSONObject();
            JSONObject item=waybill.getJSONObject("numberInconformityItem");
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", waybill.getString("numberInconformityNoticeItemRid")); // istock行号
            sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", waybill.getString("locationCode")); // 库存地点 改为运单信息里的存储级别, 以保证 WMS 与 SAP 一致
            JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
            if (batchInfoObj != null) {
                sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
            }
            BigDecimal remainder = waybill.getBigDecimal("remainder");
            Integer useSignRemainder = waybill.getInteger("useSignRemainder");
            if (useSignRemainder != null && useSignRemainder.equals(EnumRealYn.TRUE.getIntValue())) {
                remainder = BigDecimal.ZERO;
            }
            sapItem.put("ENTRY_QNT", waybill.getBigDecimal("nWriteOffQty").multiply(waybill.getBigDecimal("price")).add(remainder)); // 数量
            sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
            sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
            sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
            sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 成套设备数量差异处置
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedInconformityNumberMaintainParams (String postingItem){
        return  getUnitizedInconformityNumberNoticeParams(postingItem);
    }

    /**
     * 成套设备不符合项通知
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedInconformityNoticeParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("qualifiedInconformityItem");
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("qWriteOffPostingDate")); // 冲销-过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("qWriteOffDocDate")); // 冲销-凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        Integer callReceiptType= firstItem.getInteger("receiptType");
        String  callReceiptCode=firstItem.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        jsonObjectList.forEach(waybill -> {
            JSONObject sapItem = new JSONObject();
            JSONObject item=waybill.getJSONObject("qualifiedInconformityItem");
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", waybill.getString("qualityInconformityNoticeItemRid")); // istock行号
            sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", waybill.getString("locationCode")); // 库存地点 改为运单信息里的存储级别, 以保证 WMS 与 SAP 一致
            JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
            if (batchInfoObj != null) {
                sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
            }
            BigDecimal remainder = waybill.getBigDecimal("remainder");
            Integer useSignRemainder = waybill.getInteger("useSignRemainder");
            if (useSignRemainder != null && useSignRemainder.equals(EnumRealYn.TRUE.getIntValue())) {
                remainder = BigDecimal.ZERO;
            }
            sapItem.put("ENTRY_QNT", waybill.getBigDecimal("qWriteOffQty").multiply(waybill.getBigDecimal("price")).add(remainder)); // 数量
            sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
            sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
            sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
            sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", item.getString("specStockCode")); // WBS
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 成套设备不符合项处置
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedInconformityMaintainParams (String postingItem){
        return getUnitizedInconformityNoticeParams(postingItem);
    }

    /**
     * 成套设备验收入库
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockInputInspectParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("inspectInputItem");
        Integer callReceiptType= firstItem.getInteger("receiptType");
        String  callReceiptCode=firstItem.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(waybill -> {
            JSONObject sapItem = new JSONObject();
            JSONObject item=waybill.getJSONObject("inspectInputItem");
            sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("rid")); // istock行号
            sapItem.put("ZHXH", waybill.getString("bid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", waybill.getString("locationCode")); // 库存地点 改为运单信息里的存储级别, 以保证 WMS 与 SAP 一致
            JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
            sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
            sapItem.put("ENTRY_QNT", waybill.getBigDecimal("qty").multiply(batchInfoObj.getBigDecimal("price"))); // 数量
            sapItem.put("MOVE_TYPE", "105"); // 移动类型
            sapItem.put("REF_DOC_YR", waybill.getIntValue("preMatDocYear")); // 参考凭证年度
            sapItem.put("REF_DOC", waybill.getString("preMatDocCode")); // 参考凭证号
            sapItem.put("REF_DOC_IT", waybill.getIntValue("preMatDocRid")); // 参考凭证行项目号
            sapItem.put("SPEC_STOCK", item.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("WBS_ELEM", batchInfoObj.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", batchInfoObj.getString("productionDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", batchInfoObj.getString("productionDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", batchInfoObj.getString("specStockCode")); // WBS
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            Integer preReceiptType = item.getIntValue("preReceiptType");
            if(UtilNumber.isNotEmpty(preReceiptType) && preReceiptType.equals(EnumReceiptType.UNITIZED_CONDITIONAL_RELEASE.getValue())){
                // 有条件放行 : 入冻结库存
                sapItem.put("STCK_TYPE", "3"); // 库存类型
            }
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 成套设备验收入库 冲销
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockInputInspectWriteOffParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("inspectInputItem");
        Integer callReceiptType= firstItem.getInteger("receiptType");
        String  callReceiptCode=firstItem.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_ONE;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstItem.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        if (UtilString.isNotNullOrEmpty(firstItem.getString("writeOffMatDocCode"))) {
            paramsOfHeader.put("PSTNG_DATE", firstObject.getString("deliveryWriteOffPostingDate")); // 冲销-过账日期
            paramsOfHeader.put("DOC_DATE", firstObject.getString("deliveryWriteOffDocDate")); // 冲销-凭证日期
        }
        else {
            paramsOfHeader.put("PSTNG_DATE", firstObject.getString("writeOffPostingDate")); // 冲销-过账日期
            paramsOfHeader.put("DOC_DATE", firstObject.getString("writeOffDocDate")); // 冲销-凭证日期
        }
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(waybill -> {
            JSONObject sapItem = new JSONObject();
            JSONObject item=waybill.getJSONObject("inspectInputItem");
            sapItem.put("ZDJBH", item.getString("preReceiptCode")); // instock单据号
            sapItem.put("ZDJXM", item.getString("preReceiptRid")); // istock行号
            sapItem.put("ZHXH", waybill.getString("preReceipBid")); // instock配货号
            sapItem.put("MATERIAL", item.getString("matCode")); // 物料编码
            sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
            sapItem.put("STGE_LOC", waybill.getString("locationCode")); // 库存地点 改为运单信息里的存储级别, 以保证 WMS 与 SAP 一致
            JSONObject batchInfoObj = waybill.getJSONObject("bizBatchInfoDTO");
            sapItem.put("BATCH", batchInfoObj.getString("batchCode")); // 批次号
            //  104 - 冲销
            if ( UtilString.isNotNullOrEmpty(item.getString("writeOffMatDocCode"))) {
                sapItem.put("MOVE_TYPE", "104"); // 移动类型 - 冲销
                sapItem.put("ENTRY_QNT", waybill.getBigDecimal("qty").multiply(batchInfoObj.getBigDecimal("price"))); // 数量
                sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear103")); // 参考凭证年度
                sapItem.put("REF_DOC", item.getString("matDocCode103")); // 参考凭证号
                sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid103")); // 参考凭证行项目号
            }  else    {
                // 验收入库 106
                sapItem.put("MOVE_TYPE", "106"); // 移动类型
                sapItem.put("ENTRY_QNT", waybill.getBigDecimal("qty").multiply(batchInfoObj.getBigDecimal("price"))); // 数量
                sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear103")); // 参考凭证年度
                sapItem.put("REF_DOC", item.getString("matDocCode103")); // 参考凭证号
                sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid103")); // 参考凭证行项目号
            }
            sapItem.put("SPEC_STOCK", batchInfoObj.getString("specStock")); // 特殊库存标识
            sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
            sapItem.put("ENTRY_UOM", item.getString("unitCode")); // 计量单位
            sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
            sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
            sapItem.put("WBS_ELEM", batchInfoObj.getString("specStockCode")); // WBS
            sapItem.put("EXPIRYDATE", batchInfoObj.getString("productionDate")); // 货架寿命到期日
            sapItem.put("PROD_DATE", batchInfoObj.getString("productionDate")); // 生产日期
            sapItem.put("VAL_WBS_ELEM", batchInfoObj.getString("specStockCode")); // WBS
            sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
            sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
            sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
            sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
            sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
            sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
            sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
            sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
            sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
            sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
            sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
            sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
            sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
            sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
            sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
            sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
            sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
            sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
            sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
            sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
            paramsOfItem.add(sapItem);
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 成套设备领料退库
     *
     * @param postingItem
     * @return
     */
    private static JSONObject getUnitizedStockReturnMatReqParams(String postingItem) {
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);

        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String type = SapConst.TYPE_TWO;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, firstObject.getString("receiptCode"), type);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", firstObject.getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", firstObject.getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", firstObject.getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            // json转list
            JSONArray jsonArray = item.getJSONArray("itemInfoList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                JSONObject sapItem = new JSONObject();
                sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                sapItem.put("ZHXH", jsonObject.getString("bid")); // instock配货号
                sapItem.put("MATERIAL", item.getString("parentMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("ftyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("locationCode")); // 库存地点
                sapItem.put("BATCH", jsonObject.getJSONObject("batchInfo").getString("batchCode")); // 批次号
                sapItem.put("MOVE_TYPE", Const.STRING_EMPTY); // 移动类型
                sapItem.put("ZTKBS", "X"); // 退库标识 X
                sapItem.put("XSTOB", Const.STRING_EMPTY); // 退库标识 X
                sapItem.put("VENDOR", jsonObject.getJSONObject("batchInfo").getString("supplierCode")); // 供应商
                sapItem.put("REF_DOC_YR", item.getIntValue("preMatDocYear")); // 参考凭证年度
                sapItem.put("REF_DOC", item.getString("preMatDocCode")); // 参考凭证号
                sapItem.put("REF_DOC_IT", item.getIntValue("preMatDocRid")); // 参考凭证行项目号
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("SPEC_STOCK", jsonObject.getJSONObject("batchInfo").getString("specStock")); // 特殊库存标识
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("ENTRY_QNT", jsonObject.getBigDecimal("qty").multiply(jsonObject.getJSONObject("batchInfo").getBigDecimal("price"))); // 数量
                sapItem.put("ENTRY_UOM", item.getString("parentUnitCode")); // 计量单位
                sapItem.put("PO_NUMBER", item.getString("referReceiptCode")); // 采购订单号
                sapItem.put("PO_ITEM", item.getString("referReceiptRid")); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO",  item.getString("reserveReceiptCode")); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", item.getString("reserveReceiptRid")); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_MAT", Const.STRING_EMPTY); // 收货/发货物料
                sapItem.put("MOVE_PLANT", Const.STRING_EMPTY); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", Const.STRING_EMPTY); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH", Const.STRING_EMPTY); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                sapItem.put("EXPIRYDATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", jsonObject.getJSONObject("batchInfo").getString("productionDate")); // 生产日期
                sapItem.put("VAL_WBS_ELEM", jsonObject.getJSONObject("batchInfo").getString("specStockCode")); // WBS
                if(UtilNumber.isEmpty(firstObject.getInteger("qualified"))){
                    // 有条件放行 : 入冻结库存
                    sapItem.put("STCK_TYPE", "3"); // 库存类型
                }
                paramsOfItem.add(sapItem);
            }
        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  成套转储
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockTransportParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("assembleDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转储 511
                sapItem.put("MATERIAL", assemble.getString("parentMatCode")); // 物料编码
                sapItem.put("PLANT", assemble.getString("ftyCode")); // 工厂
                sapItem.put("STGE_LOC", assemble.getString("locationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", assemble.getString("specStockCode")); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("specStockCode")); // WBS
                sapItem.put("MOVE_MAT",assemble.getString("parentMatCode")); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("specStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getBigDecimal("qty").multiply(assemble.getJSONObject("inputBatchInfoDTO").getBigDecimal("price"))); // 数量
                sapItem.put("ENTRY_UOM", assemble.getString("parentUnitCode")); // 计量单位
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "311"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  成套转性
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockTransferParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>  itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转性 510
                sapItem.put("MATERIAL", item.getString("outputParentMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", item.getString("inputSpecStockCode")); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",item.getString("inputParentMatCode")); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getBigDecimal("qty").multiply(assemble.getJSONObject("inputBatchInfoDTO").getBigDecimal("price"))); // 数量
                sapItem.put("ENTRY_UOM", item.getString("outputParentUnitCode")); // 计量单位
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "415"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  转储
     * @param postingItem
     * @return
     */
    public static JSONObject getStockTransportParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        if("301".equals(firstObject.getString("moveTypeCode"))){
          return  getStockTransport301Params(postingItem);
        }
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("assembleDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转储 511
                sapItem.put("MATERIAL", assemble.getString("matCode")); // 物料编码
                sapItem.put("PLANT", assemble.getString("ftyCode")); // 工厂
                sapItem.put("STGE_LOC", assemble.getString("locationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", assemble.getString("specStockCode")); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("specStockCode")); // WBS
                sapItem.put("MOVE_MAT",assemble.getString("matCode")); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("specStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("ENTRY_UOM", assemble.getString("unitCode")); // 计量单位
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "311"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  转储
     * @param postingItem
     * @return
     */
    public static JSONObject getStockTransport301Params (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("assembleDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转储 511
                sapItem.put("ZDJBH", item.getString("receiptCode")); // instock单据号
                sapItem.put("ZDJXM", item.getString("rid")); // istock行号
                sapItem.put("ZHXH", item.getString("rid")); // instock配货号

                sapItem.put("MATERIAL", assemble.getString("matCode")); // 物料编码
                sapItem.put("PLANT", assemble.getString("ftyCode")); // 工厂
                sapItem.put("STGE_LOC", assemble.getString("locationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", assemble.getString("specStockCode")); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("specStockCode")); // WBS
                sapItem.put("MOVE_MAT",assemble.getString("matCode")); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("specStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("ENTRY_UOM", assemble.getString("unitCode")); // 计量单位
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY ); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  转性
     * @param postingItem
     * @return
     */
    public static JSONObject getStockTransferParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>  itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转性 510
                sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", item.getString("inputSpecStockCode")); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",item.getString("inputMatCode")); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("ENTRY_UOM", item.getString("outputUnitCode")); // 计量单位
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * 309/309Q转码
     * @param postingItem
     * @return
     */
    public static JSONObject getStockTransportMatParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>  itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();
                // 转性 510
                sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", item.getString("inputSpecStockCode")); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",item.getString("inputMatCode")); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("ENTRY_UOM", item.getString("outputUnitCode")); // 计量单位
                sapItem.put("MOVE_REAS", Const.STRING_EMPTY); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     * Y81/Y82(Q)转码
     * @param postingItem
     * @return
     */
    public static JSONObject getStockTransportMatY81AndY82Params (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject> itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            for (JSONObject assemble : itemList) {
                JSONObject sapItem = new JSONObject();
                sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("ENTRY_UOM", item.getString("outputUnitCode")); // 计量单位
                sapItem.put("ID", item.getString("id")); // 唯一值
                sapItem.put("REF_ID", Const.STRING_EMPTY); // 源行项目
                paramsOfItem.add(sapItem);
            }
            List<JSONObject> splitItemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("splitItemVOList")), JSONObject.class);
            for (JSONObject splitItem : splitItemList) {
                List<JSONObject> splitItemBinList = JSONObject.parseArray(JSONObject.toJSONString(splitItem.get("binDTOList")), JSONObject.class);
                for (JSONObject splitItemBin : splitItemBinList) {
                    JSONObject sapItem = new JSONObject();
                    sapItem.put("MATERIAL", splitItem.getString("inputMatCode")); // 物料编码
                    sapItem.put("PLANT", splitItem.getString("inputFtyCode")); // 工厂
                    sapItem.put("STGE_LOC", splitItem.getString("inputLocationCode")); // 库存地点
                    sapItem.put("BATCH",splitItemBin.get("batchCode")); // 批次号
                    sapItem.put("MOVE_TYPE", splitItem.getString("moveTypeCode")); // 移动类型
                    sapItem.put("ENTRY_QNT", splitItemBin.getString("qty")); // 数量
                    sapItem.put("ENTRY_UOM", splitItem.getString("inputUnitCode")); // 计量单位
                    sapItem.put("ID", splitItem.getString("id")); // 唯一值
                    sapItem.put("REF_ID", splitItem.getString("sourceItemId")); // 源行项目
                    paramsOfItem.add(sapItem);
                }
            }
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  报废冻结
     * @param postingItem
     * @return
     */
    public static JSONObject getStockFreezeScrapParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();

                sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",Const.STRING_EMPTY); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outputSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("MOVE_REAS", item.getString("scrapCause")); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "344"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }

    /**
     *  过期冻结
     * @param postingItem
     * @return
     */
    public static JSONObject getStockFreezeExpireParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();

                sapItem.put("MATERIAL", item.getString("outputMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",Const.STRING_EMPTY); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outputSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getString("qty")); // 数量
                sapItem.put("MOVE_REAS", item.getString("scrapCause")); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "344"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }


    /**
     *  报废冻结
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockUnFreezeScrapParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();

                sapItem.put("MATERIAL", item.getString("outputParentMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",Const.STRING_EMPTY); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outputSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getBigDecimal("qty").multiply(assemble.getJSONObject("inputBatchInfoDTO").getBigDecimal("price"))); // 数量
                sapItem.put("MOVE_REAS", item.getString("scrapCause")); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "343"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }


    /**
     *  报废冻结
     * @param postingItem
     * @return
     */
    public static JSONObject getUnitizedStockFreezeScrapParams (String postingItem){
        JSONObject params = new JSONObject();
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        Integer callReceiptType= firstObject.getInteger("receiptType");
        String  callReceiptCode=firstObject.getString("receiptCode");
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // ********  I_IMPORT 接口通用输入参数 ********
        String ivType =SapConst.TYPE_TRANS;
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
        JSONObject paramsOfImport = UtilErp.getImport(userCode, jsonObjectList.get(0).getString("receiptCode"), ivType);

        // ********  I_HEADER 抬头参数  ********
        JSONObject paramsOfHeader = new JSONObject();
        paramsOfHeader.put("PSTNG_DATE", jsonObjectList.get(0).getString("postingDate")); // 过账日期
        paramsOfHeader.put("DOC_DATE", jsonObjectList.get(0).getString("docDate")); // 凭证日期
        paramsOfHeader.put("PR_UNAME", "admin"); // 用户名
        paramsOfHeader.put("HEADER_TXT", jsonObjectList.get(0).getString("headRemark")); // 抬头文本

        // ********  I_ITEM 行项目参数  ********
        JSONArray paramsOfItem = new JSONArray();
        jsonObjectList.forEach(item -> {
            List<JSONObject>   itemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("binDTOList")), JSONObject.class);
            itemList.stream().forEach(assemble->{
                JSONObject sapItem = new JSONObject();

                sapItem.put("MATERIAL", item.getString("outputParentMatCode")); // 物料编码
                sapItem.put("PLANT", item.getString("outputFtyCode")); // 工厂
                sapItem.put("STGE_LOC", item.getString("outputLocationCode")); // 库存地点
                sapItem.put("BATCH",assemble.get("batchCode")); // 批次号
                sapItem.put("WBS_ELEM", Const.STRING_EMPTY); // WBS
                sapItem.put("VAL_WBS_ELEM", assemble.getString("outputSpecStockCode")); // WBS
                sapItem.put("MOVE_MAT",Const.STRING_EMPTY); // 收货/发货物料
                sapItem.put("SPEC_STOCK", assemble.getString("outputSpecStock")); // 特殊库存标识
                sapItem.put("VENDOR", item.getString("supplierCode")); // 供应商
                sapItem.put("ENTRY_QNT", assemble.getBigDecimal("qty").multiply(assemble.getJSONObject("inputBatchInfoDTO").getBigDecimal("price"))); // 数量
                sapItem.put("MOVE_REAS", item.getString("scrapCause")); // 移动原因
                if (UtilString.isNotNullOrEmpty(item.getString("matDocCode"))) {
                    sapItem.put("MOVE_TYPE", item.getString("moveTypeCode")); // 移动类型
                    sapItem.put("REF_DOC_YR", item.getIntValue("matDocYear")); // 参考凭证年度
                    sapItem.put("REF_DOC", item.getString("matDocCode")); // 参考凭证号
                    sapItem.put("REF_DOC_IT", item.getIntValue("matDocRid")); // 参考凭证行项目号
                } else {
                    sapItem.put("MOVE_TYPE", "344"); // 移动类型
                }
                sapItem.put("STCK_TYPE", Const.STRING_EMPTY); // 库存类型
                sapItem.put("VAL_TYPE", Const.STRING_EMPTY); //评估类型
                sapItem.put("PO_NUMBER",Const.STRING_EMPTY); // 采购订单号
                sapItem.put("PO_ITEM",Const.STRING_EMPTY); // 采购订单行号
                sapItem.put("ITEM_TEXT", Const.STRING_EMPTY); // 项目文本
                sapItem.put("GR_RCPT", Const.STRING_EMPTY); // 收货方/运达方
                sapItem.put("UNLOAD_PT", Const.STRING_EMPTY); // 卸货点
                sapItem.put("COSTCENTER", Const.STRING_EMPTY); // 成本中心
                sapItem.put("ORDERID", Const.STRING_EMPTY); // 订单号
                sapItem.put("ORDER_ITNO", Const.STRING_EMPTY); // 订单项目编号
                sapItem.put("CALC_MOTIVE", Const.STRING_EMPTY); // 会计标识
                sapItem.put("RESERV_NO", Const.STRING_EMPTY); // 预留/相关需求的编号
                sapItem.put("RES_ITEM", Const.STRING_EMPTY); // 预留/相关需求的项目编号
                sapItem.put("RES_TYPE", Const.STRING_EMPTY); // 记录类型
                sapItem.put("WITHDRAWN", Const.STRING_EMPTY); // 该预留的最后发货
                sapItem.put("MOVE_PLANT", item.getString("inputFtyCode")); // 收货/发货工厂
                sapItem.put("MOVE_STLOC", item.getString("inputLocationCode")); // 收货/发货库存地点
                sapItem.put("MOVE_BATCH",assemble.get("batchCode")); // 收货/发货批量
                sapItem.put("MOVE_VAL_TYPE", Const.STRING_EMPTY); // 转储批次的估价类型
                sapItem.put("PROFIT_CTR", Const.STRING_EMPTY); // 利润中心
                sapItem.put("EXPIRYDATE", item.getString("productDate")); // 货架寿命到期日
                sapItem.put("PROD_DATE", item.getString("productDate")); // 生产日期
                paramsOfItem.add(sapItem);
            });

        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER", paramsOfHeader);
        params.put("T_ITEM", paramsOfItem);
        return params;
    }



    /**
     *  到货登记
     * @param postingItem
     * @return
     */
    public static void delArrivalRegisterObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("rid").equals(obj.getString("ZDJXM"))
                        && item.getString("rid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  验收入库
     * @param postingItem
     * @return
     */
    public static void delStockInputInspectObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("rid").equals(obj.getString("ZDJXM"))
                        && item.getString("rid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  验收入库-冲销
     * @param postingItem
     * @return
     */
    public static void delStockInputInspectWriteOffObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("preReceiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("preReceiptRid").equals(obj.getString("ZDJXM"))
                        && item.getString("preReceiptRid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setWriteOffMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setWriteOffMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  不符合项处置
     * @param postingItem
     * @return
     */
    public static void delInconformityNaintainObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("rid").equals(obj.getString("ZDJXM"))
                        && item.getString("rid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  采购退货单
     * @param postingItem
     * @return
     */
    public static void delStockOutputPurchaseReturnObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            JSONArray jsonArray =  item.getJSONArray("binDTOList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  领料退库单
     * @param postingItem
     * @return
     */
    public static void delStockReturnMatReqObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            JSONArray   jsonArray = item.getJSONArray("itemInfoList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  退转库入库
     * @param postingItem
     * @return
     */
    public static void delStockReturnTransferObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            JSONArray jsonArray = item.getJSONArray("itemInfoList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  退旧入库
     * @param postingItem
     * @return
     */
    public static void delStockReturnOldObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        if (UtilCollection.isNotEmpty(mseg)) {
            jsonObjectList.forEach(item -> {
                for (int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if (item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && item.getString("rid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  闲置入库
     * @param postingItem
     * @return
     */
    public static void delStockLeisureInputObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        if (UtilCollection.isNotEmpty(mseg)) {
            jsonObjectList.forEach(item -> {
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && item.getString("rid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            });
        }
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  报废出库单
     * @param postingItem
     * @return
     */
    public static void delStockOutputScrapObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            JSONArray jsonArray = item.getJSONArray("binDTOList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 成套设备到货登记
     * @param postingItem
     * @return
     */
    public static void delUnitizedArrivalRegisterObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONObject firstObject =jsonObjectList.get(0);
        JSONObject firstItem =firstObject.getJSONObject("arrivalRegisterItem");
        if(UtilObject.isNotNull(firstItem)){ //冲销
              delUnitizedArrivalRegisterWriteOffObject(postingItem,erpReturnObj,returnObject);
              return ;
        }
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("rid").equals(obj.getString("ZDJXM"))
                        && item.getString("rid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 成套设备到货登记 冲销
     * @param postingItem
     * @return
     */
    public static void delUnitizedArrivalRegisterWriteOffObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("arrivalRegisterItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && waybill.getString("arrivalRegisterItemRid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }
    /**
     * 成套设备数量差异通知
     * @param postingItem
     * @return
     */
    public static void delUnitizedInconformityNumberNoticeObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("numberInconformityItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && waybill.getString("numberInconformityNoticeItemRid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);

    }

    /**
     * 成套设备数量差异处置
     * @param postingItem
     * @return
     */
    public static void delUnitizedInconformityNumberMaintainObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("numberInconformityItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && waybill.getString("numberInconformityMaintainItemRid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);

    }

    /**
     * 成套设备不符合项通知
     * @param postingItem
     * @return
     */
    public static void delUnitizedInconformityNoticeObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("qualifiedInconformityItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && waybill.getString("qualityInconformityNoticeItemRid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);

    }

    /**
     * 成套设备不符合项处置
     * @param postingItem
     * @return
     */
    public static void delUnitizedInconformityMaintainObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("qualifiedInconformityItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && waybill.getString("qualityInconformityMaintainItemRid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);

    }

    /**
     * 成套设备验收入库
     * @param postingItem
     * @return
     */
    public static void delUnitizedStockInputInspectObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("inspectInputItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("rid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("bid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);

    }

    /**
     * 成套设备验收入库 冲销
     * @param postingItem
     * @return
     */
    public static void delUnitizedStockInputInspectWriteOffObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(waybill -> {
            JSONObject item=waybill.getJSONObject("inspectInputItem");
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("preReceiptCode").equals(obj.getString("ZDJBH"))
                        && item.getString("preReceiptRid").equals(obj.getString("ZDJXM"))
                        && waybill.getString("preReceipBid").equals(obj.getString("ZHXH"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setReceiptCode(obj.getString("ZDJBH"));
                    returnItem.setReceiptRid(obj.getString("ZDJXM"));
                    returnItem.setReceiptBid(obj.getString("ZHXH"));
                    returnItem.setWriteOffMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                    returnItem.setWriteOffMatDocRid(obj.getString("ZEILE"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItemList.add(returnItem);
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);

    }



    /**
     * 成套设备领料退库
     * @param postingItem
     * @return
     */
    private static void delUnitizedStockReturnMatReqObject(String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject) {
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(item -> {
            JSONArray jsonArray = item.getJSONArray("itemInfoList");
            for(int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                for(int j = 0; j < mseg.size(); j++) {
                    JSONObject obj = mseg.getJSONObject(j);
                    if(item.getString("receiptCode").equals(obj.getString("ZDJBH"))
                            && item.getString("rid").equals(obj.getString("ZDJXM"))
                            && jsonObject.getString("bid").equals(obj.getString("ZHXH"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setReceiptCode(obj.getString("ZDJBH"));
                        returnItem.setReceiptRid(obj.getString("ZDJXM"));
                        returnItem.setReceiptBid(obj.getString("ZHXH"));
                        returnItem.setMatDocCode(obj.getString("MBLNR"));
                        returnItem.setMatDocYear(returnObject.getString("MJAHR"));
                        returnItem.setMatDocRid(obj.getString("ZEILE"));
                        returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                        returnItemList.add(returnItem);
                    }
                }
            }
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }
    /**
     *  成套转储
     * @param postingItem
     * @return
     */
    public static void delUnitizedStockTransportObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  成套转性
     * @param postingItem
     * @return
     */
    public static void delUnitizedStockTransferObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }
    /**
     *  转储
     * @param postingItem
     * @return
     */
    public static void delStockTransportObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  转性
     * @param postingItem
     * @return
     */
    public static void delStockTransferObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * 309/309Q转码
     * @param postingItem
     * @return
     */
    public static void delStockTransportMatObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     * Y81/Y82(Q)转码
     */
    public static void delStockTransportMatY81AndY82Object (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        JSONArray mseg = returnObject.getJSONArray("T_MSEG");
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        String code = mseg.getJSONObject(0).getString("CODE");
        String mes = mseg.getJSONObject(0).getString("MES");
        for (JSONObject item : jsonObjectList) {
            for(int i = 0; i < mseg.size(); i++) {
                JSONObject obj = mseg.getJSONObject(i);
                if(item.getString("id").equals(obj.getString("ID"))) {
                    ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                    returnItem.setMatDocCode(obj.getString("MBLNR"));
                    returnItem.setMatDocRid(obj.getString("ZEILE"));
                    returnItem.setMatDocYear(obj.getString("MJAHR"));
                    returnItem.setMatCode(obj.getString("MATERIAL"));
                    returnItem.setDmbtr(obj.getBigDecimal("DMBTR"));
                    returnItem.setWmsHeadId(item.getLong("headId"));
                    returnItem.setWmsItemId(item.getLong("id"));
                    if (!Const.ERP_RETURN_TYPE_S.equals(obj.getString("CODE"))) {
                        code = obj.getString("CODE");
                        mes = obj.getString("MES");
                    }
                    returnItemList.add(returnItem);
                }
            }
            List<JSONObject> splitItemList = JSONObject.parseArray(JSONObject.toJSONString(item.get("splitItemVOList")), JSONObject.class);
            for (JSONObject splitItem : splitItemList) {
                for (int i = 0; i < mseg.size(); i++) {
                    JSONObject splitObj = mseg.getJSONObject(i);
                    if (splitItem.getString("id").equals(splitObj.getString("ID"))) {
                        ErpReturnObjectItem returnItem = new ErpReturnObjectItem();
                        returnItem.setMatDocCode(splitObj.getString("MBLNR"));
                        returnItem.setMatDocRid(splitObj.getString("ZEILE"));
                        returnItem.setMatDocYear(splitObj.getString("MJAHR"));
                        returnItem.setMatCode(splitObj.getString("MATERIAL"));
                        returnItem.setDmbtr(splitObj.getBigDecimal("DMBTR"));
                        returnItem.setWmsHeadId(splitItem.getLong("headId"));
                        returnItem.setWmsItemId(splitItem.getLong("id"));
                        if (!Const.ERP_RETURN_TYPE_S.equals(splitObj.getString("CODE"))) {
                            code = splitObj.getString("CODE");
                            mes = splitObj.getString("MES");
                        }
                        returnItemList.add(returnItem);
                    }
                }
            }
        }
        // Y81 接口响应报文I_RETURN中CODE和MES永远是成功, 具体行项目是否成功需要看T_MSEG
        erpReturnObj.setSuccess(code);
        erpReturnObj.setReturnMessage(mes);
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  报废冻结
     * @param postingItem
     * @return
     */
    public static void delStockFreezeScrapObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  成套设备物资冻结
     * @param postingItem
     * @return
     */
    public static void delUnitizedStockFreezeObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  成套设备物资解冻
     * @param postingItem
     * @return
     */
    public static void delUnitizedStockUnFreezeObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }

    /**
     *  过期冻结
     * @param postingItem
     * @return
     */
    public static void delStockFreezeExpireObject (String postingItem, ErpReturnObject erpReturnObj, JSONObject returnObject){
        List<JSONObject> jsonObjectList = JSONObject.parseArray(postingItem, JSONObject.class);
        List<ErpReturnObjectItem> returnItemList = Lists.newArrayList();
        jsonObjectList.forEach(e -> {
            ErpReturnObjectItem item = new ErpReturnObjectItem();
            item.setReceiptCode(e.getString("receiptCode"));
            item.setReceiptRid(e.getString("rid"));
            item.setReceiptBid(e.getString("bid"));
            item.setMatDocCode(returnObject.getString("MBLNR"));
            item.setMatDocYear(returnObject.getString("MJAHR"));
            item.setMatDocRid(Const.DOC_RID_PRE + e.getString("rid"));
            returnItemList.add(item);
        });
        erpReturnObj.setSuccess(returnObject.getJSONObject("I_RETURN").getString("CODE"));
        erpReturnObj.setReturnMessage(returnObject.getJSONObject("I_RETURN").getString("MES"));
        erpReturnObj.setReturnItemList(returnItemList);
    }
}
