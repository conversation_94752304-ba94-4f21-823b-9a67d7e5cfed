package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.masterdata.unit.entity.DicUnitRel;
import com.inossem.wms.common.model.masterdata.unit.po.DicUnitRelSearchPO;
import com.inossem.wms.common.model.masterdata.unit.vo.DicUnitRelPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.dao.DicUnitRelMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 物料计量单位换算表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Service
public class DicUnitRelDataWrap extends BaseDataWrap<DicUnitRelMapper, DicUnitRel> {

    /**
     * 物料计量单位换算关系列表分页
     *
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicUnitRelPageVO> getDicUnitRelPageVOList(IPage<DicUnitRelPageVO> page, QueryWrapper<DicUnitRelSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicUnitRelPageVOList(page, wrapper));
    }
}
