package com.inossem.wms.bizbasis.masterdata.org.service.biz;

import com.inossem.wms.bizbasis.masterdata.org.dao.DicWhMapper;
import com.inossem.wms.common.enums.EnumFreezeType;
import com.inossem.wms.common.enums.EnumStorageTypeWhType;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.inossem.wms.bizbasis.common.service.biz.CheckDataService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.common.cache.ICacheService;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.org.wh.po.DicWhImport;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumCheckType;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.DicDeleteCheckPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.org.storagetype.entity.DicWhStorageType;
import com.inossem.wms.common.model.org.wh.dto.DicWhDTO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.org.wh.po.DicWhSavePO;
import com.inossem.wms.common.model.org.wh.po.DicWhSearchPO;
import com.inossem.wms.common.model.org.wh.vo.DicWhPageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageBinDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageTypeDataWrap;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WhService {

    @Autowired
    protected CheckDataService checkDataService;
    @Autowired
    private DicWhDataWrap dicWhDataWrap;
    @Autowired
    private DicWhMapper dicWhMapper;
    @Autowired
    private EditCacheService editCacheService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private DicWhStorageTypeDataWrap dicWhStorageTypeDataWrap;
    @Autowired
    private DicWhStorageBinDataWrap dicWhStorageBinDataWrap;
    @Autowired
    protected ICacheService cacheServiceImpl;

    /**
     * 获取仓库列表
     *
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1 15:41
     * @return 仓库详情列表
     */
    public PageObjectVO<DicWhPageVO> getPage(BizContext ctx) {
        DicWhSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("获取仓库列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new DicWhSearchPO();
        }
        // 查询条件设置
        QueryWrapper<DicWhSearchPO> wrapper = new QueryWrapper<>();
        // 拼装参数
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getWhCode()), DicWhSearchPO::getWhCode, po.getWhCode())
            .like(UtilString.isNotNullOrEmpty(po.getWhName()), DicWhSearchPO::getWhName, po.getWhName());
        IPage<DicWhPageVO> page = po.getPageObj(DicWhPageVO.class);
        dicWhDataWrap.getDicWhPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 获取所有仓库号-全量
     * @param ctx
     * @return
     */
    public MultiResultVO<DicWh> getWhList(BizContext ctx) {
        List<DicWh> dicWhs = dicWhMapper.selectList(null);
        return new MultiResultVO<>(dicWhs);
    }


    /**
     * 获取仓库详情
     *
     * @param ctx 上下文对象
     * @return 仓库详情
     *
     */
    public SingleResultVO<DicWhDTO> get(BizContext ctx) {
        Long whId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("仓库详情查询 whId：{}", whId);
        if (UtilNumber.isEmpty(whId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        DicWh dicWh = dicWhDataWrap.getById(whId);
        log.info("仓库id：{}，详情：{}", whId, JSONObject.toJSONString(dicWh));
        DicWhDTO dto = UtilBean.newInstance(dicWh, DicWhDTO.class);

        // 填充数据
        dataFillService.fillAttr(dto);
        return new SingleResultVO<>(dto);
    }

    /**
     * 新增或修改方法
     *
     * @param ctx 上下文对象
     *
     */
    public void addOrUpdate(BizContext ctx) {
        DicWhSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("新增/修改仓库信息 po：{}", JSONObject.toJSONString(po));
        if (null == po.getWhInfo()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicWhDTO dto = po.getWhInfo();

        if (UtilString.isNullOrEmpty(dto.getWhCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 根据是否存在ID判断是否为新增
        if (UtilNumber.isEmpty(dto.getId())) {
            // 新增
            dto.setCreateUserId(currentUser.getId());
            QueryWrapper<DicWh> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DicWh::getWhCode, dto.getWhCode());
            DicWh dicWh = dicWhDataWrap.getOne(queryWrapper);
            if (dicWh != null){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WH_CODE_EXIST);
            }
        }
        // 修改【校验id是否存在】
        else if (null == dicWhDataWrap.getById(dto.getId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_WAREHOUSE_NOT_EXIST);
        }
        dto.setModifyUserId(currentUser.getId());

        DicWh dicWh = UtilBean.newInstance(dto, DicWh.class);
        // 更新集团仓库号
        updateGroupWhNo(dicWh);
        if (dicWhDataWrap.saveOrUpdate(dicWh)) {
            log.info("仓库：{}，保存成功", dicWh.getWhCode());
            // 刷新缓存
            editCacheService.refreshWhCache();
        }

        // 新增仓库同时新增临时存储区和仓位
        if (UtilNumber.isEmpty(dto.getId())) {
            // 批量新增临时存储区
            multiSaveDefaultStorageType(dicWh.getId(), currentUser.getId());
            editCacheService.refreshStorageTypeCache();
            // 批量新增临时仓位
            multiSaveDefaultStorageBin(dicWh.getId(), currentUser.getId());
            editCacheService.refreshBinCache();
        }
    }

    private void updateGroupWhNo(DicWh dicWh) {
        String whCode = dicWh.getWhCode();
        int len = whCode.length();
        if (!whCode.startsWith("S") || len > 3 || len == 1) {
            dicWh.setGroupWhNo("");
            return;
        }
        String subWhCode = whCode.substring(1);
        int subLen = subWhCode.length();
        if (subLen < 4) {
            StringBuilder builder = new StringBuilder("");
            for (int i = 4; i > subLen; i--) {
                builder.append("0");
            }
            builder.append(subWhCode);
            dicWh.setGroupWhNo(builder.toString());
            return;
        }
        dicWh.setGroupWhNo("");
    }

    /**
     * 批量新增whId下的临时存储区
     *
     * @param whId 仓库id
     * @param userId 创建人id
     */
    public void multiSaveDefaultStorageType(Long whId, Long userId) {
        Set<String> typeSet = UtilConst.getInstance().getDefaultStorageTypeCodeSet();
        List<DicWhStorageType> storageTypeList = new ArrayList<>();
        for (String typeCode : typeSet) {
            DicWhStorageType storageType = new DicWhStorageType();
            storageType.setWhId(whId);
            storageType.setTypeCode(typeCode);
			storageType.setWhType(EnumStorageTypeWhType.PLANE_WH.getValue());
            storageType.setTypeName(EnumDefaultStorageType.getEnumByCode(typeCode).getTypeName());
            storageType.setIsDefault(EnumRealYn.TRUE.getIntValue());
            storageType.setCreateUserId(userId);
            storageType.setModifyUserId(userId);
            storageTypeList.add(storageType);
        }
        dicWhStorageTypeDataWrap.saveBatch(storageTypeList);
    }

    /**
     * 批量新增whId下的临时仓位
     *
     * @param whId 仓库id
     * @param userId 创建人id
     */
    public void multiSaveDefaultStorageBin(Long whId, Long userId) {
        QueryWrapper<DicWhStorageType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicWhStorageType::getWhId, whId).eq(DicWhStorageType::getIsDefault, EnumRealYn.TRUE.getIntValue());
        List<DicWhStorageType> storageTypeList = dicWhStorageTypeDataWrap.list(queryWrapper);
        List<DicWhStorageBin> storageBinList = new ArrayList<>();
        for (DicWhStorageType storageType : storageTypeList) {
            DicWhStorageBin storageBin = new DicWhStorageBin();
            storageBin.setBinCode(EnumDefaultStorageType.getEnumByCode(storageType.getTypeCode()).getBinCode());
            storageBin.setWhId(whId);
            storageBin.setTypeId(storageType.getId());
            storageBin.setIsDefault(EnumRealYn.TRUE.getIntValue());
            storageBin.setCreateUserId(userId);
            storageBin.setModifyUserId(userId);
			storageBin.setFreezeInput(storageBin.getFreezeInput() == null ? EnumFreezeType.NOT_FROZEN.getValue() : storageBin.getFreezeInput());
			storageBin.setFreezeOutput(storageBin.getFreezeOutput() == null ? EnumFreezeType.NOT_FROZEN.getValue() : storageBin.getFreezeOutput());
            storageBinList.add(storageBin);
        }
        dicWhStorageBinDataWrap.saveBatch(storageBinList);
    }

    /**
     * 删除方法
     *
     * @param ctx 上下文对象
     *
     */
    public String remove(BizContext ctx) {
        Long whId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("删除仓库 whId：{}", whId);
        if (UtilNumber.isEmpty(whId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验是否可删除
        checkDataService.dicDeleteCheck(new DicDeleteCheckPO(EnumCheckType.WH.getValue(), whId));
        String whCode = dicWhDataWrap.getById(whId).getWhCode();
        // 逻辑删除
        if (dicWhDataWrap.removeById(whId)) {
            log.info("仓库：{}，删除成功", whId);
            // 从缓存中删除
            editCacheService.deleteWhCacheById(whId);
            // 删除对应默认存储区
            QueryWrapper<DicWhStorageType> storageTypeQueryWrapper = new QueryWrapper<>();
            storageTypeQueryWrapper.lambda().eq(DicWhStorageType::getWhId, whId).eq(DicWhStorageType::getIsDefault, EnumRealYn.TRUE.getIntValue());
            dicWhStorageTypeDataWrap.remove(storageTypeQueryWrapper);
            // 删除对应默认仓位
            QueryWrapper<DicWhStorageBin> storageBinQueryWrapper = new QueryWrapper<>();
            storageBinQueryWrapper.lambda().eq(DicWhStorageBin::getWhId, whId).eq(DicWhStorageBin::getIsDefault, EnumRealYn.TRUE.getIntValue());
            dicWhStorageBinDataWrap.remove(storageBinQueryWrapper);
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_WH_DELETE_FAILURE, whCode);
        }
        return whCode;
    }

    /**
     * 仓库初始化导入
     *
     * @param ctx 上下文对象
     *
     */
    public void importWh(MultipartFile file, BizContext ctx) {
        //获取当前用户
        CurrentUser user = ctx.getCurrentUser();
        Map<String,String> whMap = new HashMap<String,String>();
        try {
            List<DicWhImport> whImportList = (List<DicWhImport>) UtilExcel.readExcelData(file.getInputStream(), DicWhImport.class);
            whImportList.forEach(
                    wh -> {
                        // 判断EXCEL中是否存在相同的仓库编码
                        if(UtilString.isNotNullOrEmpty(whMap.get(wh.getWhCode()))){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE,wh.getWhCode());
                        }else{
                            whMap.put(wh.getWhCode(),wh.getWhCode());
                        }
                        // 判断EXCEL中仓库编码是否在缓存中存在
                        Long whId = dictionaryService.getWhIdCacheByCode(wh.getWhCode());
                        if(UtilNumber.isNotEmpty(whId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_WH_CODE_EXIST,wh.getWhCode());
                        }
                        wh.setCreateUserId(user.getId());
                        wh.setModifyUserId(user.getId());
                    }
            );
            if (dicWhDataWrap.saveBatchDto(whImportList)) {
                // 放入缓存
                List<Long> whIdList = whImportList.stream().map(DicWhImport::getId).collect(Collectors.toList());
                List<DicWh> whList = dicWhDataWrap.listByIds(whIdList);
                for (DicWh wh : whList) {
                    cacheServiceImpl.put(Const.CACHE_WH, wh.getId().toString(), wh);
                    cacheServiceImpl.put(Const.CACHE_WH_ID, wh.getWhCode().toUpperCase(), wh.getId());
                }
            }

            // edit by ChangBaoLong  导入仓库同时新增临时存储区和仓位
            for(DicWhImport whImport : whImportList){
                // 批量新增临时存储区
                multiSaveDefaultStorageType(whImport.getId(), user.getId());
                editCacheService.refreshStorageTypeCache();
                // 批量新增临时仓位
                multiSaveDefaultStorageBin(whImport.getId(), user.getId());
                editCacheService.refreshBinCache();
            }

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }
}
