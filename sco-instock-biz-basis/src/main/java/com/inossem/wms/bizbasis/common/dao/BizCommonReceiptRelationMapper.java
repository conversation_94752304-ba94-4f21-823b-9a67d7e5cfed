package com.inossem.wms.bizbasis.common.dao;

import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import org.apache.ibatis.annotations.Mapper;

import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 单据行关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-02
 */
@Mapper
public interface BizCommonReceiptRelationMapper extends WmsBaseMapper<BizCommonReceiptRelation> {

    void deleteByHeadId(@Param("receiptType") int receiptType, @Param("headId") long headId, @Param("flag") int flag);

    List<BizCommonReceiptRelationDTO> queryRelationByHeadId(@Param("receiptType") int receiptType, @Param("headId") long headId );

}
