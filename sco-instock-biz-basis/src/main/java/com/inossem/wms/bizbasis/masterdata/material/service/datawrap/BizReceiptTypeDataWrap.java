package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.masterdata.receipt.dao.BizReceiptTypeMapper;
import com.inossem.wms.common.model.masterdata.receipt.entity.BizReceiptType;
import com.inossem.wms.common.model.masterdata.receipt.po.BizReceiptSearchPO;
import com.inossem.wms.common.model.masterdata.receipt.vo.BizReceiptPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 单据类型对应存储区表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Service
public class BizReceiptTypeDataWrap extends BaseDataWrap<BizReceiptTypeMapper, BizReceiptType> {

    /**
     * 获取单据类型对应存储区列表-分页
     * @param page
     * @param wrapper
     */
    public IPage<BizReceiptPageVO> getDicReceiptTypePageVOList(IPage<BizReceiptPageVO> page, QueryWrapper<BizReceiptSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicReceiptTypePageVOList(page, wrapper));
    }
}
