package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.org.section.entity.DicWhStorageSection;
import com.inossem.wms.common.model.org.section.po.DicWhStorageSectionSearchPO;
import com.inossem.wms.common.model.org.section.vo.DicWhStorageSectionPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicWhStorageSectionMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 存储区表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Service
public class DicWhStorageSectionDataWrap extends BaseDataWrap<DicWhStorageSectionMapper, DicWhStorageSection> {

    /**
     * 存储区元列表分页
     *
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicWhStorageSectionPageVO> getDicWhStorageSectionPageVOList(IPage<DicWhStorageSectionPageVO> page, QueryWrapper<DicWhStorageSectionSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicWhStorageSectionPageVOList(page, wrapper));
    }

    /**
     * 获取所有存储区列表-全量
     */
    public List<DicWhStorageSectionPageVO> getSectionList() {
        return (this.baseMapper.selectSectionList());
    }


    /**
     * 根据存储类型id获取存储区
     * @param typeId
     * @return
     */
    public List<DicWhStorageSection> selectSectionByTypeId(long typeId) {
        return this.baseMapper.selectSectionByTypeId(typeId);
    }
}
