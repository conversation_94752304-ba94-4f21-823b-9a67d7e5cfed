package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicWhStorageBinMapper;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.masterdata.storagebin.po.DicWhStorageBinSearchPO;
import com.inossem.wms.common.model.masterdata.storagebin.vo.DicWhStorageBinPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 仓位表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class DicWhStorageBinDataWrap extends BaseDataWrap<DicWhStorageBinMapper, DicWhStorageBin> {

    /**
     * 仓位列表分页
     *
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicWhStorageBinPageVO> getDicWhStorageBinPageVOList(IPage<DicWhStorageBinPageVO> page, WmsQueryWrapper<DicWhStorageBinSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicWhStorageBinPageVOList(page, wrapper));
    }

    /**
     * 获取全部仓位
     *
     * @return List<DicWhStorageBinDTO>
     */
    public List<DicWhStorageBinDTO> getAll() {
        return  this.baseMapper.getAll();
    }
}
