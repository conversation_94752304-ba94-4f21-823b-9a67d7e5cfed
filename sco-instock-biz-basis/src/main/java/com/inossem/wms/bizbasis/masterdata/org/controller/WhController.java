package com.inossem.wms.bizbasis.masterdata.org.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.org.wh.dto.DicWhDTO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.org.wh.po.DicWhSavePO;
import com.inossem.wms.common.model.org.wh.po.DicWhSearchPO;
import com.inossem.wms.common.model.org.wh.vo.DicWhPageVO;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 仓库主数据表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@RestController
@Api(tags = "仓库管理")
public class WhController {

    @Autowired
    private WhService whService;

    /**
     * 获取仓库列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 仓库集合列表
     */
    @ApiOperation(value = "获取仓库列表", tags = {"仓库管理"})
    @PostMapping(path = "/org/wh/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWhPageVO>> getPage(@RequestBody DicWhSearchPO po, BizContext ctx) {
        return BaseResult.success(whService.getPage(ctx));
    }


    /**
     * 获取仓库列表-全量
     *
     * @param ctx 上下文对象
     * @return 仓库集合列表
     */
    @ApiOperation(value = "获取仓库列表", tags = {"仓库管理"})
    @PostMapping(path = "/org/wh/results/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicWh>> getWhList(BizContext ctx) {
        return BaseResult.success(whService.getWhList(ctx));
    }


    /**
     * 查看仓库详情
     * 
     * <AUTHOR>
     * @param id 仓库Id
     * @return 仓库详情
     */
    @ApiOperation(value = "按照仓库编码查找仓库", tags = {"仓库管理"})
    @GetMapping(path = "/org/wh/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicWhDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(whService.get(ctx));
    }

    /**
     * 新增仓库
     * 
     * <AUTHOR>
     * @param po 仓库入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增仓库信息", notes = "对仓库信息进行添加", tags = {"仓库管理"})
    @PostMapping(path = "/org/wh", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicWhSavePO po, BizContext ctx) {
        // 储存仓库信息
        whService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WH_SAVE_SUCCESS, po.getWhInfo().getWhCode());
    }

    /**
     * 新增或修改仓库
     *
     * <AUTHOR>
     * @param po 仓库入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改仓库信息", notes = "对仓库信息进行修改", tags = {"仓库管理"})
    @PutMapping(path = "/org/wh", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicWhSavePO po, BizContext ctx) {
        // 储存仓库信息
        whService.addOrUpdate(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WH_SAVE_SUCCESS, po.getWhInfo().getWhCode());
    }

    /**
     * 删除仓库
     * 
     * @param id 仓库Id
     * <AUTHOR>
     * @param ctx 上下文对象
     * @return 删除结果
     */
    @ApiOperation(value = "按照仓库编码删除仓库", notes = "逻辑删除", tags = {"仓库管理"})
    @DeleteMapping(path = "/org/wh/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除仓库信息
        String whCode = whService.remove(ctx);
        // 返回成功标识
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WH_DELETE_SUCCESS, whCode);
    }

    /**
     * 仓库导入
     * @param file 仓库excel
     * @param ctx 上下文对象
     * <AUTHOR>
     */
    @ApiOperation(value = "仓库导入", notes = "仓库导入", tags = {"仓库管理"})
    @PostMapping(path = "/org/wh/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestPart("file") MultipartFile file, BizContext ctx) {
        // 导入仓库信息
        whService.importWh(file, ctx);
        return BaseResult.success();
    }

}
