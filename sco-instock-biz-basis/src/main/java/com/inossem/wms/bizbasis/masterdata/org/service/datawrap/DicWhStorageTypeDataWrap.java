package com.inossem.wms.bizbasis.masterdata.org.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.org.storagetype.entity.DicWhStorageType;
import com.inossem.wms.common.model.org.storagetype.po.DicWhStorageTypeSearchPO;
import com.inossem.wms.common.model.org.storagetype.vo.DicWhStorageTypePageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.dao.DicWhStorageTypeMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 存储类型表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class DicWhStorageTypeDataWrap extends BaseDataWrap<DicWhStorageTypeMapper, DicWhStorageType> {

    /**
     * 存储类型列表分页
     * 
     * @param page
     * @param wrapper
     * @return
     */
    public IPage<DicWhStorageTypePageVO> getDicWhStorageTypePageVOList(IPage<DicWhStorageTypePageVO> page,
        QueryWrapper<DicWhStorageTypeSearchPO> wrapper) {
        return page.setRecords(this.baseMapper.selectDicWhStorageTypePageVOList(page, wrapper));
    }
}
