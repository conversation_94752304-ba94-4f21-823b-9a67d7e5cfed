package com.inossem.wms.bizbasis.masterdata.material.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnHead;
import com.inossem.wms.common.model.masterdata.mat.info.vo.BizMaterialReturnHeadVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物资返运单抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
public interface BizMaterialReturnHeadMapper extends WmsBaseMapper<BizMaterialReturnHead> {

    /**
     * 物资返运列表-分页
     * @param pageData
     * @param pageWrapper
     * @return
     */
    List<BizMaterialReturnHeadVO> getMaterialReturnList(IPage pageData,
                                                        @Param(Constants.WRAPPER) Wrapper pageWrapper);
    /**
     * 物资返运列表-分页
     * @param pageData
     * @param pageWrapper
     * @return
     */
    List<BizMaterialReturnHeadVO> getMaterialReturnListUnitized(IPage pageData,
                                                        @Param(Constants.WRAPPER) Wrapper pageWrapper);

}
