package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 仓储管理-暂存物项入库申请审批流-监听器
 *
 * <AUTHOR>
 * @date 2022/05/03 15:49
 **/
@Service
public class ListenerTsInputApply extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_TS_INPUT_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");

        // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
        Set<String> alreadySendUserSet = new HashSet<>();

        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点动态配置审批人，查询用户所属部门所属科室的2级审批人
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门所属科室的4级审批人
                String deptCode = deptOfficePO.getDeptCode();
                String officeCode = deptOfficePO.getDeptOfficeCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);

                // 排除掉已经发过待办信息的用户
                userList.removeAll(alreadySendUserSet);
                addApproveUser(delegateTask, userList);
                alreadySendUserSet.addAll(userList);

            }
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点 商务合同部设备仓储科长 2级审批人
            List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CCD, EnumOffice.CCD05, EnumApprovalLevel.LEVEL_2);
            addApproveUser(delegateTask, userCodeList);
        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 三级审批节点 中核凯利部门 3级审批人
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.W21.getCode(), null, EnumApprovalLevel.LEVEL_3);
            addApproveUser(delegateTask, userList);
        }
    }

}
