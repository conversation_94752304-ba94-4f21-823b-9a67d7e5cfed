package com.inossem.wms.system.workflow.listener.storage;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 盘点报告审批流-监听器
 *
 **/
@Service
public class ListenerUnitizedStocktakingPlanReportApply extends ApprovalListener implements TaskListener, ExecutionListener {

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        approvalCallback(delegateExecution, TagConst.APPROVAL_UNITIZED_STOCKTAKING_PLAN_REPORT_APPLY);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String taskDefKey = delegateTask.getTaskDefinitionKey();
        List<MetaDataDeptOfficePO> userDept = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");


        // 根据节点，自定义业务需求
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 设置一个Set，用于保存已经设置过待办信息的用户，避免对同一个用户因关联多个部门而收到多次待办
            Set<String> sendUserSet = new HashSet<>();
            // 一级审批节点动态配置审批人，发起人所属科室负责人
            for (MetaDataDeptOfficePO deptOfficePO : userDept) {
                // 查询用户所属部门的部门负责人
                String deptCode = deptOfficePO.getDeptCode();
                String deptOfficeCode = deptOfficePO.getDeptOfficeCode();
                List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, deptOfficeCode, EnumApprovalLevel.LEVEL_2);
                // 排除掉已经发过待办信息的用户
                sendUserSet.addAll(userList);
            }
            addApproveUser(delegateTask, new ArrayList<>(sendUserSet));
        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点 财务部部门负责人
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FBD.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 三级审批节点 设备采购部部门负责人
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.EPE.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_4_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 四级审批节点 工程管理部部门负责人
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CDE.getCode(), null, EnumApprovalLevel.LEVEL_4);
            addApproveUser(delegateTask, userList);
        } else if (EnumApprovalNode.LEVEL_5_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 五级审批节点 工程管理部分管领导
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.CDE.getCode(), null, EnumApprovalLevel.LEVEL_5);
            addApproveUser(delegateTask, userList);
        }
    }

}
