package com.inossem.wms.common.model.bizdomain.report.vo;

import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "仓位库存查询PDA vo对象", description = "仓位库存查询PDA vo对象")
public class StockBinPdaVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 自定义扩展 *************************/
    /**
     * 标签集合
     */
    private List<BizLabelData> labelDataList;


    /* ********************** 自定义扩展 *************************/

    /* ********************** 扩展字段 *************************/
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称" , example = "物料描述001003")
    private String matName;

    /**
     * 原存储类型编码
     */
    @ApiModelProperty(value = "原存储类型编码" , example = "801")
    private String sourceTypeCode;

    /**
     * 原存储类型描述
     */
    @ApiModelProperty(value = "原存储类型描述" , example = "入库临时区")
    private String typeName;



    /**
     * 原仓位编码
     */
    @ApiModelProperty(value = "原仓位编码" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "原仓位编码" , example = "00")
    private String cellCode;


    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    /**
     * 单位描述
     */
    @ApiModelProperty(value = "单位描述" , example = "个")
    private String unitName;

    /**
     * 精确度
     */
    @ApiModelProperty(value = "精确度 - 小数位" , example = "2")
    private Integer decimalPlace;

    /**
     * 批次信息传输
     */
    @ApiModelProperty(value = "批次信息传输")
    private BizBatchInfoDTO batchInfo;

    /**
     * 批次图片
     */
    @ApiModelProperty(value = "批次图片")
    private List<BizBatchImgDTO> batchImgList;


    /* ********************** 扩展字段 *************************/







    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName", targetAttrName = "matCode,matName")
    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "基本计量单位id" , example = "7")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "batchInfo")
    private Long batchId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "typeCode,typeName")
    @ApiModelProperty(value = "存储类型ID" , example = "155336768028673")
    private Long typeId;

    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    @ApiModelProperty(value = "仓位ID" , example = "155336845623297")
    private Long binId;


    @ApiModelProperty(value = "存储单元id" , example = "152758218981377")
    private Long cellId;



}


