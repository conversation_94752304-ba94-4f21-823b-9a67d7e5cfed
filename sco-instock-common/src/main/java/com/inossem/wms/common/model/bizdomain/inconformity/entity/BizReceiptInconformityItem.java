package com.inossem.wms.common.model.bizdomain.inconformity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 不符合项行项目表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptInconformityItem对象", description="不符合项行项目表")
@TableName("biz_receipt_inconformity_item")
public class BizReceiptInconformityItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id")
    private Long headId;

    @ApiModelProperty(value = "验收单行序号")
    private String rid;

    @ApiModelProperty(value = "质检会签head主键", example = "111")
    private Long signInspectHeadId;

    @ApiModelProperty(value = "质检会签item主键", example = "111")
    private Long signInspectItemId;

    @ApiModelProperty(value = "质检会签单据类型", example = "2320")
    private Integer signInspectType;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型 220:送货通知 240:采购订单 模板:290")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销")
    private Integer itemStatus;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "接收库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "仓库号id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "订单单位id")
    private Long unitId;

    @ApiModelProperty(value = "批次号id")
    private Long batchId;

    @ApiModelProperty(value = "未到货数量")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "合格数量  = 待验收数量-不合格数量-未到货数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "凭证时间")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期")
    private Date postingDate;

    @ApiModelProperty(value = "物料凭证编号")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度")
    private String matDocYear;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true")
    private Integer isPost;

    @ApiModelProperty(value = "冲销凭证时间")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证号")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销物料凭证行项目号")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销标志0-false, 1-true")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "不符合描述")
    private String inconformityReason;

    @ApiModelProperty("解决方案（1原样接收，2有偿补件 ，3无偿补件, 4无需补件）")
    private String solveReason;

    @ApiModelProperty(value = "原样接收数量")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "换货数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "维修数量")
    private BigDecimal repairQty;

    @ApiModelProperty(value = "不合格原因")
    private String unqualifiedReason;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    /** 质量差异时会使用此字段作为方案说明 */
    @ApiModelProperty(value = "解决方案说明")
    private String solveDescription;

    @ApiModelProperty(value = "多供的物料编码")
    private String extraMatCode;

    @ApiModelProperty(value = "多供的物料描述")
    private String extraMatName;

    @ApiModelProperty(value = "多供物料的单位id")
    private Long extraUnitId;

    @ApiModelProperty(value = "多供物料填写的需求人")
    private String extraApplyUser;

    @ApiModelProperty(value = "是否参与单据拆分，0 否 1是")
    private Integer isSplit;

    @ApiModelProperty(value = "需求计划id")
    private Long demandPlanId;

    @ApiModelProperty(value = "需求计划编号")
    private String demandPlanCode;

    @ApiModelProperty("建议处置方式(1原样接收、2重新补件、3无需补件)")
    private String disposalMethod;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;
    
    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty("废弃")
    private Integer solution;

    @ApiModelProperty("废弃")
    private Integer finalSolution;


    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;
}
