package com.inossem.wms.common.model.bizdomain.task.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.constant.task.TaskConst;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业请求item 数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(value = "作业请求item 数据传输对象", description = "作业请求item 数据传输对象")
public class BizReceiptTaskReqItemDTO implements Serializable {
    private static final long serialVersionUID = 1148289963606572836L;

    /* ********************** 扩展字段 *************************/

    @ExcelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "物资ID")
    private String extend20;

    @ApiModelProperty(value = "是否精准预留（0:否；1:是）")
    private Integer isExact;

    @ApiModelProperty(value = "领料单位")
    private String usedDeptName;

    @ApiModelProperty(value = "是否超发【1是，0否】")
    private Integer isOver;

    @ApiModelProperty(value = "保养备注")
    private String mainRequirement;

    @ApiModelProperty(value = "提交数量" , example = "10")
    private BigDecimal submitQty;

    @ApiModelProperty(value = "提交重量" , example = "10")
    private BigDecimal variableWeight;

    /**
     * 前续单据code
     */
    @ApiModelProperty(value = "前续单据code" , example = "RK0001000639")
    private String preReceiptCode;

    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码" , example = "8000")
    private String ftyCode;

    /**
     * 工厂描述
     */
    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    private String ftyName;

    /**
     * 库存地点编码
     */
    @ApiModelProperty(value = "库存地点编码", name = "locationCode", required = true)
    private String locationCode;

    /**
     * 库存地点描述
     */
    @ApiModelProperty(value = "库存地点描述", name = "locationName", required = true)
    private String locationName;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码" , example = "S200")
    private String whCode;

    /**
     * 仓库描述
     */
    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    /**
     * 物料信息
     */
    @ApiModelProperty(value = "物料信息")
    private DicMaterialDTO matInfo;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码" , example = "7")
    private String unitCode;

    /**
     * 单位描述
     */
    @ApiModelProperty(value = "单位描述" , example = "个")
    private String unitName;

    /**
     * 精确度
     */
    @ApiModelProperty(value = "精确度 - 小数位" , example = "2")
    private Integer decimalPlace;

    /**
     * 批次信息传输
     */
    @ApiModelProperty(value = "批次信息传输")
    private BizBatchInfoDTO batchInfo;

    /**
     * 推荐仓位信息
     */
    @ApiModelProperty(value = "推荐仓位信息")
    private DicWhStorageBinDTO storageBin;

    /**
     * 批次图片
     */
    @ApiModelProperty(value = "批次图片")
    private List<BizBatchImgDTO> batchImgList;

    /**
     * 仓位库存
     */
    @ApiModelProperty(value = "仓位库存")
    private List<StockBinDTO> stockBinList;

    /**
     * 标签列表
     */
    @ApiModelProperty(value = "标签列表")
    private List<BizLabelReceiptRelDTO> labelReceiptRelDTOList;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /* ********************** 扩展字段 结束*************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "作业申请行项目rid" , example = "1")
    private String rid;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "前续单据headId" , example = "151559623213057")
    @RlatAttr(rlatTableName = {TaskConst.RECEIPT_TYPE_INPUT, TaskConst.RECEIPT_TYPE_OUTPUT, TaskConst.RECEIPT_TYPE_TRANSPORT, TaskConst.RECEIPT_TYPE_RETURN},
            sourceAttrName = "receiptCode,isOver,createUserId",
            targetAttrName = "preReceiptCode,isOver,preCreateUserId")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据行id" , example = "151565235191810")
    @RlatAttr(rlatTableName = "biz_receipt_output_item",
            sourceAttrName = "preReceiptHeadId",
            targetAttrName = "preApplyHeadId")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据headId" , example = "151559623213057")
    @RlatAttr(rlatTableName = "biz_receipt_apply_head",
            sourceAttrName = "receiptCode",
            targetAttrName = "preApplyReceiptCode")
    private Long preApplyHeadId;

    @ApiModelProperty(value = "前续申请单code")
    private String preApplyReceiptCode;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "preCreateUserCode,preCreateUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long preCreateUserId;

    /**
     * 申请人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String preCreateUserCode;

    /**
     * 申请人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String preCreateUserName;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;



    @ApiModelProperty(value = "前续订单bin id" , example = "144980056277019")
    private Long preReceiptBinId;

    @ApiModelProperty(value = "参考单id" , example = "152044279234561")
    @RlatAttr(rlatTableName = "biz_receipt_apply_head", sourceAttrName = "isExact,usedDeptName", targetAttrName = "isExact,usedDeptName")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行id" , example = "152044279234562")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据配货id" , example = "1")
    @RlatAttr(rlatTableName = "biz_receipt_apply_bin", sourceAttrName = "extend20,functionalLocationCode", targetAttrName = "extend20,functionalLocationCode")
    private Long referReceiptBinId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "*", targetAttrName = "matInfo")
    private Long matId;

    @ApiModelProperty(value = "库存状态 10-非限制库存 ,20-在途库存,30-质量检验库存 ,40-冻结的库存 debit_credit为S 时 目标库存状态  debit_credit 为H时 源库存状态" , example = "10")
    private Integer stockStatus;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "batchInfo")
    private Long batchId;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "基本计量单位id" , example = "7")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "物料凭证号" , example = "511111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行项目" , example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证编号id" , example = "152286305255426")
    private Long insDocId;

    @ApiModelProperty(value = "物料凭证中的项目" , example = "1")
    private String insDocRid;

    @ApiModelProperty(value = "移动类型特殊库存标识" , example = "Q")
    private String moveTypeSpecStock;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否核销【1是，0否】" , example = "0")
    private Boolean debtOffset;

    @ApiModelProperty(value = "推荐仓位id" , example = "152407797465099")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "*", targetAttrName = "storageBin")
    private Long recommendBinId;

    @ApiModelProperty(value = "特性code" , example = "biz_batch_info.batch_erp,biz_batch_info.spec_stock_code")
    private String specCode;

    @ApiModelProperty(value = "特性值" , example = "111111,1")
    private String specValue;

    @ApiModelProperty(value = "特性库存类型" , example = "Q")
    private String specStock;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    private String specStockCode;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组id" )
    private Long matGroupId;

    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @ApiModelProperty(value = "子行项目" , example = "240")
    private List<BizReceiptTaskReqItemDTO> childItemList;

    @ApiModelProperty(value = "合并下架请求单id" )
    private Long mergeReqHeadId;

    @ApiModelProperty(value = "合并下架请求单行id" )
    private Long mergeReqItemId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

}
