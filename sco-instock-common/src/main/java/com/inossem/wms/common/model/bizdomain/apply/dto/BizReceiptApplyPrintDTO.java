package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 借用申请单打印传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="借用申请单打印传输对象", description="借用申请单打印传输对象")
public class BizReceiptApplyPrintDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;
    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    @ApiModelProperty(value = "领料单号")
    private String receiptNum;

    @ApiModelProperty(value = "领料单描述")
    private String receiptRemark;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "安装位置")
    private String installSite;

    @ApiModelProperty(value = "安装系统")
    private String installSystem;

    @ApiModelProperty(value = "WBS编号" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "WBS编码" , example = "wbsCode2")
    private String whCodeOut;

    @ApiModelProperty(value = "WBS描述" , example = "wbsName2")
    private String specStockName;

    @SonAttr(sonTbName = "biz_receipt_apply_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 申请行项目")
    private List<BizReceiptApplyItemDTO> itemList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 借用方式描述【1：长期借用单；2：短期借用单】")
    private String borrowTypeI18n;

    @ApiModelProperty(value = "填充属性 - 借用部门")
    private String deptCode;

    @ApiModelProperty(value = "填充属性 - 借用部门描述")
    private String deptName;

    @ApiModelProperty(value = "填充属性 - 借用科室")
    private String deptOfficeCode;

    @ApiModelProperty(value = "填充属性 - 借用科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 暂存人编码" , example = "Admin")
    private String receiverCode;

    @ApiModelProperty(value = "填充属性 - 暂存人名称" , example = "管理员")
    private String receiverName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 对口部门Code" , example = "0", required = false)
    private String counterpartDeptCode;

    @ApiModelProperty(value = "填充属性 - 对口部门Name" , example = "0", required = false)
    private String counterpartDeptName;

    @ApiModelProperty(value = "填充属性 - 对口科室Code" , example = "0", required = false)
    private String counterpartOfficeCode;

    @ApiModelProperty(value = "填充属性 - 对口科室Name" , example = "0", required = false)
    private String counterpartOfficeName;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【借用申请：9050；维修申请：9051；报废申请：9052】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "借用方式【1：长期借用单；2：短期借用单】")
    private Integer borrowType;

    @ApiModelProperty(value = "申请部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    @ApiModelProperty(value = "申请科室id")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeCode,deptOfficeName", targetAttrName = "deptOfficeCode,deptOfficeName")
    private Long deptOfficeId;

    @ApiModelProperty(value = "预计借用天数（短期借用单必填）")
    private Integer estimateBorrowDay;

    @ApiModelProperty(value = "维修厂商")
    private String repairFty;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "移动类型id")
    private Long moveTypeId;

    @ApiModelProperty(value = "领料出库单信息id")
    @RlatAttr(rlatTableName = "biz_receipt_output_info",
            sourceAttrName = "receiptNum,receiptRemark,matReceiverId,matDeptId,applyTime,installSite,installSystem,specStockCode,specStockName,whCodeOut,deptId,deptOfficeId",
            targetAttrName = "receiptNum,receiptRemark,matReceiverId,matDeptId,applyTime,installSite,installSystem,specStockCode,specStockName,whCodeOut,counterpartDeptId,counterpartOfficeId")
    private Long outInfoId;

    @ApiModelProperty(value = "是否为内部领料【1是，0否】" , example = "0", required = false)
    private Integer isInnerFlag;

    @ApiModelProperty(value = "对口部门Id" , example = "0", required = false)
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "counterpartDeptCode,counterpartDeptName")
    private Long counterpartDeptId;

    @ApiModelProperty(value = "对口科室Id" , example = "0", required = false)
    @RlatAttr(rlatTableName = "dic_dept_office",sourceAttrName = "deptOfficeCode,deptOfficeName",targetAttrName = "counterpartOfficeCode,counterpartOfficeName")
    private Long counterpartOfficeId;

    @ApiModelProperty(value = "暂存人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "receiverCode,receiverName")
    private Long receiverId;

    @ApiModelProperty(value = "需求部门")
    private String applyDeptName;

    @ApiModelProperty(value = "领料人")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "matReceiverCode,matReceiverName")
    private Long matReceiverId;

    @ApiModelProperty(value = "填充属性 -领料人编码" , example = "Admin")
    private String matReceiverCode;

    @ApiModelProperty(value = "填充属性 - 领料人名称" , example = "管理员")
    private String matReceiverName;

    @ApiModelProperty(value = "填充属性 - 领用部门编码")
    private String matDeptCode;

    @ApiModelProperty(value = "填充属性 - 领用部门描述")
    private String matDeptName;

    @ApiModelProperty(value = "领用部门")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "matDeptCode,matDeptName")
    private Long matDeptId;

    private String des;
    // 转性标识
    private Integer transferFlag;

    @ApiModelProperty(value = "填充属性 - 暂存部门描述")
    private String tempDeptName;

    @ApiModelProperty(value = "填充属性 - 存放原因")
    private String tempStoreReason;

    @ApiModelProperty(value = "填充属性 - 暂存类型 首次暂存0 延期暂存1")
    private Integer tempStoreType;

    @ApiModelProperty(value = "填充属性 - 暂存类型 首次暂存0 延期暂存1")
    private String tempStoreTypeI18n;

    @ApiModelProperty(value = "填充属性 - 暂存物项暂存人部门负责人")
    private String tempStoreHeadUser;

    @ApiModelProperty(value = "审批人名称", example = "159843409264782", required = false)
    private String assigneeName;

    @ApiModelProperty(value = "结束时间", example = "159843409264782", required = false)
    private String endTime;

    @ApiModelProperty(value = "用户签名", example = "", required = false)
    private String autograph;

    @ApiModelProperty(value = "审批人名称", example = "159843409264782", required = false)
    private String assigneeNameLevel1;

    @ApiModelProperty(value = "结束时间", example = "159843409264782", required = false)
    private String endTimeLevel1;

    @ApiModelProperty(value = "用户签名", example = "", required = false)
    private String autographLevel1;

    @ApiModelProperty(value = "审批人名称", example = "159843409264782", required = false)
    private String assigneeNameLevel2;

    @ApiModelProperty(value = "结束时间", example = "159843409264782", required = false)
    private String endTimeLevel2;

    @ApiModelProperty(value = "用户签名", example = "", required = false)
    private String autographLevel2;

    @ApiModelProperty(value = "审批人名称", example = "159843409264782", required = false)
    private String assigneeNameLevel3;

    @ApiModelProperty(value = "结束时间", example = "159843409264782", required = false)
    private String endTimeLevel3;

    @ApiModelProperty(value = "用户签名", example = "", required = false)
    private String autographLevel3;

    @ApiModelProperty(value = "领用部门")
    private String createUserDepartment;
    
    @ApiModelProperty(value = "首次暂存")
    private String tempStoreTypeF;
    
    @ApiModelProperty(value = "延期暂存")
    private String tempStoreTypeR;


}
