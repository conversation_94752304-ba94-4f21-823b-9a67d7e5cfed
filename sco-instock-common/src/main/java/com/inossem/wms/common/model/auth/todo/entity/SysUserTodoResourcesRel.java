package com.inossem.wms.common.model.auth.todo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 用户与待办资源(菜单)关系表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysUserTodoResourcesRel对象", description="用户与待办资源(菜单)关系表")
@TableName("sys_user_todo_resources_rel")
public class SysUserTodoResourcesRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户ID", name = "userId", example = "1")
    private Long userId;

    @ApiModelProperty(value = "待办资源(菜单)id" , example = "148309173927937")
    private Long resourcesId;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;


}
