package com.inossem.wms.common.model.bizdomain.apply.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 门到门送货申请行项目导出VO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptD2dDeliveryApplyItemExportVO", description = "门到门送货申请行项目导出VO")
public class BizReceiptD2dDeliveryApplyItemExportVO implements Serializable {

    @ExcelProperty(value = "门到门送货申请", index = 0)
    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ExcelProperty(value = "申请原因", index = 1)
    @ApiModelProperty(value = "申请原因")
    private String applyReason;

    @ExcelProperty(value = "要求到货时间", index = 2)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ApiModelProperty(value = "要求到货时间")
    private Date requiredArrivalTime;

    @ExcelIgnore
    @ApiModelProperty(value = "物料紧急程度，1特急2紧急3一般")
    private Integer logisticsUrgency;

    @ExcelProperty(value = "物流紧急程度", index = 3)
    @ApiModelProperty(value = "物流紧急程度")
    private String logisticsUrgencyI18n;

    @ExcelProperty(value = "创建人", index = 4)
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ExcelProperty(value = "创建时间", index = 5)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ExcelProperty(value = "需求计划单号", index = 6)
    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ExcelProperty(value = "需求人", index = 7)
    @ApiModelProperty(value = "需求人")
    private String demandPerson;

    @ExcelProperty(value = "物料编码", index = 8)
    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "物料描述", index = 9)
    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ExcelProperty(value = "物料描述(英文)", index = 10)
    @ApiModelProperty(value = "品名（英文）")
    private String matNameEn;

    @ExcelProperty(value = "计量单位", index = 11)
    @ApiModelProperty(value = "计量单位名称")
    private String unitName;

    @ExcelProperty(value = "门到门送货数量", index = 12)
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ExcelProperty(value = "预估重量小记(KG)", index = 13)
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    @ExcelIgnore
    @ApiModelProperty(value = "币种")
    private String currency;

    @ExcelProperty(value = "币种", index = 14)
    @ApiModelProperty(value = "币种")
    private String currencyI18n;

    @ExcelProperty(value = "合同号", index = 15)
    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ExcelProperty(value = "合同名称", index = 16)
    @ApiModelProperty(value = "合同名称")
    private String contractName;

}
