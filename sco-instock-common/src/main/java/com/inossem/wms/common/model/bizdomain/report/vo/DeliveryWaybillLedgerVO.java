package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 运单管理台账
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "运单管理台账 查询出参传输对象")
public class DeliveryWaybillLedgerVO {

    @ApiModelProperty("运单id")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty("送货单抬头id")
    @ExcelIgnore
    private Long deliveryNoticeReceiptHeadId;

    @ApiModelProperty("运单号")
    @ExcelProperty(value = "运单号")
    private String receiptCode;

    @ApiModelProperty("单据状态")
    @ExcelIgnore
    private Integer receiptStatus;

    @ApiModelProperty(value = "单据状态国际化")
    @ExcelProperty(value = "单据状态")
    private String receiptStatusI18n;

    @ApiModelProperty("送货单号")
    @ExcelProperty(value = "送货单号")
    private String deliveryNoticeReceiptCode;

    @ApiModelProperty("送货类型")
    @ExcelIgnore
    private Integer deliveryType;

    @ApiModelProperty(value = "送货类型国际化")
    @ExcelProperty(value = "送货类型")
    private String deliveryTypeI18n;

    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty("合同编号")
    @ExcelProperty(value = "合同编号")
    private String contractReceiptCode;

    @ApiModelProperty("采购员")
    @ExcelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty("货代信息")
    @ExcelProperty(value = "货代信息")
    private String supplierName;

    @ApiModelProperty("贸易信息")
    @ExcelProperty(value = "贸易信息")
    private String tradeInfo;

    @ApiModelProperty("运输方式")
    @ExcelIgnore
    private Integer shippingType;

    @ApiModelProperty("运输方式国际化")
    @ExcelProperty(value = "运输方式")
    private String shippingTypeI18n;

    @ApiModelProperty("货物名称概述")
    @ExcelProperty(value = "货物名称概述")
    private String overviewOfGoodsNames;

    @ApiModelProperty(value = "提单号")
    @ExcelProperty(value = "提单号")
    private String blNo;

    @ApiModelProperty("箱件数量")
    @ExcelProperty(value = "箱件数量")
    private long caseTotalCount;

    @ApiModelProperty("毛重（KG)")
    @ExcelProperty(value = "毛重（KG)")
    private long caseWeightSum;

    @ApiModelProperty("体积 M3")
    @ExcelProperty(value = "体积 M3")
    private long volumeValueSum;

    @ApiModelProperty("货值")
    @ExcelProperty(value = "货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty("创建人")
    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty("订舱时间")
    @ExcelProperty(value = "订舱时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date bookingSpaceTime;

    @ApiModelProperty("托收单号")
    @ExcelProperty(value = "托收单号")
    private String submitRedemptionDocReceiptCode;

    @ApiModelProperty("托收发起日期")
    @ExcelProperty(value = "托收发起日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date submitRedemptionDocStartTime;

    @ApiModelProperty("托收办理完成日期")
    @ExcelProperty(value = "托收办理完成日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date submitRedemptionDocFinishTime;

    @ApiModelProperty("所得税免税单号")
    @ExcelProperty(value = "所得税免税单号")
    private String incomeTaxExemptionReceiptCode;

    @ApiModelProperty("所得税免税发起日期")
    @ExcelProperty(value = "所得税免税发起日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date incomeTaxExemptionStartTime;

    @ApiModelProperty("所得税免税批复日期")
    @ExcelProperty(value = "所得税免税批复日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date incomeTaxExemptionFinishTime;

    @ApiModelProperty("预计发运时间")
    @ExcelProperty(value = "预计发运时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date estimatedShippingTime;

    @ApiModelProperty("实际船名船次或航班信息")
    @ExcelProperty(value = "实际船名船次或航班信息")
    private String actualTransportVehicleInfo;

    @ApiModelProperty("ship id")
    @ExcelProperty(value = "ship id")
    private String shipId;

    @ApiModelProperty("出口报关时间")
    @ExcelProperty(value = "出口报关时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date exportCustomsDeclarationTime;

    @ApiModelProperty("实际离港时间")
    @ExcelProperty(value = "实际离港时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date actualDepartureTime;

    @ApiModelProperty("预计到港时间")
    @ExcelProperty(value = "预计到港时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date estimatedPortOfArrivalTime;

    @ApiModelProperty("实际到港时间")
    @ExcelProperty(value = "实际到港时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date actualPortOfArrivalTime;

    @ApiModelProperty("所得税免税状态备注")
    @ExcelProperty(value = "所得税免税状态备注")
    private String exemption_status_info;

    @ApiModelProperty("托收状态备注")
    @ExcelProperty(value = "托收状态备注")
    private String doc_status_info;

    @ApiModelProperty("清关状态备注")
    @ExcelProperty(value = "清关状态备注")
    private String status_info;

    @ApiModelProperty("货物清关完成时间")
    @ExcelProperty(value = "货物清关完成时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date customsClearanceCompletionTime;

    @ApiModelProperty("目的港提货时间")
    @ExcelProperty(value = "目的港提货时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date destinationTakeDeliveryTime;

    @ApiModelProperty(value = "到场时间")
    @ExcelProperty(value = "到场时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date receiveDate;

    @ApiModelProperty(value = "卡拉奇工行收单时间")
    @ExcelProperty(value = "卡拉奇工行收单时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date klqBackDocTime;

    @ApiModelProperty(value = "卡拉奇工行FI批复时间")
    @ExcelProperty(value = "卡拉奇工行FI批复时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date klqBackFiApprovalTime;

    @ApiModelProperty(value = "到港-到场周期/天")
    @ExcelProperty(value = "到港-到场周期/天")
    private Integer receiveArrivalDays;

    @ApiModelProperty(value = "托收延误周期/天")
    @ExcelProperty(value = "托收延误周期/天")
    private Integer redemptionArrivalDays;

    @ApiModelProperty(value = "所得税延延误周期/天")
    @ExcelProperty(value = "所得税延延误周期/天")
    private Integer finishArrivalDays;

    @ApiModelProperty(value = "内陆运输延误周期/天")
    @ExcelProperty(value = "内陆运输延误周期/天")
    private Integer receiveCompletionDays;

    @ApiModelProperty(value = "清关周期/自然日")
    @ExcelProperty(value = "清关周期/自然日")
    private Integer completionDays;

    @ApiModelProperty(value = "清关周期/工作日")
    @ExcelProperty(value = "清关周期/工作日")
    private Integer completionWeekDays;

    @ApiModelProperty(value = "其他备注")
    @ExcelProperty(value = "其他备注")
    private String otherRemark;

    @ApiModelProperty(value = "物流周期主要影响因素")
    @ExcelProperty(value = "物流周期主要影响因素")
    private String reason;

}