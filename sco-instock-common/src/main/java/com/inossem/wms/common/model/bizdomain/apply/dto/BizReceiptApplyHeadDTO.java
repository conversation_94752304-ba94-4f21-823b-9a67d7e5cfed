package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputSignatureGraphDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 借用申请单抬头传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="借用申请单抬头传输对象", description="借用申请单抬头传输对象")
public class BizReceiptApplyHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;
    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    @ApiModelProperty(value = "领料单号")
    private String receiptNum;

    @ApiModelProperty(value = "领料单描述")
    private String receiptRemark;

    @ApiModelProperty(value = "领料单创建人code" , example = "Admin")
    private String erpCreateUserCode;

    @ApiModelProperty(value = "申请时间")
    private Date applyTime;

    @ApiModelProperty(value = "安装位置")
    private String installSite;

    @ApiModelProperty(value = "安装系统")
    private String installSystem;

    @ApiModelProperty(value = "WBS编号" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "WBS编码" , example = "wbsCode2")
    private String whCodeOut;

    @ApiModelProperty(value = "WBS描述" , example = "wbsName2")
    private String specStockName;

    @SonAttr(sonTbName = "biz_receipt_apply_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 申请行项目")
    private List<BizReceiptApplyItemDTO> itemList;

    @ApiModelProperty(value = "填充属性 - 聚合行项目")
    private List<BizReceiptApplyItemGroupByDTO> groupItemList;

    @SonAttr(sonTbName = "biz_receipt_apply_bin", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 申请行项目")
    private List<BizReceiptApplyBinDTO> binList;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "扩展属性 - 借用方式描述【1：长期借用单；2：短期借用单】")
    private String borrowTypeI18n;

    @ApiModelProperty(value = "填充属性 - 借用部门")
    private String deptCode;

    @ApiModelProperty(value = "填充属性 - 借用部门描述")
    private String deptName;

    @ApiModelProperty(value = "填充属性 - 借用科室")
    private String deptOfficeCode;

    @ApiModelProperty(value = "填充属性 - 借用科室描述")
    private String deptOfficeName;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 创建人部门名称", example = "管理员")
    private String createUserDeptName;

    @ApiModelProperty(value = "填充属性 - 暂存人编码" , example = "Admin")
    private String receiverCode;

    @ApiModelProperty(value = "填充属性 - 暂存人名称" , example = "管理员")
    private String receiverName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String splitUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String splitUserName;

    @ApiModelProperty(value = "专业工程师code")
    private String professionalEngineerUserCode;

    @ApiModelProperty(value = "专业工程师name")
    private String professionalEngineerUserName;

    @ApiModelProperty(value = "填充属性 - 对口部门Code" , example = "0", required = false)
    private String counterpartDeptCode;

    @ApiModelProperty(value = "填充属性 - 对口部门Name" , example = "0", required = false)
    private String counterpartDeptName;

    @ApiModelProperty(value = "填充属性 - 对口科室Code" , example = "0", required = false)
    private String counterpartOfficeCode;

    @ApiModelProperty(value = "填充属性 - 对口科室Name" , example = "0", required = false)
    private String counterpartOfficeName;

    @ApiModelProperty(value = "实际领料人签字信息")
    private BizReceiptOutputSignatureGraphDTO matReceiverSignature;

    @ApiModelProperty(value = "保管员签字信息")
    private BizReceiptOutputSignatureGraphDTO storeKeeperSignature;

    @ApiModelProperty(value = "审核人电子签名信息")
    private BizReceiptOutputSignatureGraphDTO assignerSignature;

    @ApiModelProperty(value = "创建人完整信息")
    private SysUserDTO userDTO;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【借用申请：9050；维修申请：9051；报废申请：9052】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "借用方式【1：长期借用单；2：短期借用单】")
    private Integer borrowType;

    @ApiModelProperty(value = "申请部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "deptCode,deptName")
    private Long deptId;

    @ApiModelProperty(value = "申请科室id")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeCode,deptOfficeName", targetAttrName = "deptOfficeCode,deptOfficeName")
    private Long deptOfficeId;

    @ApiModelProperty(value = "预计借用天数（短期借用单必填）")
    private Integer estimateBorrowDay;

    @ApiModelProperty(value = "维修厂商")
    private String repairFty;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName,commonImgId", targetAttrName = "createUserCode,createUserName,createUserCommonImgId")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "移动类型id")
    private Long moveTypeId;

    @ApiModelProperty(value = "领料出库单信息id")
    @RlatAttr(rlatTableName = "biz_receipt_output_info",
            sourceAttrName = "receiptNum,receiptRemark,matReceiverId,matDeptId,applyTime,installSite,installSystem,specStockCode,specStockName,whCodeOut,deptId,deptOfficeId",
            targetAttrName = "receiptNum,receiptRemark,matReceiverId,matDeptId,applyTime,installSite,installSystem,specStockCode,specStockName,whCodeOut,counterpartDeptId,counterpartOfficeId")
    private Long outInfoId;

    @ApiModelProperty(value = "是否为内部领料【1是，0否】" , example = "0", required = false)
    private Integer isInnerFlag;

    @ApiModelProperty(value = "是否劳保物资【1是，0否】" , example = "0", required = false)
    private Integer isLaborFlag;

    @ApiModelProperty(value = "对口部门Id" , example = "0", required = false)
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "counterpartDeptCode,counterpartDeptName")
    private Long counterpartDeptId;

    @ApiModelProperty(value = "对口科室Id" , example = "0", required = false)
    @RlatAttr(rlatTableName = "dic_dept_office",sourceAttrName = "deptOfficeCode,deptOfficeName",targetAttrName = "counterpartOfficeCode,counterpartOfficeName")
    private Long counterpartOfficeId;

    @ApiModelProperty(value = "暂存人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "receiverCode,receiverName")
    private Long receiverId;

    @ApiModelProperty(value = "需求部门")
    private String applyDeptName;

    @ApiModelProperty(value = "领料人")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "matReceiverCode,matReceiverName")
    private Long matReceiverId;

    @ApiModelProperty(value = "填充属性 -领料人编码" , example = "Admin")
    private String matReceiverCode;

    @ApiModelProperty(value = "填充属性 - 领料人名称" , example = "管理员")
    private String matReceiverName;

    @ApiModelProperty(value = "填充属性 - 领用部门编码")
    private String matDeptCode;

    @ApiModelProperty(value = "填充属性 - 领用部门描述")
    private String matDeptName;

    @ApiModelProperty(value = "领用部门")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "matDeptCode,matDeptName")
    private Long matDeptId;

    private String des;
    // 转性标识
    private Integer transferFlag;

    @ApiModelProperty(value = "填充属性 - 暂存部门描述")
    private String tempDeptName;

    @ApiModelProperty(value = "填充属性 - 存放原因")
    private String tempStoreReason;

    @ApiModelProperty(value = "填充属性 - 暂存类型 首次暂存0 延期暂存1")
    private Integer tempStoreType;

    @ApiModelProperty(value = "填充属性 - 暂存类型 首次暂存0 延期暂存1")
    private String tempStoreTypeI18n;

    @ApiModelProperty(value = "填充属性 - 暂存物项暂存人部门负责人")
    private String tempStoreHeadUser;

    @ApiModelProperty(value = "填充属性 - 暂存物项暂申请备注")
    private String tempRemark;

    @ApiModelProperty(value = "提交人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "填充属性 -提交人编码" , example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称" , example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private Integer receiveType;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private String receiveTypeI18n;

    @ApiModelProperty(value = "工单号" )
    private String workOrder;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "质检会签单ID")
    private Long signInspectionId;

    @ApiModelProperty(value = "质检会签单编码")
    private String signInspectionCode;

    @ApiModelProperty(value = "本批次已受检货物【1：全部；2：部分；】 ")
    private Integer isInspect;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "质检会签单签发日期")
    private Date signInspectionDate;

    @ApiModelProperty(value = "到货日期" , example = "2021-05-15")
    private Date arrivalDate;

    @ApiModelProperty(value = "LOT包号（采购包）")
    private String lotCode;

    @ApiModelProperty(value = "设备供应方")
    private String inspectCompany;

    @ApiModelProperty(value = "仓储承包商")
    private String contractorName;

    @ApiModelProperty(value = "是否反运（0:否；1:是）")
    private Integer backTransport;

    @ApiModelProperty(value = "是否精准预留（0:否；1:是）")
    private Integer isExact;

    @ApiModelProperty(value = "是否拆单（0:否；1:是）")
    private Integer isSplit;

    @ApiModelProperty(value = "创建类型【1:基于出库单入库2:基于物料编码入库】")
    private Integer createType;

    @ApiModelProperty(value = "拆分人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "splitUserCode,splitUserName")
    private Long splitUserId;

    @ApiModelProperty(value = "是否本部门领用（0:否；1:是）")
    private Integer isThisDept;

    @ApiModelProperty(value = "领料单位")
    private String usedDeptName;

    @ApiModelProperty(value = "预计领用时间")
    private Date usedTime;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "professionalEngineerUserCode,professionalEngineerUserName")
    @ApiModelProperty(value = "专业工程师id")
    private Long professionalEngineerUserId;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "是否虚拟出入库物项(1是0否)")
    private Integer isVirtual;

    @ApiModelProperty(value = "领用依据")
    private Integer receiveBasis;

    @ApiModelProperty(value = "领用依据国际化")
    private String receiveBasisI18n;

    @ApiModelProperty(value = "领用依据关联的紧急抢修单编号或者应急抢修审批表编号")
    private String receiveBasisReceiptCode;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName,commonImgId", targetAttrName = "assignUserCode,assignUserName,assignUserCommonImgId")
    @ApiModelProperty(value = "指派的审批人id(专工)")
    private Long assignUserId;

    @ApiModelProperty(value = "审批指派人Code")
    private String assignUserCode;

    @ApiModelProperty(value = "审批指派人name")
    private String assignUserName;

    @ApiModelProperty(value = "紧急领用出库申请单单号")
    private String emergencyMatOrderApplyReceiptCode;

    @ApiModelProperty(value = "是否紧急领用(1是0否)")
    private Integer isEmergency;

    @ApiModelProperty(value = "创建人部门编码", example = "1100", required = true)
    private String createDeptCode;

    @ApiModelProperty(value = "创建人部门名称", example = "采购部", required = true)
    private String createDeptName;

    @ApiModelProperty(value = "创建人部门id")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptCode,deptName", targetAttrName = "createDeptCode,createDeptName")
    private Long createDeptId;

    @ApiModelProperty(value = "创建人科室id" , example = "1", required = false)
    private Long createOfficeId;

    @RlatAttr(rlatTableName = "dic_cost_center", sourceAttrName = "costCenterName", targetAttrName = "costCenterName")
    @ApiModelProperty(value = "成本中心id" , example = "1", required = false)
    private Long costCenterId;

    @ApiModelProperty(value = "成本中心编码" , example = "1", required = false)
    private String costCenterCode;

    @ApiModelProperty(value = "成本中心名称", example = "1", required = false)
    private String costCenterName;

    @ApiModelProperty(value = "wbs编码")
    private String wbsCode;

    @ApiModelProperty(value = "是否计划内零用（1:是；0:否）")
    private Integer innerPlan;

    @ApiModelProperty(value = "实际领料人姓名")
    private String actualReceiverName;

    @ApiModelProperty("资产编码")
    private String assetCode;

    @ApiModelProperty("资产描述")
    private String assetName;

    @ApiModelProperty("资产子编号")
    private String assetSubCode;

    @ApiModelProperty(value = "资产id")
    @RlatAttr(rlatTableName = "dic_asset", sourceAttrName = "assetCode,assetName,assetSubCode", targetAttrName = "assetCode,assetName,assetSubCode")
    private Long assetId;

    @RlatAttr(rlatTableName = "biz_common_img", sourceAttrName = "imgBase64", targetAttrName = "createUserSignImg")
    @ApiModelProperty(value = "填充属性 - 创建人用户签名id" , example = "管理员")
    private Long createUserCommonImgId;

    @ApiModelProperty(value = "创建人签名")
    private String createUserSignImg;

    @RlatAttr(rlatTableName = "biz_common_img", sourceAttrName = "imgBase64", targetAttrName = "assignUserSignImg")
    @ApiModelProperty(value = "填充属性 - 审批人用户签名id" , example = "管理员")
    private Long assignUserCommonImgId;

    @ApiModelProperty(value = "审批人签名")
    private String assignUserSignImg;

}
