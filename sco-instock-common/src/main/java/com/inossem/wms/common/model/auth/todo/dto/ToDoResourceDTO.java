package com.inossem.wms.common.model.auth.todo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ToDoResourceDTO implements Serializable {

    private static final long serialVersionUID = 8578421885677471868L;
    @ApiModelProperty(value = "资源id" , example = "148309173927937")
    private Long resourcesId;

    @ApiModelProperty(value = "资源code" , example = "purchase_inbound")
    private String resourcesCode;

    @ApiModelProperty(value = "资源名称" , example = "采购入库")
    private String resourcesName;

    @ApiModelProperty(value = "资源父id"  , example = "148314154663937")
    private Long parentId;

    @ApiModelProperty(value = "显示顺序" , example = "1")
    private String displayIndex;

    @ApiModelProperty(value = "是否有效" , example = "1")
    private String enabled;

    @ApiModelProperty(value = "是否已选中过" , example = "1")
    private String isChecked;

    @ApiModelProperty(value = "资源id", example = "148309173927937")
    private Long id;
}
