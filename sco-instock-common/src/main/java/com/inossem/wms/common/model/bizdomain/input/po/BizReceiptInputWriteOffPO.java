package com.inossem.wms.common.model.bizdomain.input.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库冲销入参对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-26
 */
@Data
@ApiModel(value = "入库冲销入参对象", description = "入库冲销入参对象")
public class BizReceiptInputWriteOffPO implements Serializable {

    private static final long serialVersionUID = -2045884192296680283L;

    @ApiModelProperty(value = "入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long headId;

    @ApiModelProperty(value = "冲销行项目id集合" , example = "149901631619075")
    private List<Long> itemIds;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "冲销运单id集合" , example = "149901631619075")
    private List<Long> waybillIds;

}
