package com.inossem.wms.common.model.bizdomain.screen.dto;

import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizReceiptPalletSortingItemDTO;
import com.inossem.wms.common.model.label.entity.BizReceiptPalletSortingItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 大屏过门标签
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "查询对象po", description = "查询对象po")
public class ScreenLabelDTO implements Serializable {

    private static final long serialVersionUID = -1339757591761061746L;

    @ApiModelProperty(value = "过门标签", example = "D00000000000011354")
    private List<BizReceiptPalletSortingItemDTO> resultList;

    @ApiModelProperty(value = "过门标签", example = "D00000000000011354")
    private List<BizReceiptPalletSortingItem> errorResultList;

    @ApiModelProperty(value = "过门标签", example = "D00000000000011354")
    private List<BizLabelDataDTO> errorLabelList;

    @ApiModelProperty(value = "api调用执行状态, 调用成功返回1，失败返回0", example = "0")
    private Byte status;

    @ApiModelProperty(value = "生成请求的码盘单", example = "PS00000007")
    private List<String> successCodeList;
}
