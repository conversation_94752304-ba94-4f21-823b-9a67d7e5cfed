package com.inossem.wms.common.model.bizdomain.apply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 物料编码申请单抬头导出对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BizReceiptMatApplyItemExportVO对象", description = "物料编码申请单")
public class BizReceiptMatApplyItemExportVO implements Serializable {

    @ExcelProperty(value = "序号")
    private String rid;

    @ExcelProperty(value = "*物料编码\nMaterial code")
    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ExcelProperty(value = "物料描述中文\nMaterial Chinese name")
    @ApiModelProperty(value = "物料描述中文")
    private String matName;

    @ExcelProperty(value = "物料描述英文（简称）\nMaterial English description")
    @ApiModelProperty(value = "物料描述英文（简称）")
    private String matNameEnShort;

    @ExcelProperty(value = "计量单位")
    @ApiModelProperty(value = "单位编码", example = "7")
    private String unitCode;

    @ExcelProperty(value = "物料组")
    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ExcelProperty(value = "物料类型")
    @ApiModelProperty(value = "物料类型")
    private String matTypeCode;

    @ExcelProperty(value = "评估类")
    @ApiModelProperty(value = "评估类")
    private String extEvaluationClassification;

    @ExcelProperty(value = "行业领域")
    @ApiModelProperty(value = "行业领域")
    private String industryCode;

    @ExcelProperty(value = "价格控制")
    @ApiModelProperty(value = "价格控制")
    private String priceControlCode;

    @ExcelProperty(value = "价格单位")
    @ApiModelProperty(value = "价格单位")
    private String priceUnit;

    @ExcelProperty(value = "采购组")
    @ApiModelProperty(value = "采购组")
    private String purchaseGroupCode;

    @ExcelProperty(value = "利润中心")
    @ApiModelProperty(value = "利润中心")
    private String profitCenterCode;

}
