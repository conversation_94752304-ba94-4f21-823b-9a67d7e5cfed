package com.inossem.wms.common.model.bizdomain.register.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.cases.dto.BizCasesImgDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 登记单行项目明细传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="登记单行项目明细传输对象", description="登记单行项目明细传输对象")
public class BizReceiptRegisterItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "采购订单备注")
    private String receiptRemark;

    @ApiModelProperty(value = "接货时间" , example = "2021-05-10")
    private Date receiveDate;

    @ApiModelProperty(value = "需求部门")
    private String needDeptCode;

    @ApiModelProperty(value = "箱件数量")
    private Long caseCount;

    @ApiModelProperty(value = "扩展属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 接收库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "填充属性 - 物料英文名称" , example = "Material Description 001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 工具编码" , example = "100001")
    private String toolCode;

    @ApiModelProperty(value = "填充属性 - 出厂编码")
    private String outFtyCode;

    @ApiModelProperty(value = "标签类型	0：普通标签	1：RFID抗金属  	2：RFID非抗金属 签")
    private Integer tagType;

    @ApiModelProperty(value = "填充属性 - 存储类型编码")
    private String typeCode;

    @ApiModelProperty(value = "填充属性 - 仓位编码")
    private String binCode;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 - 前置单据编码")
    private String preReceiptCode;

    @ApiModelProperty(value = "填充属性 - 盘点类型：0-首盘，1-复盘")
    private Integer isReplay;

    @ApiModelProperty(value = "扩展属性 - 盘点类型描述")
    private String isReplayI18n;

    @ApiModelProperty(value = "扩展属性 - 到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "扩展属性 - 可遗失数量")
    private BigDecimal canLoseQty;

    @ApiModelProperty(value = "扩展属性 - 可损坏数量")
    private BigDecimal canDamageQty;

    @ApiModelProperty(value = "扩展属性 - 外观检查描述")
    private String visualCheckI18n;

    @ApiModelProperty(value = "填充属性 - 单据单号")
    private String receiptCode;

    @ApiModelProperty(value = "填充属性 - 单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "填充属性 - 采购订单编码" , example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 采购订单行号" , example = "0010")
    private String referReceiptRid;

    @ApiModelProperty(value = "填充属性 - 需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "填充属性 - 需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "填充属性 - 采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "填充属性 - 采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "填充属性 - 箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "填充属性 - 包装形式")
    private String packageType;

    @ApiModelProperty(value = "填充属性 - 包装形式国际化字符串")
    private String packageTypeI18n;

    @ApiModelProperty(value = "填充属性 - 包装形式")
    private String boxPackageType;
    
    @ApiModelProperty(value = "填充属性 - 包装形式国际化字符串")
    private String boxPackageTypeI18n;

    @ApiModelProperty(value = "填充属性 - 箱件尺寸")
    private String caseSize;

    @ApiModelProperty(value = "填充属性 - 毛重")
    private String caseWeight;

    @ApiModelProperty(value = "单据备注")
    private String headRemark;

    @ApiModelProperty(value = "填充属性 - 特殊库存代码" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "填充属性 - 供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 供应商名称" , example = "邯郸市邯山荷华商贸有限公司")
    private String supplierName;

    @ApiModelProperty(value = "客户编号" , example = "1")
    private String customerCode;

    @ApiModelProperty(value = "客户名称" , example = "1")
    private String customerName;

    @SonAttr(sonTbName = "biz_cases_img", sonTbFkAttrName = "receiptItemId")
    @ApiModelProperty(value = "箱件图片")
    private List<BizCasesImgDTO> casesImgList;

    @ApiModelProperty(value = "前序单据最后修改时间")
    private Date preReceiptModifyTime;

    @ApiModelProperty(value = "与sap过账的qty")
    private BigDecimal billQty;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    @RlatAttr(rlatTableName = "biz_receipt_register_head", sourceAttrName = "receiptCode,receiptType,remark,receiveDate",
            targetAttrName = "receiptCode,receiptType,headRemark,receiveDate")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_APPLY_HEAD, Const.PRE_RECEIPT_TYPE_STOCKTAKING_HEAD, Const.PRE_RECEIPT_TYPE_DELIVERY_HEAD},
            sourceAttrName = "receiptCode,createUserId,isReplay,deliveryNoticeDescribe,customsSealsNumber,billLadingNumber,estimatedArrivalTime", targetAttrName = "preReceiptCode,borrowUserId,isReplay,deliveryNoticeDescribe,customsSealsNumber,billLadingNumber,estimatedArrivalTime")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_APPLY_ITEM, Const.PRE_RECEIPT_TYPE_STOCKTAKING_ITEM,
            Const.PRE_RECEIPT_TYPE_DELIVERY_ITEM}, sourceAttrName = "modifyUserId,caseCode,packageType,caseSize,caseWeight",
            targetAttrName = "stocktakingUserId,caseCode,packageType,caseSize,caseWeight")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    @RlatAttr(rlatTableName = "erp_purchase_receipt_head", sourceAttrName = "receiptCode,remark,needDeptCode", targetAttrName = "referReceiptCode,receiptRemark,needDeptCode")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    @RlatAttr(rlatTableName = "erp_purchase_receipt_item",
            sourceAttrName = "rid,applyUserCode,applyUserName,purchaseUserCode,purchaseUserName,specStockCode,supplierCode,supplierName,customerCode,customerName,applyUserDeptName",
            targetAttrName = "referReceiptRid,applyUserCode,applyUserName,purchaseUserCode,purchaseUserName,specStockCode,supplierCode,supplierName,customerCode,customerName,applyUserDeptName")
    private Long referReceiptItemId;

    private String applyUserDeptName;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName,corpId", targetAttrName = "ftyCode,ftyName,corpId")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "batchCode,toolTypeId,outFtyCode,tagType", targetAttrName = "toolCode,toolTypeId,outFtyCode,tagType")
    private Long batchId;

    @ApiModelProperty(value = "存储类型ID")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode", targetAttrName = "typeCode")
    private Long typeId;

    @ApiModelProperty(value = "仓位ID")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    private Long binId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "冲销数量")
    private BigDecimal writeOffQty;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015")
    private String matDocYear;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true" , example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "冲销物料凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销标志0-false, 1-true" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "外观检查")
    private Integer visualCheck;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    /* ********************** 顺序扩展字段开始 ****************************/

    @ApiModelProperty(value = "填充属性 - 工具类型id")
    @RlatAttr(rlatTableName = "dic_tool_type", sourceAttrName = "toolTypeName", targetAttrName = "toolTypeName")
    private Long toolTypeId;

    @ApiModelProperty(value = "填充属性 - 工具类型描述")
    private String toolTypeName;

    @ApiModelProperty(value = "填充属性 - 借用人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "borrowUserCode,borrowUserName")
    private Long borrowUserId;

    @ApiModelProperty(value = "填充属性 - 借用人编码")
    private String borrowUserCode;

    @ApiModelProperty(value = "填充属性 - 借用人名称")
    private String borrowUserName;

    @ApiModelProperty(value = "填充属性 - 盘点人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "stocktakingUserCode,stocktakingUserName")
    private Long stocktakingUserId;

    @ApiModelProperty(value = "填充属性 - 盘点人编码")
    private String stocktakingUserCode;

    @ApiModelProperty(value = "填充属性 - 盘点人名称")
    private String stocktakingUserName;

    @RlatAttr(rlatTableName = "dic_corp", sourceAttrName = "corpCode,corpName", targetAttrName = "corpCode,corpName")
    @ApiModelProperty(value = "公司编码", example = "1000", required = false)
    private Long corpId;

    @ApiModelProperty(value = "公司编码" , example = "1000", required = false)
    private String corpCode;

    @ApiModelProperty(value = "公司描述" , example = "示例公司", required = false)
    private String corpName;

    /* ********************** 顺序扩展字段结束 ****************************/

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "工器具 - 赔偿建议")
    private String compensationProposal;

    @ApiModelProperty(value = "海关关封号")
    private String customsSealsNumber;

    @ApiModelProperty(value = "提单号")
    private String billLadingNumber;

    @ApiModelProperty(value = "预计到货时间")
    private Date estimatedArrivalTime;

    @ApiModelProperty(value = "需求计划id")
    private Long demandPlanId;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "可登记数量")
    private BigDecimal canRegisterQty;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;       

    //新增5个字段：车辆编号、司机姓名、联系方式、发票号、发票日期

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;  

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;


}
