package com.inossem.wms.common.model.bizdomain.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 配货特征表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptSpecApplyTransfer", description = "领料合并转性表")
@TableName("biz_receipt_apply_transfer")
public class BizReceiptApplyTransfer implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键" , example = "152218403667969")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据类型" , example = "211", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据head表id" , example = "155163729920001")
    private Long receiptHeadId;

    @ApiModelProperty(value = "单据item表id" , example = "152412159541249")
    private Long receiptItemId;

    @ApiModelProperty(value = "前置单据类型" , example = "211", required = false )
    private Integer preReceiptType;

    @ApiModelProperty(value = "前置单据head表id" , example = "155163729920001")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前置单据item表id" , example = "155163729920001")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前置单据bin表id" , example = "155163729920001")
    private Long preReceiptBinId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "成套库存地点id" , example = "145725436526593")
    private Long mainLocationId;

    @ApiModelProperty(value = "库存数量" , example = "10")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "特性code" , example = "biz_batch_info.batch_erp,biz_batch_info.spec_stock_code")
    private String specCode;

    @ApiModelProperty(value = "特性值" , example = "111111,1")
    private String specValue;

    @ApiModelProperty(value = "显示特性值" , example = "Q,wbsCode1,0001002957,LK_D,,LK_01")
    private String specDisplayValue;

    @ApiModelProperty(value = "特性类型" , example = "0")
    private Integer specType;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "接收批次id" , example = "100001")
    private Long inputBatchId;

    @ApiModelProperty(value = "接收仓库存储类型id" , example = "152218403667969")
    private Long inputTypeId;

    @ApiModelProperty(value = "接收仓位id" , example = "152218489651201")
    private Long inputBinId;

    @ApiModelProperty(value = "接收存储单元id" , example = "152758218981377")
    private Long inputCellId;

    @ApiModelProperty(value = "成套批次id")
    private Long unitizedBatchId;

    // 前端必须要的字段
    private String parentId;
    private String uid;

    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;
    @ApiModelProperty(value = "预留单行项目号")
    private String reservedOrderRid;
    @ApiModelProperty(value = "行项目序号")
    private String rid;
    @ApiModelProperty(value = "凭证时间")
    private Date docDate;
    @ApiModelProperty(value = "过账时间")
    private Date postingDate;
    @ApiModelProperty(value = "sap转性标识0-false, 1-true")
    private Integer isTransfer;
    @ApiModelProperty(value = "sap预留标识0-false, 1-true")
    private Integer isReserved;

    private int transferFlag;
    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;
    @ApiModelProperty(value = "物料凭证行号")
    private String matDocRid;
    @ApiModelProperty(value = "物料凭证年份")
    private String matDocYear;

    @ApiModelProperty(value = "接收特殊库存代码")
    private String inputSpecStockCode;
    @ApiModelProperty(value = "接收仓库号id")
    private Long inputWhId;
    @ApiModelProperty(value = "接收物料单位id")
    private Long inputUnitId;
    @ApiModelProperty(value = "接收库存地点id")
    private Long inputLocationId;
    @ApiModelProperty(value = "接收工厂id")
    private Long inputFtyId;
    @ApiModelProperty(value = "接收物料id")
    private Long inputMatId;
    @ApiModelProperty(value = "发出特殊库存代码" , example = "")
    private String outputSpecStockCode;
    @ApiModelProperty(value = "发出仓库号id")
    private Long outputWhId;
    @ApiModelProperty(value = "发出物料单位id")
    private Long outputUnitId;
    @ApiModelProperty(value = "发出库存地点id")
    private Long outputLocationId;
    @ApiModelProperty(value = "发出工厂id")
    private Long outputFtyId;
    @ApiModelProperty(value = "发出物料id")
    private Long outputMatId;
    @ApiModelProperty(value = "单价")
    private BigDecimal price;
    @ApiModelProperty(value = "总价")
    private BigDecimal amount;
    @ApiModelProperty(value = "总数量" , example = "5")
    private BigDecimal qtyTotal;
    @ApiModelProperty(value = "WBS编码" , example = "wbsCode2")
    private String whCodeOut;
    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;
    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;
    private String batchCode;
    private Long batchId;
}
