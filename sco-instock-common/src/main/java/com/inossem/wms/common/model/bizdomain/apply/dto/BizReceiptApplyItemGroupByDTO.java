package com.inossem.wms.common.model.bizdomain.apply.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/29 14:40
 * @desc BizReceiptApplyItemGroupByDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "借用申请单行项目聚合对象", description = "借用申请单行项目聚合对象")
public class BizReceiptApplyItemGroupByDTO implements Serializable {
    private static final long serialVersionUID = 4192503083596295623L;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "单位")
    private String unitName;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "实发数量")
    private BigDecimal actualQty;
}
