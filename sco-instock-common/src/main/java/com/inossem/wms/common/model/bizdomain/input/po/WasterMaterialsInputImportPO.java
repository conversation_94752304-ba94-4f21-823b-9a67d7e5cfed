package com.inossem.wms.common.model.bizdomain.input.po;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> wang
 * <p>
 *     废旧物资数据传输对象
 * </p>
 * @date 2022/4/25 9:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "废旧物资数据传输对象", description = "废旧物资数据传输对象")
public class WasterMaterialsInputImportPO implements Serializable {


    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @ExcelProperty(value = "物料编码", index =0)
    private String matCode;

//    @ApiModelProperty(value = "单位编码" , example = "M3")
//    @ExcelProperty(value = "单位编码", index =1)
//    private String unitCode;

    @ApiModelProperty(value = "工厂编码" , example = "8000")
    @ExcelProperty(value = "库存地点编码", index =1)
    private String ftyCode;

    @ApiModelProperty(value = "库存地点编码" , example = "2500")
    @ExcelProperty(value = "库存地点编码", index =2)
    private String locationCode;

    @ApiModelProperty(value = "入库数量" , example = "5")
    @ExcelProperty(value = "入库数量", index =3)
    private BigDecimal qty;

    @ApiModelProperty(value = "wbs编码" , example = "111")
    @ExcelProperty(value = "wbs编码", index =4)
    private String specStockCode;

    @ApiModelProperty(value = "废旧物资-原价" , example = "20.1")
    @ExcelProperty(value = "原价", index =5)
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "废旧物资-折旧价" , example = "1.1")
    @ExcelProperty(value = "折旧价", index =6)
    private BigDecimal depreciationPrice;

    @ApiModelProperty(value = "废旧物资-处置评估价" , example = "3.1")
    @ExcelProperty(value = "处置评估价", index =7)
    private BigDecimal appraisalPrice;

    @ApiModelProperty(value = "废旧物资-是否危险【1是，0否】")
    @ExcelProperty(value = "是否危险", index =8)
    private Integer isDanger;

    @ApiModelProperty(value = "wbs名称" , example = "111")
    private String wbsName;

}
