package com.inossem.wms.common.model.stock.dto;

import java.io.Serializable;
import java.util.List;

import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 库存凭证传输对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存凭证传输对象", description = "库存凭证传输对象")
public class StockInsMoveTypeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批次库存凭证传输对象")
    private List<StockInsDocBatch> insDocBatchList;

    @ApiModelProperty(value = "仓位库存凭证传输对象")
    private List<StockInsDocBin> insDocBinList;

    @ApiModelProperty(value = "标签相关参数")
    private List<StockInsDocBinPo> insDocBinPoList;

}
