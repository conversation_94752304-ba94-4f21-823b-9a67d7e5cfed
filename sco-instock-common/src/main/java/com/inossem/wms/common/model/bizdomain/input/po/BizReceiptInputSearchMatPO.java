package com.inossem.wms.common.model.bizdomain.input.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 工具归还单查询物料库存入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="工具归还单查询物料库存入参", description="工具归还单查询物料库存入参")
public class BizReceiptInputSearchMatPO implements Serializable {

    private static final long serialVersionUID = 8822125030297256947L;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "工具编码")
    private String toolCode;

}
