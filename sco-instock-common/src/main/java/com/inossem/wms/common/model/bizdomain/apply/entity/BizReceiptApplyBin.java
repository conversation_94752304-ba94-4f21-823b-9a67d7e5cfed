package com.inossem.wms.common.model.bizdomain.apply.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 申请单行项目明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptApplyBin对象", description="申请单行项目明细表")
@TableName("biz_receipt_apply_bin")
public class BizReceiptApplyBin implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "item表id")
    private Long itemId;

    @ApiModelProperty(value = "配货行序号")
    private String bid;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "批次库存id")
    private Long stockBatchId;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "冲减数量")
    private BigDecimal subtractQty;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "可预留量")
    private BigDecimal reserveQty;

    @ApiModelProperty(value = "已预留量")
    private BigDecimal alreadyReserveQty;

    @ApiModelProperty(value = "可冲减数量")
    private BigDecimal canSubtractQty;

    @ApiModelProperty(value = "锁定冲减数量")
    private BigDecimal lockSubtractQty;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;
    @ApiModelProperty(value = "预留单行项目号")
    private String reservedOrderRid;

    @ApiModelProperty(value = "本次申请数量")
    private BigDecimal preQty;

    @ApiModelProperty(value = "已拆分数量")
    private BigDecimal alreadySplitQty;

    @ApiModelProperty(value = "拆分前续单据bin表id")
    private Long preApplyBinId;

    @ApiModelProperty(value = "拆分前续单据head表id")
    private Long preApplyHeadId;

    @ApiModelProperty(value = "WBS编号")
    private String specStockCode;

    @ApiModelProperty(value = "主设备编码")
    private String parentMatCode;

    @ApiModelProperty(value = "物资编码")
    private String extend20;

    @ApiModelProperty(value = "物料类型")
    private String extend28;

    @ApiModelProperty(value = "功能位置码")
    private String functionalLocationCode;

    @ApiModelProperty(value = "NCR编号")
    private String ncrbh;

}
