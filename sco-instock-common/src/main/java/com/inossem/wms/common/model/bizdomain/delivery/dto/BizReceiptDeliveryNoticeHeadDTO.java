package com.inossem.wms.common.model.bizdomain.delivery.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.common.enums.EnumContractTaxRateMapVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 送货通知抬头DTO
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="送货通知抬头DTO", description="送货通知抬头DTO")
public class BizReceiptDeliveryNoticeHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 *************************/

    @ApiModelProperty(value = "扩展属性 送货车型-打印使用")
    private String deliveryCarTypeStr;

    @ApiModelProperty(value = "扩展属性 是否有完工报告-打印使用")
    private String isReportStr;

    @ApiModelProperty(value = "扩展属性 是否有包装，运输，装修，贮存要求-打印使用")
    private String isSpecialRequStr;

    @ApiModelProperty(value = "扩展属性 是否放危化品-打印使用")
    private String isDangerStr;

    @ApiModelProperty(value = "扩展属性 是否放射性-打印使用")
    private String isRadioactivityStr;

    @ApiModelProperty(value = "扩展属性 是否进口核安全设备-打印使用")
    private String isSafeStr;

    @ApiModelProperty(value = "扩展属性 是否有运输专用装置和包装材料返回供应商要求-打印使用")
    private String isTranSpecialStr;

    @ApiModelProperty(value = "扩展属性 打印使用 - 采购订单编码" , example = "4500000001")
    private String referReceiptCode;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称" , example = "管理员")
    private String modifyUserName;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @SonAttr(sonTbName = "biz_common_receipt_attachment", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据附件")
    private List<BizCommonReceiptAttachment> fileList;

    @SonAttr(sonTbName = "biz_receipt_delivery_notice_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 送货通知单行项目")
    private List<BizReceiptDeliveryNoticeItemDTO> itemList;

    @SonAttr(sonTbName = "biz_receipt_delivery_notice_d2d_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 送货通知单门到门行项目")
    private List<BizReceiptDeliveryNoticeD2dItemDTO> d2dItemList;

    @SonAttr(sonTbName = "biz_receipt_delivery_notice_case_rel", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 箱信息")
    private List<BizReceiptDeliveryNoticeCaseRelDTO> caseNowList;

    @SonAttr(sonTbName = "biz_receipt_waybill", sonTbFkAttrName = "deliveryNoticeHeadId")
    @ApiModelProperty(value = "填充属性 - 送货通知单运单")
    private List<BizReceiptWaybillDTO> waybillDTOList;

    @ApiModelProperty(value = "扩展属性 - 箱件清单")
    private List<BizReceiptDeliveryNoticeItemDTO> caseList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;

    @ApiModelProperty(value = "扩展属性 - 审批流")
    private List<BizApproveRecordDTO> approveList;

    @ApiModelProperty(value = "扩展属性 - 流程实例ID")
    private Long proInstanceId;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称" , example = "入库单")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "采购员编号" , example = "Admin")
    private String purchaseUserCode;

    @ApiModelProperty(value = "采购员名称" , example = "管理员")
    private String purchaseUserName;

    @ApiModelProperty(value = "填充属性 - 采购负责人编号" , example = "Admin")
    private String purchaseManagerCode;

    @ApiModelProperty(value = "填充属性 - 采购负责人名称" , example = "管理员")
    private String purchaseManagerName;

    @ApiModelProperty(value = "填充属性 - 供应商名称" , example = "英诺森")
    private String supplierName;

    @ApiModelProperty(value = "填充属性 - 供应商编码" , example = "60000001")
    private String supplierCode;

    @ApiModelProperty(value = "填充属性 - 内贸供应商名称" , example = "英诺森")
    private String inlandSupplierName;

    @ApiModelProperty(value = "填充属性 - 内贸供应商编码" , example = "60000001")
    private String inlandSupplierCode;

    @ApiModelProperty(value = "工厂编码", example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "工厂名称", example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "需求人编码", example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "需求人描述", example = "管理员")
    private String applyUserName;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "assignUserCode,assignUserName")
    @ApiModelProperty(value = "物项合同科审核人员", example = "管理员")
    private Long assignUserId;

    @ApiModelProperty(value = "物项合同科审核人员", example = "管理员")
    private String assignUserName;

    @ApiModelProperty(value = "物项合同科审核人员", example = "管理员")
    private String assignUserCode;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "送货通知单号" , example = "SH01000006")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型  送货通知：220" , example = "220")
    private Integer receiptType;

    @ApiModelProperty(value = "草稿：10  送货中：120  已到货：121" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "计划到货日期" , example = "2021-05-15")
    private Date planArrivalDate;

    @ApiModelProperty(value = "实际到货时间" , example = "2021-05-15")
    private Date realDeliveryTime;

    @ApiModelProperty(value = "单据备注" , example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "预计到货时间")
    private Date estimatedArrivalTime;

    @ApiModelProperty(value = "送货车型")
    private String deliveryCarType;

    @ApiModelProperty(value = "是否直抵现场")
    private Integer isDirectScene;

    @ApiModelProperty(value = "是否有运输专用装置和包装材料返回供应商要求")
    private Integer isTranSpecial;

    @ApiModelProperty(value = "是否进口核安全设备")
    private Integer isSafe;

    @ApiModelProperty(value = "是否放射性")
    private Integer isRadioactivity;

    @ApiModelProperty(value = "是否放危化品")
    private Integer isDanger;

    @ApiModelProperty(value = "是否有包装，运输，装修，贮存要求")
    private Integer isSpecialRequ;

    @ApiModelProperty(value = "是否有完工报告")
    private Integer isReport;

    @ApiModelProperty(value = "到货通知描述")
    private String deliveryNoticeDescribe;

    @ApiModelProperty(value = "创建部门")
    private String createUserDeptName;

    @ApiModelProperty(value = "海关关封号")
    private String customsSealsNumber;

    @ApiModelProperty(value = "提单号")
    private String billLadingNumber;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "采购方式 1:联合采购;2:自主采购")
    private Integer procurementMethod;

    @ApiModelProperty(value = "采购方式 1:联合采购;2:自主采购")
    private String procurementMethodI18n;

    @ApiModelProperty(value = "预计发货时间")
    private Date estimatedSendTime;

    /*@RlatAttr(rlatTableName = "dic_purchase_package",
            sourceAttrName = "purchasePackageCode,purchasePackageName,contractCode,contractName,purchasePersonName",
            targetAttrName = "purchasePackageCode,purchasePackageName,contractCode,contractName,purchasePersonName")
    @ApiModelProperty(value = "采购包id")*/
    private Long purchasePackageId;

    @ApiModelProperty(value = "采购包号")
    private String purchasePackageCode;

    @ApiModelProperty(value = "采购包名称")
    private String purchasePackageName;

    @ApiModelProperty(value = "采购负责人")
    private String purchasePersonName;

    @ApiModelProperty(value = "是否返运物资(1是0否)")
    private Integer isMaterialReturn;

    @ApiModelProperty(value = "物资返运单号")
    private String materialReturnReceiptCode;

    @ApiModelProperty(value = "是否有加速度仪(1是0否)")
    private Integer isSpeedometer;

    @ApiModelProperty(value = "是否虚拟出入库物项(1是0否)")
    private Integer isVirtual;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "是否复检(1是0否)")
    private Integer isRecheck;

    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同员")
    private String contractUser;


    /*********************华信新增**************/

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方")
    private String firstPartyI18n;

    @ApiModelProperty(value = "支付方式")
    private Integer paymentMethod;      

    @ApiModelProperty(value = "支付方式")
    private String paymentMethodI18n;

    

    @ApiModelProperty(value = "合同id")
    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,contractName,deliveryAddress,createUserName,supplierId,firstParty,paymentMethod,currency,purchaseType",
            targetAttrName = "contractCode,contractName,deliveryAddress,purchaserName,supplierId,firstParty,paymentMethod,currency,purchaseType")
    private Long contractId;


    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商")
    private Long supplierId;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "inlandSupplierCode,inlandSupplierName")
    @ApiModelProperty(value = "内贸供应商id")
    private Long inlandSupplierId;

    @ApiModelProperty(value = "批次号")
    private String batchCode;

    @ApiModelProperty(value = "具备发货日期")
    private Date canDeliveryDate;

    @ApiModelProperty(value = "预计到货日期")
    private Date expectArrivalDate;

    @ApiModelProperty(value = "运输方式  1 空运，2 船运")
    private String transportType;

    @ApiModelProperty(value = "班车、船次")
    private String transportBatch;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "供方交货/服务地点")
    private Integer deliveryAddress;

    @ApiModelProperty(value = "供方交货/服务地点")
    private String deliveryAddressI18n;

    @ApiModelProperty(value = "供货方式")
    private String deliveryType;

    @ApiModelProperty(value = "采购员")
    private String purchaserName;

    @ApiModelProperty(value = "送货类型 1 离岸采购 2 在岸采购 3 油品采购 4 内贸采购")
    private Integer sendType;

    private String sendTypeI18n;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseCode;

    @ApiModelProperty(value = "税码枚举列表")
    private List<EnumContractTaxRateMapVO> taxRateList;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "币种")
    private String currencyI18n;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "倍率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "合同税码")
    private Integer contractTaxCode;

    @ApiModelProperty(value = "合同税码国际化")
    private String contractTaxCodeI18n;

    @ApiModelProperty(value = "司机信息")
    private String driverInfo;

    @ApiModelProperty(value = "车辆信息")
    private String carInfo;

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "po不含税总价")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "是否门到门送货")
    private Integer isD2dDelivery;

    @ApiModelProperty(value = "可送货数量", example = "10")
    private BigDecimal canDeliveryQty;
}
