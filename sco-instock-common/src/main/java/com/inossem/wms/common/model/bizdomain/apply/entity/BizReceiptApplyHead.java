package com.inossem.wms.common.model.bizdomain.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 借用申请单抬头表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptApplyHead对象", description="借用申请单抬头表")
@TableName("biz_receipt_apply_head")
public class BizReceiptApplyHead implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【借用申请：9050；维修申请：9051；报废申请：9052】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "借用方式【1：长期借用单；2：短期借用单】")
    private Integer borrowType;

    @ApiModelProperty(value = "申请部门id")
    private Long deptId;

    @ApiModelProperty(value = "申请科室id")
    private Long deptOfficeId;

    @ApiModelProperty(value = "预计借用天数（短期借用单必填）")
    private Integer estimateBorrowDay;

    @ApiModelProperty(value = "维修厂商")
    private String repairFty;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "移动类型id")
    private Long moveTypeId;

    @ApiModelProperty(value = "领料出库单信息id")
    private Long outInfoId;

    @ApiModelProperty(value = "是否为内部领料【1是，0否】" , example = "0", required = false)
    private Integer isInnerFlag;

    @ApiModelProperty(value = "是否劳保物资【1是，0否】" , example = "0", required = false)
    private Integer isLaborFlag;

    @ApiModelProperty(value = "对口部门Id" , example = "0", required = false)
    private Long counterpartDeptId;

    @ApiModelProperty(value = "对口科室Id" , example = "0", required = false)
    private Long counterpartOfficeId;

    @ApiModelProperty(value = "暂存人id")
    private Long receiverId;

    @ApiModelProperty(value = "需求部门")
    private String applyDeptName;

    private String des;
    private Integer transferFlag;

    @ApiModelProperty(value = "填充属性 - 存放原因")
    private String tempStoreReason;

    @ApiModelProperty(value = "填充属性 - 暂存类型 首次暂存0 延期暂存1")
    private Integer tempStoreType;

    @ApiModelProperty(value = "填充属性 - 暂存物项暂申请备注")
    private String tempRemark;

    @ApiModelProperty(value = "提交人id")
    private Long submitUserId;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "领用类型 1 手工领用 2需求计划领用" )
    private Integer receiveType;

    @ApiModelProperty(value = "工单号" )
    private String workOrder;

    @ApiModelProperty(value = "机组")
    private Integer unit;

    @ApiModelProperty(value = "本批次已受检货物【1：全部；2：部分；】 ")
    private Integer isInspect;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "质检会签单签发日期")
    private Date signInspectionDate;

    @ApiModelProperty(value = "到货日期" , example = "2021-05-15")
    private Date arrivalDate;

    @ApiModelProperty(value = "LOT包号")
    private String lotCode;

    @ApiModelProperty(value = "设备供应方")
    private String inspectCompany;

    @ApiModelProperty(value = "质检会签单编码")
    private String signInspectionCode;

    @ApiModelProperty(value = "是否反运（0:否；1:是）")
    private Integer backTransport;

    @ApiModelProperty(value = "是否精准预留（0:否；1:是）")
    private Integer isExact;

    @ApiModelProperty(value = "是否拆单（0:否；1:是）")
    private Integer isSplit;

    @ApiModelProperty(value = "创建类型【1:基于出库单入库2:基于物料编码入库】")
    private Integer createType;

    @ApiModelProperty(value = "拆分人id")
    private Long splitUserId;

    @ApiModelProperty(value = "是否本部门领用（0:否；1:是）")
    private Integer isThisDept;

    @ApiModelProperty(value = "领料单位")
    private String usedDeptName;

    @ApiModelProperty(value = "预计领用时间")
    private Date usedTime;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @ApiModelProperty(value = "专业工程师id")
    private Long professionalEngineerUserId;

    @ApiModelProperty(value = "虚拟出入库申请单单号")
    private String virtualOutputApplyReceiptCode;

    @ApiModelProperty(value = "领用依据")
    private Integer receiveBasis;

    @ApiModelProperty(value = "领用依据关联的紧急抢修单编号或者应急抢修审批表编号")
    private String receiveBasisReceiptCode;

    @ApiModelProperty(value = "紧急领用指派的审批人id(部门负责人或者生产值班负责人)")
    private Long assignUserId;

    @ApiModelProperty(value = "是否虚拟出入库物项(1是0否)")
    private Integer isVirtual;

    @ApiModelProperty(value = "紧急领用出库申请单单号")
    private String emergencyMatOrderApplyReceiptCode;

    @ApiModelProperty(value = "是否紧急领用(1是0否)")
    private Integer isEmergency;

    @ApiModelProperty(value = "创建人部门id")
    private Long createDeptId;

    @ApiModelProperty(value = "创建人科室id" , example = "1", required = false)
    private Long createOfficeId;


    @ApiModelProperty(value = "成本中心id" , example = "1", required = false)
    private Long costCenterId;

    @ApiModelProperty(value = "成本中心编码" , example = "1", required = false)
    private String costCenterCode;

    @ApiModelProperty(value = "wbs编码")
    private String wbsCode;

    @ApiModelProperty(value = "是否计划内零用（1:是；0:否）")
    private Integer innerPlan;

    @ApiModelProperty(value = "实际领料人姓名")
    private String actualReceiverName;

    @ApiModelProperty(value = "资产id")
    private Long assetId;

}
