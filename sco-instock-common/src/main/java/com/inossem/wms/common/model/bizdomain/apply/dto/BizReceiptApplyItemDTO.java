package com.inossem.wms.common.model.bizdomain.apply.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 借用申请单行项目明细传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="借用申请单行项目明细传输对象", description="借用申请单行项目明细传输对象")
public class BizReceiptApplyItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /* ********************** 扩展字段开始 ****************************/

    @ApiModelProperty(value = "特征列表")
    @SonAttr(sonTbName = "biz_receipt_apply_transfer", sonTbFkAttrName = "receiptItemId")
    private List<BizReceiptApplyTransferDTO> transferDTOList;


    @ApiModelProperty(value = "仓位库存信息")
    private List<StockBinDTO> stockBinDTOList;

    @ApiModelProperty(value = "扩展属性 - 标签与单据关联数据")
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptItemId")
    private List<BizLabelReceiptRelDTO> labelReceiptRelDTOList;

    @ApiModelProperty(value = "扩展属性-打印使用是否返回【1是，0否】" , example = "0", required = false)
    private String isReturnFlagStr;

    @ApiModelProperty(value = "扩展属性-是否返回【1是，0否】" , example = "0", required = false)
    private String isReturnFlagI8n;

    @ApiModelProperty(value = "WBS编号" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "WBS编码" , example = "wbsCode2")
    private String whCodeOut;

    @ApiModelProperty(value = "WBS描述" , example = "wbsName2")
    private String specStockName;

    @ApiModelProperty(value = "特征列表")
    @SonAttr(sonTbName = "biz_receipt_assemble", sonTbFkAttrName = "receiptItemId")
    private List<BizReceiptAssembleDTO> assembleDTOList;

    @ApiModelProperty(value = "扩展属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "填充属性 - 工厂编码" , example = "8000")
    private String ftyCode;

    @ApiModelProperty(value = "填充属性 - 工厂名称" , example = "英诺森沈阳工厂")
    private String ftyName;

    @ApiModelProperty(value = "填充属性 - 接收库存地点" , example = "2500")
    private String locationCode;

    @ApiModelProperty(value = "填充属性 - 接收库存地点name" , example = "英诺森001")
    private String locationName;

    @ApiModelProperty(value = "成套库存地点编码" , example = "2500")
    private String mainLocationCode;

    @ApiModelProperty(value = "成套库存地点描述" , example = "英诺森001")
    private String mainLocationName;

    @ApiModelProperty(value = "填充属性 - 仓库编码" , example = "S200")
    private String whCode;

    @ApiModelProperty(value = "填充属性 - 仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "填充属性 - 物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "填充属性 - 物料名称" , example = "物料描述001003")
    private String matName;

    @ApiModelProperty(value = "品名（英文）" , example = "物料描述001003")
    private String matNameEn;

    @ApiModelProperty(value = "填充属性 - 工具编码" , example = "100001")
    private String toolCode;

    @ApiModelProperty(value = "填充属性 - 批次编码" , example = "100001")
    private String batchCode;

    @ApiModelProperty(value = "填充属性 - 工具状态")
    private Integer toolStatus;

    @ApiModelProperty(value = "扩展属性 - 工具状态描述")
    private String toolStatusI18n;

    @ApiModelProperty(value = "批次信息")
    private BizBatchInfoDTO batchInfoDto;

    @ApiModelProperty(value = "填充属性 - 出厂编码")
    private String outFtyCode;

    @ApiModelProperty(value = "填充属性 - 存储类型编码")
    private String typeCode;

    @ApiModelProperty(value = "填充属性 - 仓位编码")
    private String binCode;

    @ApiModelProperty(value = "填充属性 - 计量单位编码" , example = "M3")
    private String unitCode;

    @ApiModelProperty(value = "填充属性 - 计量单位名称" , example = "立方米")
    private String unitName;

    @ApiModelProperty(value = "填充属性 - 小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "填充属性 - 创建人编码" , example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 - 创建人名称" , example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 - 修改人编码" , example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 - 修改人名称" , example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "扩展属性 - 特征code")
    private String specCode;

    @ApiModelProperty(value = "扩展属性 - 特征值")
    private String specValue;

    @ApiModelProperty(value = "扩展属性 - 单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "扩展属性 - 单据编号")
    private String receiptCode;

    @ApiModelProperty(value = "扩展属性 - 批次库存id")
    private Long stockBatchId;

    @ApiModelProperty(value = "填充属性 - 前置单据编码")
    private String preReceiptCode;

    @ApiModelProperty(value = "填充属性 - 前置单据行号")
    private String preReceiptRid;

    @ApiModelProperty(value = "填充属性 - 可出库数量")
    private BigDecimal canOutputQty;

    @ApiModelProperty(value = "扩展属性 - 可退库数量" , example = "100")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private Integer isNuclearIslandToolRoom;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private String isNuclearIslandToolRoomI18n;

    @ApiModelProperty(value = "前序单据最后修改时间")
    private Date preReceiptModifyTime;

    @ApiModelProperty(value = "功能位置码")
    private String funcNo;

    @ApiModelProperty(value = "物资ID")
    private String matnr;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "卷标")
    private String fileMark;

    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @ApiModelProperty(value = "机组")
    private String term;

    @ApiModelProperty(value = "专业")
    private String major;

    @ApiModelProperty(value = "系统")
    private String paperSystem;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "厂房")
    private String factoryBuilding;

    @ApiModelProperty(value = "工程文件编码")
    private String fileCode;

    @ApiModelProperty(value = "物资描述")
    private String descText;

    @ApiModelProperty(value = "物料类别")
    private String materType;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "需求数量(图纸输机净量×消耗系数)")
    private BigDecimal preQty;

    @ApiModelProperty(value = "创建人名称")
    private String preCreateUserName;

    @ApiModelProperty(value = "需求计划创建人id")
    private Long preCreateUserId;

    /* ********************** 扩展字段结束 ****************************/

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_OUTPUT_HEAD, Const.PRE_RECEIPT_TYPE_PURCHASE_HEAD, Const.PRE_RECEIPT_TYPE_REQUIRE_HEAD},
            sourceAttrName = "createUserId,receiptCode",
            targetAttrName = "preCreateUserId,preReceiptCode")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    @RlatAttr(rlatTableName = {Const.PRE_RECEIPT_TYPE_OUTPUT_ITEM, Const.PRE_RECEIPT_TYPE_PURCHASE_ITEM, Const.PRE_RECEIPT_TYPE_REQUIRE_ITEM},
            sourceAttrName = "rid,canOutputQty,preReceiptHeadId",
            targetAttrName = "preReceiptRid,canOutputQty,matApplyHeadId")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    @RlatAttr(rlatTableName = "biz_receipt_paper_head",
            sourceAttrName = "fileCode,version,fileMark,island,term,major",
            targetAttrName = "fileCode,version,fileMark,island,term,major")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    @RlatAttr(rlatTableName = {Const.REFER_RECEIPT_TYPE_PAPER_ITEM, Const.REFER_RECEIPT_TYPE_REQUIRE_ITEM},
            sourceAttrName = "specification,materType,descText,funcNo,matnr,paperSystem,area,factoryBuilding,qty",
            targetAttrName = "specification,materType,descText,funcNo,matnr,paperSystem,area,factoryBuilding,preQty")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "mainLocationCode,mainLocationName")
    @ApiModelProperty(value = "成套库存地点id" , example = "145725436526593")
    private Long mainLocationId;

    @ApiModelProperty(value = "仓库id")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn,parentMatId,matGroupId,extManufacturerPartNumber,extMainMaterial,extIndustryStandardDesc", targetAttrName = "matCode,matName,matNameEn,parentMatId,matGroupId,extManufacturerPartNumber,extMainMaterial,extIndustryStandardDesc")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "batchCode,batchCode,toolStatus,tooTypeId,outFtyCode,*", targetAttrName = "batchCode,toolCode,toolStatus,tooTypeId,outFtyCode,batchInfoDto")
    private Long batchId;

    @ApiModelProperty(value = "存储类型ID")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode", targetAttrName = "typeCode")
    private Long typeId;

    @ApiModelProperty(value = "仓位ID")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "binCode")
    private Long binId;

    @ApiModelProperty(value = "单位id")
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已退库数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "已退旧数量")
    private BigDecimal returnOldQty;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    private Long modifyUserId;

    @ApiModelProperty(value = "是否返回【1是，0否】" , example = "0", required = false)
    private Integer isReturnFlag;

    /* ********************** 顺序扩展字段开始 ****************************/

    @ApiModelProperty(value = "填充属性 - 工具类型id")
    @RlatAttr(rlatTableName = "dic_tool_type", sourceAttrName = "toolTypeName", targetAttrName = "toolTypeName")
    private Long toolTypeId;

    @ApiModelProperty(value = "填充属性 - 工具类型描述")
    private String toolTypeName;

    @ApiModelProperty(value = "维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "维保周期")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "维保有效期")
    private Date maintenanceInDate;

    @ApiModelProperty(value = "保养大纲")
    private String maintenanceProgram;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    /* ********************** 顺序扩展字段结束 ****************************/

    @ApiModelProperty(value = "暂存人")
    private String tempStoreUser;

    @ApiModelProperty(value = "填充属性 - 暂存部门名称")
    private String tempStoreDeptName;
    @ApiModelProperty(value = "填充属性 - 暂存部门code")
    private String tempStoreDeptCode;
    @ApiModelProperty(value = "填充属性 - 暂存科室名称")
    private String tempStoreDeptOfficeName;
    @ApiModelProperty(value = "填充属性 - 暂存科室code")
    private String tempStoreDeptOfficeCode;

    @ApiModelProperty(value = "暂存部门")
    @RlatAttr(rlatTableName = "dic_dept", sourceAttrName = "deptName,deptCode", targetAttrName = "tempStoreDeptName,tempStoreDeptCode")
    private Long tempStoreDeptId;

    @ApiModelProperty(value = "暂存科室")
    @RlatAttr(rlatTableName = "dic_dept_office", sourceAttrName = "deptOfficeName,deptOfficeCode", targetAttrName = "tempStoreDeptOfficeName,tempStoreDeptOfficeCode")
    private Long tempStoreDeptOfficeId;

    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;
    @ApiModelProperty(value = "预留单行项目号")
    private String reservedOrderRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "总价")
    private BigDecimal amount;

    @ApiModelProperty(value = "头部件id")
    @RlatAttr(rlatTableName = "dic_material",sourceAttrName = "*",targetAttrName = "parentMat")
    private Long parentMatId;

    @ApiModelProperty(value = "头部件物料编码")
    private DicMaterialDTO parentMat;
    // 创建预留单行项目号
    private String ridReserved;

    @ApiModelProperty(value = "领料出库单信息id")
    @RlatAttr(rlatTableName = "biz_receipt_apply_head", sourceAttrName = "outInfoId", targetAttrName = "outInfoId")
    private Long matApplyHeadId;

    @ApiModelProperty(value = "领料出库单信息id")
    @RlatAttr(rlatTableName = "biz_receipt_output_info", sourceAttrName = "receiptRemark", targetAttrName = "receiptRemark")
    private Long outInfoId;

    @ApiModelProperty(value = "领料单单据描述")
    private String receiptRemark;
    private int ridReservedNum;

    @ApiModelProperty(value = "工器具 - 管理状态备注")
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 技术参数")
    private String technicalSpecification;

    @ApiModelProperty(value = "工器具 - 维保有效期（维保到期时间）")
    private Date maintenanceValidDate;

    @ApiModelProperty(value = "填充属性 - 暂存期限（1-12月）")
    private Integer tempStorePeriod;

    @ApiModelProperty(value = "填充属性 - 暂存期限（1-12月）")
    private String tempStorePeriodStr;

    @ApiModelProperty(value = "填充属性 - 领用单号")
    private String tempStoreOutCode;

    @ApiModelProperty(value = "填充属性 - 暂存期间维保要求")
    private String maintenanceRequirements;

    @ApiModelProperty(value = "填充属性 - 暂存申请分类:修旧利废0 专用工器具1 见证件2 试块3 其他4")
    private Integer category;

    @ApiModelProperty(value = "填充属性 - 分类:修旧利废0 专用工器具1 见证件2 试块3 其他4")
    private String categoryI18n;

    @ApiModelProperty(value = "填充属性 - 剩余数量")
    private BigDecimal numberRemaining;

    @ApiModelProperty(value = "填充属性 - 暂存前序单据code")
    private String tempStorePreReceiptCode;

    @ApiModelProperty(value = "规格型号")
    private String formatCode;

    @ApiModelProperty(value = "已发数量")
    private BigDecimal sendQty;

    @ApiModelProperty(value = "批准数量")
    private BigDecimal approveQty;

    @ApiModelProperty(value = "工单号" )
    private String workOrder;

    @ApiModelProperty(value = "暂存到期日期")
    private Date tempStoreExpireDate;

    @ApiModelProperty(value = "暂存延期到期日期")
    private Date tempStoreDelayExpireDate;

    @ApiModelProperty(value = "暂存延期原因")
    private String tempStoreDelayReason;

    @ApiModelProperty(value = "退旧类型")
    private Integer returnOldType;

    @ApiModelProperty(value = "已申请数量")
    private BigDecimal appliedQty;

    @ApiModelProperty(value = "已出库数量")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "实发数量")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "移动类型(用于前台显示，不参与过账逻辑)")
    private String moveType;

    @ApiModelProperty(value = "需求计划id")
    private Integer demandPlanId;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求人")
    private String demandUserName;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组ID", notes = "必填,关联物料组主数据")
    private Long matGroupId;

    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @ApiModelProperty(value = "SAP响应字段 MFRPN 制造商零件编号")
    private String extManufacturerPartNumber;

    @ApiModelProperty(value = "SAP响应字段 WRKST 基本物料")
    private String extMainMaterial;

    @ApiModelProperty(value = "SAP响应字段 NORMT 行业标准描述")
    private String extIndustryStandardDesc;

    @ApiModelProperty(value = "状态备注(已结算、已退库)")
    private String statusRemark;

    @ApiModelProperty(value = "退货采购单号")
    private String returnPurchaseCode;

    @ApiModelProperty(value = "退货采购单行项目号")
    private String returnPurchaseRid;
}
