package com.inossem.wms.common.model.bizdomain.input.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> wang
 * @description 工器具数据 基于表output_item
 * @date 2022/3/31 19:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "工器具数据数据传输对象", description = "工器具数据数据传输对象")
public class ToolsInputImportPO implements Serializable {

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "仓位Id" , example = "01-01")
    @ExcelIgnore
    private Long binId;

    @ApiModelProperty(value = "工具类型id" , example = "1")
    private Long toolTypeId;

    @ApiModelProperty(value = "物料编码" , example = "M001005")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "剪刀")
    @ExcelProperty(value = "物料描述", index =1)
    private String matName;

    @ApiModelProperty(value = "计量单位id" , example = "1")
    private Long unitId;

    @ApiModelProperty(value = "计量单位编码" , example = "1")
    private String unitCode;

    @ApiModelProperty(value = "计量单位名称" , example = "EA")
    @ExcelProperty(value = "计量单位", index =2)
    private String unitName;

    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号", index =3)
    private String formatCode;

    @ApiModelProperty(value = "工具类型名称" , example = "通用工具")
    @ExcelProperty(value = "工具类型名称", index =4)
    private String toolTypeName;

    @ApiModelProperty(value = "仓位Code" , example = "A01")
    @ExcelProperty(value = "仓位Code", index =5)
    private String binCode;

    @ApiModelProperty(value = "入库数量" , example = "2")
    @ExcelProperty(value = "入库数量", index =6)
    private BigDecimal inputQty;

    @ApiModelProperty(value = "维保日期" , example = "1")
    @ExcelProperty(value = "维保日期", index =7)
    private Date maintenanceDate;

    @ApiModelProperty(value = "维保周期" , example = "1")
    @ExcelProperty(value = "维保周期", index =8)
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "管理状态值" , example = "1")
    private Integer toolStatus;

    @ApiModelProperty(value = "管理状态" , example = "Inossem")
    @ExcelProperty(value = "管理状态", index =9)
    private String toolStatusName;

    @ApiModelProperty(value = "出厂编码" , example = "3")
    @ExcelProperty(value = "出厂编码", index =10)
    private String outFtyCode;

    @ApiModelProperty(value = "生产厂家" , example = "Inossem")
    @ExcelProperty(value = "生产厂家", index =11)
    private String supplierName;

    @ApiModelProperty(value = "技术参数" , example = "")
    @ExcelProperty(value = "技术参数", index =12)
    private String technicalSpecification;

    @ApiModelProperty(value = "生产日期" , example = "2020-02-02")
    @ExcelProperty(value = "生产日期", index =13)
    private Date productionDate;

    @ApiModelProperty(value = "管理状态" , example = "Inossem")
    @ExcelProperty(value = "管理状态", index =14)
    private String toolManageStatusRemark;

    @ApiModelProperty(value = "计量分级" , example = "")
    @ExcelProperty(value = "计量分级", index =15)
    private String metrologyClassification;

    @ApiModelProperty(value = "准确度等级" , example = "")
    @ExcelProperty(value = "准确度等级", index =16)
    private String levelOfAccuracy;

    @ApiModelProperty(value = "量程" , example = "")
    @ExcelProperty(value = "量程", index =17)
    private String toolsRange;

    @ApiModelProperty(value = "入库日期" , example = "2020-02-02")
    @ExcelProperty(value = "入库日期", index =18)
    private Date inputDate;

    @ApiModelProperty(value = "送检单位" , example = "")
    @ExcelProperty(value = "送检单位", index =19)
    private String toolInspectUnit;

//    @ApiModelProperty(value = "保养大纲" , example = "保养大纲")
//    @ExcelProperty(value = "保养大纲", index =13)
//    private String maintenanceProgram;

}
