package com.inossem.wms.common.model.bizdomain.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/30 11:53
 * @desc BizReceiptToolBorrowApplyItem
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptToolBorrowApplyItem", description = "专用工器具借用行项目")
@TableName("biz_receipt_tool_borrow_apply_item")
public class BizReceiptToolBorrowApplyItem implements Serializable {
    private static final long serialVersionUID = 5654193757481099980L;


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键", example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键", example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型", example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "源存储类型ID")
    private Long sourceTypeId;

    @ApiModelProperty(value = "源仓位ID")
    private Long sourceBinId;

    @ApiModelProperty(value = "目标存储类型ID")
    private Long targetTypeId;

    @ApiModelProperty(value = "目标仓位ID")
    private Long targetBinId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "借出数量或归还数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已归还数量")
    private BigDecimal returnedQty;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "延期原因")
    private String delayReason;

    @ApiModelProperty(value = "归还状态，1合格 0不合格,默认合格")
    private Integer returnStatus;

    @ApiModelProperty(value = "归还状态说明")
    private String returnStatusExplain;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

}
