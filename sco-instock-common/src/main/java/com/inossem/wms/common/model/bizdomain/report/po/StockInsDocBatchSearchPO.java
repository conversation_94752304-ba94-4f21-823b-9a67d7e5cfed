package com.inossem.wms.common.model.bizdomain.report.po;




import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务凭证 查询入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "业务凭证报表 查询入参传输对象", description = "业务凭证报表 查询入参")
public class StockInsDocBatchSearchPO extends PageCommon {

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;
    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    private Long locationId;
    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "批次号" , example = "151692536512514")
    private String batchCode;
    @ApiModelProperty(value = "erp批次" , example = "00016603")
    private String batchErp;

    @ApiModelProperty(value = "物料编码集合")
    private List<String> matCodeList;
    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    private String matName;
    @ApiModelProperty(value = "物料组描述" , example = "物料组描述")
    private String matGroupName;

    @ApiModelProperty(value = "创建日期起始值" , example = "2021-05-11")
    private Date createTimeStart;
    @ApiModelProperty(value = "创建日期结束值" , example = "2021-05-12")
    private Date createTimeEnd;

    @ApiModelProperty(value = "单据编号" , example = "RK0001000633")
    private String preReceiptCode;

    @ApiModelProperty(value = "特殊库存标识" , example = "Q")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;


    @ApiModelProperty(value = "采购订单" , example = "4500000001")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "销售订单" , example = "60000228")
    private String saleReceiptCode;

    @ApiModelProperty(value = "生产订单" , example = "4600000001")
    private String productionReceiptCode;


    @ApiModelProperty(value = "预留单" , example = "4700000001")
    private String reserveReceiptCode;


    @ApiModelProperty(value = "参考单据号" , example = "158430306304001")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "仓储单据类型" , example = "214")
    private Integer preReceiptType;


    @ApiModelProperty(value = "创建人" , example = "1")
    private Long createUserId;


    @ApiModelProperty(value = "创建人姓名" , example = "管理员")
    private String createUserName;
}

