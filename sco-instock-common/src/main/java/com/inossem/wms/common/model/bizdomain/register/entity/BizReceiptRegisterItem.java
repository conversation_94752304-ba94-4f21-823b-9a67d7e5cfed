package com.inossem.wms.common.model.bizdomain.register.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 登记单行项目明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptRegisterItem对象", description="登记单行项目明细表")
@TableName("biz_receipt_register_item")
public class BizReceiptRegisterItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "存储类型id")
    private Long typeId;

    @ApiModelProperty(value = "仓位id")
    private Long binId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "冲销数量")
    private BigDecimal writeOffQty;

    @ApiModelProperty(value = "物料凭证编号" , example = "5211111111")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证的行序号" , example = "111")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度" , example = "2015")
    private String matDocYear;

    @ApiModelProperty(value = "凭证时间" , example = "2021-05-10")
    private Date docDate;

    @ApiModelProperty(value = "过帐日期" , example = "2021-05-11")
    private Date postingDate;

    @ApiModelProperty(value = "sap过账标识0-false, 1-true" , example = "0")
    private Integer isPost;

    @ApiModelProperty(value = "冲销物料凭证号" , example = "52222222")
    private String writeOffMatDocCode;

    @ApiModelProperty(value = "冲销凭证时间" , example = "2021-05-11")
    private Date writeOffDocDate;

    @ApiModelProperty(value = "冲销过帐日期" , example = "2021-05-11")
    private Date writeOffPostingDate;

    @ApiModelProperty(value = "冲销物料凭证行项目号" , example = "0010")
    private String writeOffMatDocRid;

    @ApiModelProperty(value = "冲销年度" , example = "2021")
    private String writeOffMatDocYear;

    @ApiModelProperty(value = "冲销标志0-false, 1-true" , example = "0")
    private Integer isWriteOff;

    @ApiModelProperty(value = "冲销原因")
    private String writeOffReason;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "外观检查")
    private Integer visualCheck;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "本位币金额")
    private BigDecimal dmbtr;

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "工器具 - 赔偿建议")
    private String compensationProposal;

    @ApiModelProperty(value = "需求计划id")
    private Long demandPlanId;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "可登记数量")
    private BigDecimal canRegisterQty;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;       


    //新增5个字段：车辆编号、司机姓名、联系方式、发票号、发票日期

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;  

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发货单行项目id")
    private Long deliveryItemId;        

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;


}
