package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * BI-华信资源驾驶舱 费用分组 查询传输对象
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "费用分组查询传输对象", description = "费用分组查询传输对象")
public class BiExpenseGroupVO {

    @ApiModelProperty(value = "费用分类")
    private String hkont;

    @ApiModelProperty(value = "费用分类描述")
    private String hkontStr;

    @ApiModelProperty(value = "管理费用")
    private BigDecimal dmbtr = BigDecimal.ZERO;

}
