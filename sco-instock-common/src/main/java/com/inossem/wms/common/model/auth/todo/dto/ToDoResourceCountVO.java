package com.inossem.wms.common.model.auth.todo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description="待办事项数量")
public class ToDoResourceCountVO {

	@ApiModelProperty(value = "待办数量" , example = "approval_todo")
	private String resourcesCode;

	@ApiModelProperty(value = "待办数量" , example = "approval_todo")
	private String resourcesCodeI18n;

	@ApiModelProperty(value = "代办事项名称" , example = "入库单")
	private String resourcesName;

	@ApiModelProperty(value = "代办url" , example = "storage/inbound/purchase-inbound/index.vue")
	private String url;
	
	@ApiModelProperty(value = "各个代办数量" , example = "5")
	private Integer count;
	
	@ApiModelProperty(value = "对应业务单据类型" , example = "211")
	private Integer receiptType;

	@ApiModelProperty(value = "是否根据创建人查询" , example = "true")
	private boolean findByCreateUser;

}
