package com.inossem.wms.common.model.auth.user.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "OA登录", description = "OA登录")
@Data
public class OALoginPO {

    @ApiModelProperty(value = "ltpaToken" , example = "1")
    private String ltpaToken;

    @ApiModelProperty(value = "回调参数" , example = "1")
    private String param;
}
