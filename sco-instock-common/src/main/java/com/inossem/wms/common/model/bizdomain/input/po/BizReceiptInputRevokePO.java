package com.inossem.wms.common.model.bizdomain.input.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 入库撤销
 * <AUTHOR>
 * @Date 2021/5/10 19:16
 */
@Data
@ApiModel(value = "入库撤销入参对象", description = "入库撤销入参对象")
public class BizReceiptInputRevokePO implements Serializable {

    private static final long serialVersionUID = -8614138279786486788L;

    @ApiModelProperty(value = "入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long headId;

    @ApiModelProperty(value = "撤销行项目id集合" , example = "149901631619075")
    private List<Long> itemIds;
}
