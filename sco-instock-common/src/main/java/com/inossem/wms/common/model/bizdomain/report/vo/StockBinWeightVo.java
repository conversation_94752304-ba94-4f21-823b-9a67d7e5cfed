package com.inossem.wms.common.model.bizdomain.report.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 重量库存报表 查询出参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "重量库存报表 查询出参传输对象", description = "重量库存报表 查询出参")
public class StockBinWeightVo {
    @ApiModelProperty(value = "物料编码" , example = "M001005")
    @TableField(value = "dm.mat_code")
    @ExcelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料描述" , example = "物料描述001003")
    @TableField(value = "dm.mat_name")
    @ExcelProperty(value = "物料描述")
    private String matName;

    @ApiModelProperty(value = "工厂code" , example = "8000")
    @TableField(value = "df.fty_code")
    @ExcelProperty(value = "工厂编码")
    private String ftyCode;

    @ApiModelProperty(value = "库存地点code" , example = "2700")
    @TableField(value = "dl.location_code")
    @ExcelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "仓库号" , example = "1")
    @TableField(value = "dw.wh_code")
    @ExcelProperty(value = "仓库号")
    private String whCode;

    @ApiModelProperty(value = "重量单位" , example = "M3")
    @TableField(value = "dm.unit_weight")
    @ExcelProperty(value = "重量单位")
    private String unitWeight;

    @ApiModelProperty(value = "工厂描述" , example = "英诺森沈阳工厂")
    @TableField(value = "df.fty_name")
    @ExcelProperty(value = "工厂描述")
    private String ftyName;

    @ApiModelProperty(value = "库存地点描述" , example = "英诺森001")
    @TableField(value = "dl.location_name")
    @ExcelProperty(value = "库存地点描述")
    private String locationName;

    @ApiModelProperty(value = "存储类型描述" , example = "入库临时区")
    @TableField(value = "dwt.type_name")
    @ExcelProperty(value = "存储类型描述")
    private String typeName;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    @TableField(value = "dw.wh_name")
    @ExcelProperty(value = "仓库描述")
    private String whName;

    @ApiModelProperty(value = "存储类型code" , example = "T001")
    @TableField(value = "dwt.type_code")
    @ExcelProperty(value = "存储类型编码")
    private String typeCode;

    @ApiModelProperty(value = "仓位code" , example = "00")
    @TableField(value = "dwb.bin_code")
    @ExcelProperty(value = "仓位编码")
    private String binCode;

    @ApiModelProperty(value = "存储区code" , example = "s008")
    @TableField(value = "dwss.section_code")
    @ExcelProperty(value = "存储区编码")
    private String sectionCode;

    @ApiModelProperty(value = "存储区描述" , example = "存储区008")
    @TableField(value = "dwss.section_name")
    @ExcelProperty(value = "存储区描述")
    private String sectionName;

    @ApiModelProperty(value = "电子秤ID" , example = "10000003")
    @TableField(value = "dwc.cell_code")
    @ExcelProperty(value = "电子秤ID")
    private String cellCode;


    @ApiModelProperty(value = "物料净重" , example = "0.5")
    @TableField(value = "dm.net_weight")
    @ExcelProperty(value = "物料净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "库存重量" , example = "50")
    @TableField(value = "sbw.qty")
    @ExcelProperty(value = "库存重量")
    private BigDecimal qty;
}
