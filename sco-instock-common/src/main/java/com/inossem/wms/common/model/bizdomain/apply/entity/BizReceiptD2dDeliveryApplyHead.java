package com.inossem.wms.common.model.bizdomain.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 * 门到门送货申请
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptD2dDeliveryApplyHead", description = "门到门送货申请抬头")
@TableName("biz_receipt_d2d_delivery_apply_head")
public class BizReceiptD2dDeliveryApplyHead implements Serializable {
    private static final long serialVersionUID = -5732895754656799758L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型【借用申请：9050；维修申请：9051；报废申请：9052】")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "申请原因")
    private String applyReason;

    @ApiModelProperty(value = "单据备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "需求人部门领导")
    private String level1ApproveUserCode;

    @ApiModelProperty(value = "需求人部门领导")
    private String level1ApproveUserName;

    @ApiModelProperty(value = "业务经办人")
    private String level2ApproveUserCode;

    @ApiModelProperty(value = "业务经办人")
    private String level2ApproveUserName;

    @ApiModelProperty(value = "代进口经办人")
    private String level3ApproveUserCode;

    @ApiModelProperty(value = "代进口经办人")
    private String level3ApproveUserName;

    @ApiModelProperty(value = "经营管理部领导")
    private String level4ApproveUserCode;

    @ApiModelProperty(value = "经营管理部领导")
    private String level4ApproveUserName;

    @ApiModelProperty(value = "需求部门分管领导")
    private String level5ApproveUserCode;

    @ApiModelProperty(value = "需求部门分管领导")
    private String level5ApproveUserName;

    @ApiModelProperty(value = "金额总价USD")
    private BigDecimal usdAmount;

    @ApiModelProperty(value = "重量总计")
    private BigDecimal weight;

    @ApiModelProperty(value = "要求到货时间")
    private Date requiredArrivalTime;
    
    @ApiModelProperty(value = "物料紧急程度，1特急2紧急3一般")
    private Integer logisticsUrgency;



}
