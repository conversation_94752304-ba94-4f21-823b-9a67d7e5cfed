package com.inossem.wms.common.model.bizdomain.output.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 工器具报废查询条件对象
 *
 * <AUTHOR>
 * @date 2022/03/31 20:44
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizReceiptToolScrapSearchPO extends PageCommon {

    @ApiModelProperty(value = "报废出库单号", example = "WB00000118")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型 411 销售出库/413 采购退货/414 领料或预留出库/415 报废出库/416 其他出库/418 临时出库", example = "411")
    private Integer receiptType;

    @ApiModelProperty(value = "前置单据号" , example = "RK0001000633")
    private String preReceiptCode;

    @ApiModelProperty(value = "查询单据目标状态列表" , example = "10")
    private List<Integer> receiptStatusList;

    @ApiModelProperty(value = "单据状态" , example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "工具编码", example = "工具001")
    private String toolId;

    @ApiModelProperty(value = "物料描述", example = "物料001")
    private String matName;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01")
    private Date createTime;

    @ApiModelProperty(value = "创建人姓名", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "创建人姓名-数据库字段名映射", example = "管理员")
    private String userName;

    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "库存地点Id")
    private Long locationId;


}
