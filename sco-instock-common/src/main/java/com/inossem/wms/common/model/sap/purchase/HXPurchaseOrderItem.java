package com.inossem.wms.common.model.sap.purchase;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SAP采购订单创建行项目参数
 */
@Data
public class HXPurchaseOrderItem {

    @ApiModelProperty(value = "行项目号 EBELP")
    private String itemCode = "";
    
    @ApiModelProperty(value = "科目分配类别 KNTTP")
    private String accountCategory = "";
    
    // @ApiModelProperty(value = "项目类别 PSTYP")
    // private String itemCategory = "";

    @ApiModelProperty(value = "物料编码 MATNR")
    private String matCode = "";

    @ApiModelProperty(value = "物料描述 TXZ01")
    private String matName = "";
    
    @ApiModelProperty(value = "物料组 MATKL")
    private String materialGroup = "";

    @ApiModelProperty(value = "数量 MENGE")
    private String qty = "";

    @ApiModelProperty(value = "计量单位 MEINS")
    private String unitCode = "";

    @ApiModelProperty(value = "交货日期 EEIND")
    private String deliveryDate = "";

    @ApiModelProperty(value = "采购单价不含税 NETPR")
    private String netPrice = "";

    @ApiModelProperty(value = "ZT01 关税 KBETR1")
    private String tariff = "";

    @ApiModelProperty(value = "ZT01关税币种 KOEIN1")
    private String tariffCurrency = "";

    @ApiModelProperty(value = "ZT01 供应商编码 LIFNR1")
    private String supplierCode1 = "";

    @ApiModelProperty(value = "ZT02 附加关税 KBETR2")
    private String additionalTariff = "";

    @ApiModelProperty(value = "ZT02 附加关税币种 KOEIN2")
    private String additionalTariffCurrency = "";
    
    @ApiModelProperty(value = "ZT02 供应商编码 LIFNR2")
    private String supplierCode2 = "";

    @ApiModelProperty(value = "ZT03 调节税 KBETR3")
    private String adjustmentTax = "";

    @ApiModelProperty(value = "ZT03 调节税币种 KOEIN3")
    private String adjustmentTaxCurrency = "";

    @ApiModelProperty(value = "ZT03 供应商编码 LIFNR3")
    private String supplierCode3 = "";

    @ApiModelProperty(value = "ZT04 调联邦消费税 KBETR4")
    private String federalConsumptionTax = "";

    @ApiModelProperty(value = "ZT04 调联邦消费税币种 KOEIN4")
    private String federalConsumptionTaxCurrency = "";

    @ApiModelProperty(value = "ZT04 供应商编码 LIFNR4")
    private String supplierCode4 = "";

    @ApiModelProperty(value = "ZT05 逾期罚款 KBETR5")
    private String overduePenalty = "";

    @ApiModelProperty(value = "ZT05 逾期罚款币种 KOEIN5")
    private String overduePenaltyCurrency = "";

    @ApiModelProperty(value = "ZT05 供应商编码 LIFNR5")
    private String supplierCode5 = "";

    @ApiModelProperty(value = "ZT06 发票缺失 KBETR6")
    private String invoiceMissing = "";

    @ApiModelProperty(value = "ZT06 发票缺失币种 KOEIN6")
    private String invoiceMissingCurrency = "";

    @ApiModelProperty(value = "ZT06 供应商编码 LIFNR6")
    private String supplierCode6 = "";
    
    @ApiModelProperty(value = "ZT07 GD提交费 KBETR7")
    private String gdSubmissionFee = "";

    @ApiModelProperty(value = "ZT07 GD提交费币种 KOEIN7")
    private String gdSubmissionFeeCurrency = "";

    @ApiModelProperty(value = "ZT07 供应商编码 LIFNR7")
    private String supplierCode7 = "";

    @ApiModelProperty(value = "ZT08 印花税 KBETR8")
    private String stampDuty = "";

    @ApiModelProperty(value = "ZT08 印花税币种 KOEIN8")
    private String stampDutyCurrency = "";

    @ApiModelProperty(value = "ZT08 供应商编码 LIFNR8")
    private String supplierCode8 = "";

    @ApiModelProperty(value = "ZT09 基建税 KBETR9")
    private String constructionTax = "";

    @ApiModelProperty(value = "ZT09 基建税币种 KOEIN9")
    private String constructionTaxCurrency = "";

    @ApiModelProperty(value = "ZT09 供应商编码 LIFNR9")
    private String supplierCode9 = "";

    @ApiModelProperty(value = "ZT10 检验检测费 KBETR10")
    private String inspectionFee = "";

    @ApiModelProperty(value = "ZT10 检验检测费币种 KOEIN10")
    private String inspectionFeeCurrency = "";

    @ApiModelProperty(value = "ZT10 供应商编码 LIFNR10")
    private String supplierCode10 = "";

    @ApiModelProperty(value = "ZF01 海/空运费 KBETR11")
    private String seaAirFreight = "";

    @ApiModelProperty(value = "ZF01 海/空运费币种 KOEIN11")
    private String seaAirFreightCurrency = "";

    @ApiModelProperty(value = "ZF01 供应商编码 LIFNR11")
    private String supplierCode11 = "";
    
    @ApiModelProperty(value = "ZF02 内陆运输费 KBETR12")
    private String inlandTransportFee = "";
    
    @ApiModelProperty(value = "ZF02 内陆运输费币种 KOEIN12")
    private String inlandTransportFeeCurrency = "";
    
    @ApiModelProperty(value = "ZF02 供应商编码 LIFNR12")
    private String supplierCode12 = "";

    @ApiModelProperty(value = "ZF03 出口报关服务费 KBETR13")
    private String exportCustomsServiceFee = "";

    @ApiModelProperty(value = "ZF03 出口报关服务费币种 KOEIN13")
    private String exportCustomsServiceFeeCurrency = "";
    
    @ApiModelProperty(value = "ZF03 供应商编码 LIFNR13")
    private String supplierCode13 = "";

    @ApiModelProperty(value = "ZF04 进口清关服务费 KBETR14")
    private String importCustomsServiceFee = "";

    @ApiModelProperty(value = "ZF04 进口清关服务费币种 KOEIN14")
    private String importCustomsServiceFeeCurrency = "";
    
    @ApiModelProperty(value = "ZF04 供应商编码 LIFNR14")
    private String supplierCode14 = "";

    @ApiModelProperty(value = "ZF05 进口拖车押车费（车船直取） KBETR15")
    private String importTruckRentalFee = "";

    @ApiModelProperty(value = "ZF05 进口拖车押车费（车船直取）币种 KOEIN15")
    private String importTruckRentalFeeCurrency = "";
    
    @ApiModelProperty(value = "ZF05 进口拖车押车费（车船直取）供应商编码 LIFNR15")
    private String supplierCode15 = "";

    // @ApiModelProperty(value = "价格单位 PEINH")
    // private String priceUnit = "";

    // @ApiModelProperty(value = "币种 WAERS")
    // private String currency = "";

    @ApiModelProperty(value = "工厂 WERKS")
    private String factory = "";

    @ApiModelProperty(value = "库存地点 LGORT")
    private String stockLocation = "";

    @ApiModelProperty(value = "需求跟踪号 BEDNR")
    private String requirementTrackingNumber = "";

    @ApiModelProperty(value = "税码 MWSKZ")
    private String taxCode = "";
    
    // @ApiModelProperty(value = "采购订单删除标识 LOEKZ")
    // private String purchaseOrderDeleteFlag = "";

    // @ApiModelProperty(value = "基于收货的发票校验 WEBRE")
    // private String invoiceVerificationFlag = "";

    // @ApiModelProperty(value = "退货项目标识 RETPO")
    // private String returnItemFlag = "";

    // @ApiModelProperty(value = "免费项目标识 UMSON")
    // private String freeItemFlag = "";

    // @ApiModelProperty(value = "收货已完成标识 ELIKZ")
    // private String receiptFinishedFlag = "";

    @ApiModelProperty(value = "资产卡片号 ANLN1")
    private String assetCardNumber = "";

    @ApiModelProperty(value = "成本中心 KOSTL")
    private String costCenter = "";

    @ApiModelProperty(value = "项目编码WBS PS_POSID")
    private String projectCode = "";


} 