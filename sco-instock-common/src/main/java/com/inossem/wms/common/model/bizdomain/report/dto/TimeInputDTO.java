package com.inossem.wms.common.model.bizdomain.report.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 入库时效
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "入库时效 查询出参传输对象")
public class TimeInputDTO extends PageCommon {

    @ExcelProperty(value = "验收入库单号")
    private String receiptCode;

    @ExcelProperty(value = "验收入库单创建时间")
    private Date createTime;

    @ExcelProperty(value = "验收入库单提交时间")
    private Date submitTime;

    @ExcelProperty(value = "入库办理时效")
    private Integer dateDiff;

    @ApiModelProperty(value = "验收入库单号")
    @ExcelIgnore
    private List<String> receiptCodeList;

    @ApiModelProperty(value = "验收入库单创建时间")
    @ExcelIgnore
    private Date createTimeStart;

    @ApiModelProperty(value = "验收入库单创建时间")
    @ExcelIgnore
    private Date createTimeEnd;

    @ApiModelProperty(value = "验收入库单提交时间")
    @ExcelIgnore
    private Date submitTimeStart;

    @ApiModelProperty(value = "验收入库单提交时间")
    @ExcelIgnore
    private Date submitTimeEnd;

}
