package com.inossem.wms.common.model.bizdomain.paper.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 图纸抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
//@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptPaperHead对象", description="图纸抬头表导入")
@TableName("biz_receipt_paper_head")
public class BizReceiptPaperHeadImportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "图纸ID")
    @ApiModelProperty(value = "图纸ID")
    private String npiotPaperInfoDesignId;

    @ExcelProperty(value = "提交状态")
    @ApiModelProperty(value = "提交状态1：已经提交；0：未提交；2：驳回")
    private String submitflg;

    @ExcelProperty(value = "工程文件编码")
    @ApiModelProperty(value = "工程文件编码")
    private String fileCode;

    @ExcelProperty(value = "内部编号")
    @ApiModelProperty(value = "内部编号")
    private String innerCode;

    @ExcelProperty(value = "版本")
    @ApiModelProperty(value = "版本")
    private String version;

    @ExcelProperty(value = "卷标")
    @ApiModelProperty(value = "卷标")
    private String fileMark;

    @ExcelProperty(value = "状态")
    @ApiModelProperty(value = "状态")
    private String statusCode;

    @ExcelProperty(value = "项目编号")
    @ApiModelProperty(value = "项目编号")
    private String projCode;

    @ExcelProperty(value = "岛别")
    @ApiModelProperty(value = "岛别(NI/CI/BOP)")
    private String island;

    @ExcelProperty(value = "机组")
    @ApiModelProperty(value = "机组")
    private String term;

    @ExcelProperty(value = "系统")
    @ApiModelProperty(value = "系统")
    private String paperSystem;

    @ExcelProperty(value = "专业")
    @ApiModelProperty(value = "专业(EM1,EM2,EM3,EM4.1,EM4.2,EM5,EM6,EM7,EM8,EM9,EM10,CE:土建,ME:机械,HP:保温,PI:管道,EL:电气,IN:仪表,HVAC:暖通空调)")
    private String major;

    @ExcelProperty(value = "区域")
    @ApiModelProperty(value = "区域")
    private String area;

    @ExcelProperty(value = "厂房")
    @ApiModelProperty(value = "厂房")
    private String factoryBuilding;

    @ExcelProperty(value = "标高")
    @ApiModelProperty(value = "标高")
    private String level;

    @ExcelProperty(value = "房间")
    @ApiModelProperty(value = "房间")
    private String room;

    @ExcelProperty(value = "设计公司")
    @ApiModelProperty(value = "设计公司")
    private String designCompany;

    @ExcelProperty(value = "图纸类型")
    @ApiModelProperty(value = "图纸类型")
    private String type;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注")
    private String note;

    @ExcelProperty(value = "图纸输入单位")
    @ApiModelProperty(value = "图纸输入单位")
    private String unit;

    @ExcelProperty(value = "来源")
    @ApiModelProperty(value = "来源")
    private String source;

    @ExcelProperty(value = "图纸原始更新者")
    @ApiModelProperty(value = "图纸原始更新者")
    private String updater;

    @ExcelProperty(value = "状态标识")
    @ApiModelProperty(value = "状态标识C：新建 U：修改 D：逻辑删除")
    private String status;

    @ExcelProperty(value = "更新时间")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ExcelProperty(value = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updateOperator;

    @ExcelProperty(value = "创建时间")
    @ApiModelProperty(value = "创建时间")
    private Date paperCreateTime;

    @ExcelProperty(value = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createOperator;

    @ExcelProperty(value = "预留字段1")
    @ApiModelProperty(value = "预留字段1")
    private String extend1;

    @ExcelProperty(value = "预留字段2")
    @ApiModelProperty(value = "预留字段2")
    private String extend2;

    @ExcelProperty(value = "预留字段3")
    @ApiModelProperty(value = "预留字段3")
    private String extend3;

    @ExcelProperty(value = "预留字段4")
    @ApiModelProperty(value = "预留字段4")
    private String extend4;

    @ExcelProperty(value = "预留字段5")
    @ApiModelProperty(value = "预留字段5")
    private String extend5;

    @ExcelProperty(value = "图纸清单最后更新时间")
    @ApiModelProperty(value = "图纸最后更新时间")
    private String lastUpdateTime;

    @ExcelProperty(value = "是否是最新版")
    @ApiModelProperty(value = "是否是最新版")
    private String newVersion;

    @ExcelProperty(value = "文件名称")
    @ApiModelProperty(value = "文件名")
    private String fileName;




    public String getNpiotPaperInfoDesignId() {
        return npiotPaperInfoDesignId;
    }

    public void setNpiotPaperInfoDesignId(String npiotPaperInfoDesignId) {
        this.npiotPaperInfoDesignId = npiotPaperInfoDesignId;
    }

    public String getSubmitflg() {
        return submitflg;
    }

    public void setSubmitflg(String submitflg) {
        this.submitflg = submitflg;
    }

    public String getFileCode() {
        return fileCode;
    }

    public void setFileCode(String fileCode) {
        this.fileCode = fileCode;
    }

    public String getInnerCode() {
        return innerCode;
    }

    public void setInnerCode(String innerCode) {
        this.innerCode = innerCode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getFileMark() {
        return fileMark;
    }

    public void setFileMark(String fileMark) {
        this.fileMark = fileMark;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getProjCode() {
        return projCode;
    }

    public void setProjCode(String projCode) {
        this.projCode = projCode;
    }

    public String getIsland() {
        return island;
    }

    public void setIsland(String island) {
        this.island = island;
    }

    public String getTerm() {
        return term;
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public String getPaperSystem() {
        return paperSystem;
    }

    public void setPaperSystem(String paperSystem) {
        this.paperSystem = paperSystem;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getFactoryBuilding() {
        return factoryBuilding;
    }

    public void setFactoryBuilding(String factoryBuilding) {
        this.factoryBuilding = factoryBuilding;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getDesignCompany() {
        return designCompany;
    }

    public void setDesignCompany(String designCompany) {
        this.designCompany = designCompany;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getNewVersion() {
        return newVersion;
    }

    public void setNewVersion(String newVersion) {
        this.newVersion = newVersion;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOperator() {
        return updateOperator;
    }

    public void setUpdateOperator(String updateOperator) {
        this.updateOperator = updateOperator;
    }

    public Date getPaperCreateTime() {
        return paperCreateTime;
    }

    public void setPaperCreateTime(Date paperCreateTime) {
        this.paperCreateTime = paperCreateTime;
    }

    public String getCreateOperator() {
        return createOperator;
    }

    public void setCreateOperator(String createOperator) {
        this.createOperator = createOperator;
    }

    public String getExtend1() {
        return extend1;
    }

    public void setExtend1(String extend1) {
        this.extend1 = extend1;
    }

    public String getExtend2() {
        return extend2;
    }

    public void setExtend2(String extend2) {
        this.extend2 = extend2;
    }

    public String getExtend3() {
        return extend3;
    }

    public void setExtend3(String extend3) {
        this.extend3 = extend3;
    }

    public String getExtend4() {
        return extend4;
    }

    public void setExtend4(String extend4) {
        this.extend4 = extend4;
    }

    public String getExtend5() {
        return extend5;
    }

    public void setExtend5(String extend5) {
        this.extend5 = extend5;
    }
}
