package com.inossem.wms.common.model.bizdomain.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 借用申请单行项目明细表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizReceiptApplyItem对象", description="借用申请单行项目明细表")
@TableName("biz_receipt_apply_item")
public class BizReceiptApplyItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表id")
    private Long headId;

    @ApiModelProperty(value = "行项目序号")
    private String rid;

    @ApiModelProperty(value = "行项目状态")
    private Integer itemStatus;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前续单据操作数量")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据head主键" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据item主键" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "工厂id")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id")
    private Long locationId;

    @ApiModelProperty(value = "成套库存地点id" , example = "145725436526593")
    private Long mainLocationId;

    @ApiModelProperty(value = "仓库id")
    private Long whId;

    @ApiModelProperty(value = "物料id")
    private Long matId;

    @ApiModelProperty(value = "批次id")
    private Long batchId;

    @ApiModelProperty(value = "存储类型ID")
    private Long typeId;

    @ApiModelProperty(value = "仓位ID")
    private Long binId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "特殊库存代码" , example = "wbsCode2")
    private String specStockCode;

    @ApiModelProperty(value = "WBS编码" , example = "wbsCode2")
    private String whCodeOut;

    @ApiModelProperty(value = "特殊库存描述" , example = "wbsName2")
    private String specStockName;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已退库数量")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "已退旧数量")
    private BigDecimal returnOldQty;

    @ApiModelProperty(value = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @ApiModelProperty(value = "是否返回【1是，0否】" , example = "0", required = false)
    private Integer isReturnFlag;

    @ApiModelProperty(value = "暂存人")
    private String tempStoreUser;

    @ApiModelProperty(value = "暂存部门")
    private Long tempStoreDeptId;

    @ApiModelProperty(value = "暂存科室")
    private Long tempStoreDeptOfficeId;

    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;
    @ApiModelProperty(value = "预留单行项目号")
    private String reservedOrderRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "总价")
    private BigDecimal amount;

    @ApiModelProperty(value = "工器具 - 使用人")
    private String toolUserName;

    @ApiModelProperty(value = "工器具 - 使用位置")
    private String toolUsePlace;

    @ApiModelProperty(value = "工器具 - 使用原因")
    private String toolUseReason;

    @ApiModelProperty(value = "填充属性 - 暂存期限（1-12月）")
    private Integer tempStorePeriod;

    @ApiModelProperty(value = "填充属性 - 领用单号")
    private String tempStoreOutCode;

    @ApiModelProperty(value = "填充属性 - 暂存期间维保要求")
    private String maintenanceRequirements;

    @ApiModelProperty(value = "填充属性 - 暂存申请分类:修旧利废0 专用工器具1 见证件2 试块3 其他4")
    private Integer category;

    @ApiModelProperty(value = "生产日期")
    private Date productDate;

    @ApiModelProperty(value = "批准数量")
    private BigDecimal approveQty;

    @ApiModelProperty(value = "工单号" )
    private String workOrder;

    @ApiModelProperty(value = "暂存前序单据code")
    private String tempStorePreReceiptCode;

    @ApiModelProperty(value = "暂存到期日期")
    private Date tempStoreExpireDate;

    @ApiModelProperty(value = "暂存延期到期日期")
    private Date tempStoreDelayExpireDate;

    @ApiModelProperty(value = "暂存延期原因")
    private String tempStoreDelayReason;

    @ApiModelProperty(value = "退旧类型")
    private Integer returnOldType;

    @ApiModelProperty(value = "已申请数量")
    private BigDecimal appliedQty;

    @ApiModelProperty(value = "已出库数量")
    private BigDecimal outputQty;

    @ApiModelProperty(value = "实发数量")
    private BigDecimal actualQty;

    @ApiModelProperty(value = "需求计划id")
    private String demandPlanId;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求人")
    private String demandUserName;

    @ApiModelProperty(value = "状态备注(已结算、已退库)")
    private String statusRemark;

    @ApiModelProperty(value = "退货采购单号")
    private String returnPurchaseCode;

    @ApiModelProperty(value = "退货采购单行项目号")
    private String returnPurchaseRid;
}
