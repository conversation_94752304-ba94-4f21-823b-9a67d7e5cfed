package com.inossem.wms.common.model.auth.todo.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.model.print.template.dto.DicPrintTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 模版附件定时任务
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TemplateTaskDTO对象", description="模版附件定时任务")
public class TemplateTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键" , example = "111")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "任务类型 1 同步dts")
    private Integer type;

    @ApiModelProperty(value = "head表id" , example = "1")
    private Long headId;

    @ApiModelProperty(value = "单据号" , example = "4500000001")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型" , example = "211" )
    private Integer receiptType;

    @ApiModelProperty(value = "模版单据类型" , example = "211" )
    private Integer templateReceiptType;

    @ApiModelProperty(value = "参数" )
    private String para;

    @ApiModelProperty(value = "处理标识  0 未处理 1处理中 2已生成pdf 3已同步 4已处理" )
    private Integer dealFlag;

    @ApiModelProperty(value = "处理时间")
    private Date dealTime;

    @ApiModelProperty(value = "是否删除【1是，0否】")
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    private  DicPrintTemplateDTO dicPrintTemplateDTO;
}
