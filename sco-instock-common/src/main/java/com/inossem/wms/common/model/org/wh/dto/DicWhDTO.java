package com.inossem.wms.common.model.org.wh.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.org.storagetype.dto.DicWhStorageTypeDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓库数据传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = false)
@ApiModel(value = "仓库数据传输对象", description = "仓库主数据表")
public class DicWhDTO implements Serializable {

    private static final long serialVersionUID = 6251885845697263053L;

    /* ********************** 扩展字段 *************************/

    /**
     * 存储类型集合
     */
    @SonAttr(sonTbName = "dic_wh_storage_type", sonTbFkAttrName = "whId")
    @ApiModelProperty(value = "存储类型集合", name = "typeDtoList", required = true)
    private List<DicWhStorageTypeDTO> typeDtoList;

    /**
     * 创建人编码
     */
    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "管理员", required = true)
    private String createUserName;

    /**
     * 修改人编码
     */
    @ApiModelProperty(value = "修改人编码", example = "Admin", required = true)
    private String modifyUserCode;

    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称", example = "管理员", required = true)
    private String modifyUserName;

    /**
     * 启用仓位描述
     */
    @ApiModelProperty(value = "启用仓位描述" , example = "是")
    private String isBinEnabledI18n;

    /**
     * 启用存储单元描述
     */
    @ApiModelProperty(value = "启用存储单元描述" , example = "是")
    private String isCellEnabledI18n;

    /**
     * 启用拣配描述
     */
    @ApiModelProperty(value = "启用拣配描述" , example = "是")
    private String isPickingEnabledI18n;

    /**
     * 启用WCS描述
     */
    @ApiModelProperty(value = "启用WCS描述" , example = "是")
    private String isWcsEnabledI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "仓库号" , example = "1")
    private String whCode;

    @ApiModelProperty(value = "仓库描述" , example = "英诺森仓库沈阳")
    private String whName;

    @ApiModelProperty(value = "仓库地址" , example = "新地中心")
    private String address;

    @ApiModelProperty(value = "国家" , example = "中国")
    private String country;

    @ApiModelProperty(value = "城市" , example = "沈阳")
    private String city;

    @ApiModelProperty(value = "启用仓位" , example = "1")
    private Integer isBinEnabled;

    @ApiModelProperty(value = "启用存储单元" , example = "1")
    private Integer isCellEnabled;

    @ApiModelProperty(value = "启用拣配" , example = "1")
    private Integer isPickingEnabled;

    @ApiModelProperty(value = "启用WCS【1是，0否】" , example = "1")
    private Integer isWcsEnabled;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;
    // 集团仓库号
    private String groupWhNo;
}