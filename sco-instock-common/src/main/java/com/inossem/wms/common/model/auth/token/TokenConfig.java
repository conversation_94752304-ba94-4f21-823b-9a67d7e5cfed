package com.inossem.wms.common.model.auth.token;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Component
@ApiModel(value = "token", description = "token")
@ConfigurationProperties(prefix = "token")
public class TokenConfig {

    @ApiModelProperty(value = "认证Token")
    private Token accessToken;

    @ApiModelProperty(value = "刷新Token")
    private Token refreshToken;
}
