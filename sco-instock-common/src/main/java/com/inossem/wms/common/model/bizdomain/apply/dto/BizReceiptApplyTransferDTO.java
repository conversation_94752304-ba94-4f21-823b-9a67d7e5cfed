package com.inossem.wms.common.model.bizdomain.apply.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 配货特征传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "领料申请和转性传输对象", description = "领料申请和转性传输对象")
public class BizReceiptApplyTransferDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /* ********************** 扩展字段 *************************/

    // 前端必须要的字段
    private String parentId;
    private String uid;

    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer outputStockStatus;
    @ApiModelProperty(value = "库存状态10-非限制,20-在途,30-质检,40-冻结单号" , example = "10")
    private Integer inputStockStatus;
    @ApiModelProperty(value = "总数量" , example = "5")
    private BigDecimal qtyTotal;
    @ApiModelProperty(value = "发出特殊库存标识" , example = "Q")
    private String outSpecStock;
    @ApiModelProperty(value = "预留单号")
    private Long reservedOrderCode;
    @ApiModelProperty(value = "WBS编码")
    private String whCodeOut;
    @ApiModelProperty(value = "预留单行项目号")
    private String reservedOrderRid;
    @ApiModelProperty(value = "行项目序号")
    private String rid;
    @ApiModelProperty(value = "扩展属性 - 单据编号")
    private String receiptCode;
    @ApiModelProperty(value = "填充属性 - 前置单据编码")
    private String preReceiptCode;
    @ApiModelProperty(value = "凭证时间")
    private Date docDate;
    @ApiModelProperty(value = "过账时间")
    private Date postingDate;
    @ApiModelProperty(value = "sap转性标识0-false, 1-true")
    private Integer isTransfer;
    @ApiModelProperty(value = "sap预留标识0-false, 1-true")
    private Integer isReserved;

    private int transferFlag;
    @SonAttr(sonTbName = "biz_receipt_transport_bin", sonTbFkAttrName = "itemId")
    @ApiModelProperty(value = "行项目列表")
    List<BizReceiptTransportBinDTO> binDTOList;
    @ApiModelProperty(value = "物料凭证")
    private String matDocCode;
    @ApiModelProperty(value = "物料凭证行号")
    private String matDocRid;
    @ApiModelProperty(value = "物料凭证年份")
    private String matDocYear;

    private String inputSpecStockName;
    @ApiModelProperty(value = "接收特殊库存代码")
    private String inputSpecStockCode;
    @ApiModelProperty(value = "接收仓库编码")
    private String inputWhCode;
    @ApiModelProperty(value = "接收仓库号id")
    private Long inputWhId;
    @ApiModelProperty(value = "接收物料单位id")
    private Long inputUnitId;
    @ApiModelProperty(value = "接收库存地点id")
    private Long inputLocationId;
    @ApiModelProperty(value = "发出库存地点编码" , example = "2800")
    private String inputLocationCode;
    @ApiModelProperty(value = "接收工厂id")
    private Long inputFtyId;
    @ApiModelProperty(value = "发出工厂编码" , example = "8000")
    private String inputFtyCode;
    @ApiModelProperty(value = "接收物料id")
    private Long inputMatId;
    @ApiModelProperty(value = "接收物料编码" , example = "M001005")
    private String inputMatCode;

    @ApiModelProperty(value = "接收物料名称" , example = "物料描述001003")
    private String inputMatName;

    @ApiModelProperty(value = "发出特殊库存代码" , example = "")
    private String outputSpecStockCode;
    @ApiModelProperty(value = "发出仓库编码")
    private String outputWhCode;
    @ApiModelProperty(value = "发出仓库号id")
    private Long outputWhId;
    @ApiModelProperty(value = "发出物料单位id")
    private Long outputUnitId;
    @ApiModelProperty(value = "发出单位编码" , example = "M3")
    private String outputUnitCode;
    @ApiModelProperty(value = "发出单位名称" , example = "立方米")
    private String outputUnitName;
    @ApiModelProperty(value = "发出库存地点id")
    private Long outputLocationId;
    @ApiModelProperty(value = "发出工厂id")
    private Long outputFtyId;
    @ApiModelProperty(value = "发出物料id")
    private Long outputMatId;
    @ApiModelProperty(value = "发出物料编码" , example = "M001005")
    private String outputMatCode;
    @ApiModelProperty(value = "发出物料名称" , example = "物料描述001003")
    private String outputMatName;
    @ApiModelProperty(value = "发出库存地点编码" , example = "2800")
    private String outputLocationCode;
    @ApiModelProperty(value = "发出库存地点名称" , example = "英诺森004")
    private String outputLocationName;
    @ApiModelProperty(value = "发出工厂编码" , example = "8000")
    private String outputFtyCode;
    @ApiModelProperty(value = "发出工厂名称" , example = "英诺森沈阳工厂")
    private String outputFtyName;

//    @ApiModelProperty(value = "预留单状态状态名称")
//    private String reservedStatusI18n;
//    @ApiModelProperty(value = "预留单状态")
//    private Integer reservedStatus;
//    @ApiModelProperty(value = "转性状态名称")
//    private String transferStatusI18n;
//    @ApiModelProperty(value = "转性状态")
//    private Integer transferStatus;
    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;
    /**
     * 标签列表
     */
    @SonAttr(sonTbName = "biz_label_receipt_rel", sonTbFkAttrName = "receiptBinId")
    @ApiModelProperty(value = "行项目列表")
    List<BizLabelReceiptRelDTO> labelDataList;
    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码", name = "ftyCode", example = "1206", required = true)
    private String ftyCode;
    /**
     * 工厂描述
     */
    @ApiModelProperty(value = "工厂描述", name = "ftyName", example = "1206", required = true)
    private String ftyName;
    /**
     * 库存地点编码
     */
    @ApiModelProperty(value = "库存地点编码", name = "locationCode", example = "0001", required = true)
    private String locationCode;
    /**
     * 库存地点描述
     */
    @ApiModelProperty(value = "库存地点描述", name = "locationCode", example = "0001", required = true)
    private String locationName;
    /**
     * 成套库存地点编码
     */
    @ApiModelProperty(value = "成套库存地点编码" , example = "2500")
    private String mainLocationCode;
    /**
     * 成套库存地点描述
     */
    @ApiModelProperty(value = "成套库存地点描述" , example = "英诺森001")
    private String mainLocationName;
    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码", name = "whCode", example = "0001", required = true)
    private String whCode;
    /**
     * 仓库描述
     */
    @ApiModelProperty(value = "仓库描述", name = "whCode", example = "0001", required = true)
    private String whName;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", name = "matCode", example = "80012345", required = true)
    private String matCode;

    @ApiModelProperty(value = "物料描述", name = "matName", example = "80012345", required = true)
    private String matName;

    @ApiModelProperty(value = "维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "维保周期")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "维保有效期")
    private Date maintenanceInDate;

    @ApiModelProperty(value = "保养大纲")
    private String maintenanceProgram;

    /**
     * 单位编码code
     */
    @ApiModelProperty(value = "单位编码", name = "unitCode", example = "EA", required = true)
    private String unitCode;

    @ApiModelProperty(value = "单位描述", name = "unitName", example = "EA", required = true)
    private String unitName;

    @ApiModelProperty(value = "小数位" , example = "3")
    private Integer decimalPlace;

    @ApiModelProperty(value = "创建人编码", example = "Admin", required = true)
    private String createUserCode;

    @ApiModelProperty(value = "创建人描述", name = "createUserName", example = "EA", required = true)
    private String createUserName;
    @ApiModelProperty(value = "接收存储类型" , example = "802")
    private String inputTypeCode;
    @ApiModelProperty(value = "接收存储类型名称" , example = "组盘临时区")
    private String inputTypeName;
    @ApiModelProperty(value = "接收仓位" , example = "00")
    private String inputBinCode;
    @ApiModelProperty(value = "接收批次信息")
    private BizBatchInfoDTO inputBatchInfoDTO;
    @ApiModelProperty(value = "批次信息")
    private BizBatchInfoDTO batchInfo;
    @ApiModelProperty(value = "行项目状态" , example = "10")
    private Integer itemStatus;
    @ApiModelProperty(value = "填充属性 - 单据行项目状态名称" , example = "草稿")
    private String itemStatusI18n;
    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;
    @ApiModelProperty(value = "特殊库存代码(WBS)")
    private String specStockCode;
    @ApiModelProperty(value = "特殊库存描述(WBS)")
    private String specStockName;
    @ApiModelProperty(value = "是否危险" , example = "1危险")
    private Integer isDanger;
    @ApiModelProperty(value = "是否危险描述" , example = "1危险")
    private String isDangerI18n;
    @ApiModelProperty(value = "存储类型" , example = "T001")
    private String typeCode;
    @ApiModelProperty(value = "仓位" , example = "00")
    private String binCode;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productionDate;

    @ApiModelProperty(value = "填充属性 - 需求人编码" , example = "Admin")
    private String applyUserCode;

    @ApiModelProperty(value = "填充属性 - 需求人描述" , example = "管理员")
    private String applyUserName;

    @ApiModelProperty(value = "填充属性 - 需求人部门编码" , example = "")
    private String applyUserDeptCode;

    @ApiModelProperty(value = "填充属性 - 需求人部门描述" , example = "管理员")
    private String applyUserDeptName;

    @ApiModelProperty(value = "总货架寿命")
    private Integer shelfLifeMax;

    @ApiModelProperty(value = "包装方式（0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵）")
    private Integer packageType;

    @ApiModelProperty(value = "存放方式（0：平放；1：直立存放；2：悬挂；3：朝上存放；4：非关闭状态；5：倒置存放）")
    private Integer depositType;

    @ApiModelProperty(value = "批次id（报废出）")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "batchInfo")
    private Long batchId;

    @ApiModelProperty(value = "批次code" , example = "152758218981377")
    private String batchCode;

    @ApiModelProperty(value = "接收批次id" , example = "100001")
    private String inputBatchCode;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private Integer isNuclearIslandToolRoom;

    @ApiModelProperty(value = "是否为核岛工具间【0：否；1：是】")
    private String isNuclearIslandToolRoomI18n;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "尾差")
    private BigDecimal remainder;

    @ApiModelProperty(value = "总价")
    private BigDecimal amount;

    @ApiModelProperty(value = "是否为主要部件")
    private Integer isMainParts;

    @ApiModelProperty(value = "是否为主要部件")
    private String isMainPartsI18n;

    /* ********************** 扩展字段结束 *************************/

    @ApiModelProperty(value = "主键" , example = "155163729920003")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据类型" , example = "414", required = false )
    private Integer receiptType;

    @ApiModelProperty(value = "单据head表id" , example = "155163729920001")
    private Long receiptHeadId;

    @ApiModelProperty(value = "单据item表id" , example = "152412159541249")
    private Long receiptItemId;

    @ApiModelProperty(value = "前置单据类型" , example = "211", required = false )
    private Integer preReceiptType;

    @ApiModelProperty(value = "前置单据head表id" , example = "155163729920001")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前置单据item表id" , example = "155163729920001")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前置单据bin表id" , example = "155163729920001")
    private Long preReceiptBinId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,unitId,matName", targetAttrName = "matCode,unitId,matName")
    private Long matId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    @RlatAttr(rlatTableName = "dic_factory", sourceAttrName = "ftyCode,ftyName", targetAttrName = "ftyCode,ftyName")
    private Long ftyId;

    @ApiModelProperty(value = "库存地点id" , example = "145725436526593")
    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "locationCode,locationName")
    private Long locationId;

    @RlatAttr(rlatTableName = "dic_stock_location", sourceAttrName = "locationCode,locationName", targetAttrName = "mainLocationCode,mainLocationName")
    @ApiModelProperty(value = "成套库存地点id" , example = "145725436526593")
    private Long mainLocationId;

    @ApiModelProperty(value = "仓库id" , example = "152214349873153")
    @RlatAttr(rlatTableName = "dic_wh", sourceAttrName = "whCode,whName", targetAttrName = "whCode,whName")
    private Long whId;

    @ApiModelProperty(value = "库存数量" , example = "10")
    private BigDecimal stockQty;

    @ApiModelProperty(value = "操作数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "已作业数量" , example = "5")
    private BigDecimal taskQty;

    @ApiModelProperty(value = "特性code" , example = "biz_batch_info.batch_erp,biz_batch_info.spec_stock_code")
    private String specCode;

    @ApiModelProperty(value = "特性值" , example = "111111,1")
    private String specValue;

    @ApiModelProperty(value = "显示特性值" , example = "Q,wbsCode1,0001002957,LK_D,,LK_01")
    private String specDisplayValue;

    @ApiModelProperty(value = "特性类型" , example = "0")
    private Integer specType;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "接收批次id" , example = "100001")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "*", targetAttrName = "inputBatchInfoDTO")
    private Long inputBatchId;

    @ApiModelProperty(value = "接收仓库存储类型id" , example = "152218403667969")
    @RlatAttr(rlatTableName = "dic_wh_storage_type", sourceAttrName = "typeCode,typeName", targetAttrName = "inputTypeCode,inputTypeName")
    private Long inputTypeId;

    @ApiModelProperty(value = "接收仓位id" , example = "152218489651201")
    @RlatAttr(rlatTableName = "dic_wh_storage_bin", sourceAttrName = "binCode", targetAttrName = "inputBinCode")
    private Long inputBinId;

    @ApiModelProperty(value = "接收存储单元id" , example = "152758218981377")
    private Long inputCellId;

    @ApiModelProperty(value = "成套批次id")
    @RlatAttr(rlatTableName = "biz_batch_info", sourceAttrName = "price,remainder,isMainParts", targetAttrName = "price,remainder,isMainParts")
    private Long unitizedBatchId;

    /* ********************** 顺序填充字段开始 *************************/

    @ApiModelProperty(value = "单位编码", name = "unitId", example = "EA", required = true)
    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName,decimalPlace", targetAttrName = "unitCode,unitName,decimalPlace")
    private Long unitId;

    /* ********************** 顺序填充字段结束 *************************/

}
