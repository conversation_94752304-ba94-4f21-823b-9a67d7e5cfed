package com.inossem.wms.common.model.bizdomain.input.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 入库删除入参
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@Data
@ApiModel(value = "入库删除入参", description = "入库单行删除入参")
public class BizReceiptInputDeletePO implements Serializable {

    private static final long serialVersionUID = -5438341708714648431L;

    @ApiModelProperty(value = "入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "headId" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "itemIds" , example = "157511386726401")
    private List<Long> itemIds;

    @ApiModelProperty(value = "batchIds" , example = "157511386726401")
    private List<Long> batchIds;

    @ApiModelProperty(value = "是否全部删除" , example = "false")
    private boolean isDeleteAll;
}
