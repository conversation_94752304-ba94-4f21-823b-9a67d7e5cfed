package com.inossem.wms.common.model.bizdomain.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 库存周转率
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "库存周转率 查询出参传输对象", description = "库存周转率 查询出参")
public class StockTurnoverVO {

    @ApiModelProperty(value = "年度" , example = "2021", required = false)
    private String year;
    @ApiModelProperty(value = "年度-月份" ,example = "2021-04")
    private String month;
    @ApiModelProperty(value = "平局库存金额 万元" , example = "100")
    private BigDecimal avgStockMoney;
    @ApiModelProperty(value = "转储次数" , example = "2")
    private BigDecimal stockTurnover;


}
