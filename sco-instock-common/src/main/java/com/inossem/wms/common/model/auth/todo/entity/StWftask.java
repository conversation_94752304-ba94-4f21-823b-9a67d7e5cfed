package com.inossem.wms.common.model.auth.todo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * ums待办任务
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StWftask对象", description="ums待办任务表")
@TableName("st_wftask")
public class StWftask implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "st_wftaskid", type = IdType.INPUT)
    private Long stWftaskid;

    @ApiModelProperty(value = "操作")
    private String action;

    @ApiModelProperty(value = "环节id")
    private String dataId;

    private String app;

    @ApiModelProperty(value = "流程名称")
    private String appdesc;

    @ApiModelProperty(value = "环节名称")
    private String wfassname;

    @ApiModelProperty(value = "状态")
    private String taskStatus;

    @ApiModelProperty(value = "当前处理人")
    private String assigncode;

    @ApiModelProperty(value = "当前处理人姓名")
    private String assignname;

    @ApiModelProperty(value = "环节开始时间")
    private Date startdate;

    @ApiModelProperty(value = "回调url")
    private String url;

    private Long assignid;

    @ApiModelProperty(value = "描述")
    private String wfdesc;

    @ApiModelProperty(value = "环节结束时间")
    private Date enddate;

    private Date processname;

    @ApiModelProperty(value = "处理意见")
    private String memo;

    private Long ownerid;
    private String ownertable;
    private String yn;
    private Long processrev;
    private String respersonid;
    private Long nodeid;
    private Long wfid;
    @ApiModelProperty(value = "任务类别")
    private String taskType;
}
