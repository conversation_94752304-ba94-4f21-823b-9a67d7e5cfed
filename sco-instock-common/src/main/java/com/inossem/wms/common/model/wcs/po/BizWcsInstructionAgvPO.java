package com.inossem.wms.common.model.wcs.po;

import com.inossem.wms.common.model.common.base.PageCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <p>
 *  查询AGV
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since  2021/5/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BizWcsInstructionAgvPO extends PageCommon {

    @ApiModelProperty(value = "仓库号编码状态" , example = "S800")
    private String whCode;
}
