package com.inossem.wms.common.model.auth.todo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TodolistCountDTO {

	@ApiModelProperty(value = "资源code" , example = "organizational_structure")
	private String resourcesCode;

	@ApiModelProperty(value = "资源名称" , example = "组织架构管理")
	private String resourcesName;

	@ApiModelProperty(value = "待办数量" , example = "5")
	private Integer countNum;

	@ApiModelProperty(value = "单据类型" , example = "211", required = false )
	private Integer receiptType;
	
}
