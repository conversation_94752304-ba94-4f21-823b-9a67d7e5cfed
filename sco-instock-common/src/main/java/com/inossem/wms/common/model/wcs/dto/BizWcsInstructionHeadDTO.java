package com.inossem.wms.common.model.wcs.dto;

import java.io.Serializable;
import java.util.List;

import com.inossem.wms.common.annotation.SonAttr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * WCS指令集抬头表传输对象
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "WCS指令集抬头表传输对象", description = "WCS指令集抬头表传输对象")
public class BizWcsInstructionHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @SonAttr(sonTbName = "biz_wcs_instruction_item", sonTbFkAttrName = "headId")
    private List<BizWcsInstructionItemDTO> itemList;

    @ApiModelProperty(value = "指令头id" , example = "157647881961473")
    private Long id;

    @ApiModelProperty(value = "指令co" , example = "WCS000000079")
    private String instructionCode;

    @ApiModelProperty(value = "biz_receipt_task_req_head   表的ID" , example = "157647879864321")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "任务阶段  理货中 300，上料中301，验证中 302，上架中 303，过账 304 待执行 305，已完成 306  ...." , example = "305")
    private Integer receiptStatus;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称" , example = "草稿")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "托盘ID" , example = "0")
    private Long palletId;

    @ApiModelProperty(value = "是否启用AGV" , example = "0")
    private Integer agvEnable;

    @ApiModelProperty(value = "暂存区id" , example = "156417642266625")
    private Long tempPositionId;

    @ApiModelProperty(value = "创建用户ID" , example = "1")
    private Long createUserId;

    @ApiModelProperty(value = "是否已删除：y/n" , example = "0")
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private String createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private String modifyTime;

    @ApiModelProperty(value = "异常信息" , example = "未认证登录")
    private String exceptionMsg;

    @ApiModelProperty(value = "过账类型 0 sap+instock 1 instock 2 都不改" , example = "0")
    private Integer postType;

    @ApiModelProperty(value = "反向标识  0 正向 1 反向" , example = "0")
    private Integer opposite;


    @ApiModelProperty(value = "托盘数量" , example = "1")
    private String palletQty;

}
