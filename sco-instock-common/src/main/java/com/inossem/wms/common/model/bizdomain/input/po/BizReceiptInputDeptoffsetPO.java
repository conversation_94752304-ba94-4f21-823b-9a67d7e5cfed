package com.inossem.wms.common.model.bizdomain.input.po;

import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 入库核销
 * <AUTHOR>
 * @Date 2021/5/10 19:16
 */
@Data
@ApiModel(value = "入库核销入参对象", description = "入库核销入参对象")
public class BizReceiptInputDeptoffsetPO implements Serializable {

    private static final long serialVersionUID = -1953563058057811002L;

    @ApiModelProperty(value = "入库单号" , example = "RK0001000633")
    private String receiptCode;

    @ApiModelProperty(value = "单据id", example = "151561399500801", required = true)
    private Long headId;

    @ApiModelProperty(value = "核销行项目id集合" , example = "149901631619075")
    private List<Long> itemIds;

    @ApiModelProperty(value = "入库行项目")
    private List<BizReceiptInputItemDTO> itemList;

}
