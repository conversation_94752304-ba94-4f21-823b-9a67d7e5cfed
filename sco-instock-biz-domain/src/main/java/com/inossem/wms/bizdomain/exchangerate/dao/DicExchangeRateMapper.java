package com.inossem.wms.bizdomain.exchangerate.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.masterdata.exchangerate.entity.DicExchangeRate;
import com.inossem.wms.common.model.masterdata.exchangerate.po.DicExchangeRateSearchPO;
import com.inossem.wms.common.model.masterdata.exchangerate.vo.DicExchangeRatePageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 汇率主数据Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Mapper
public interface DicExchangeRateMapper extends WmsBaseMapper<DicExchangeRate> {

    /**
     * 分页查询汇率主数据列表
     *
     * @param page 分页对象
     * @param po   查询参数
     * @return 汇率主数据分页列表
     */
    List<DicExchangeRatePageVO> selectPageVOList(IPage<DicExchangeRatePageVO> page, @Param("po") DicExchangeRateSearchPO po);
}
