package com.inossem.wms.bizdomain.unitized.service.component;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialGroupDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialTypeDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicUnitDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.masterdata.purchasepackage.service.datawrap.DicPurchasePackageDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumDbDefaultValueString;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.register.EnumUnitizedVisualCheck;
import com.inossem.wms.common.enums.register.EnumVisualCheck;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeDeletePO;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.vo.BizReceiptDeliveryNoticeListVo;
import com.inossem.wms.common.model.bizdomain.delivery.vo.BizReceiptDeliveryNoticePreHeadVo;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputWaybillExportVO;
import com.inossem.wms.common.model.bizdomain.maintain.entity.BizReceiptMaintainHead;
import com.inossem.wms.common.model.bizdomain.maintain.vo.BizReceiptMaintainPlanVO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.purchasepackage.entity.DicPurchasePackage;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备到货通知 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-07-05
 */
@Service
public class UnitizedDeliveryNoticeComponent {

    @Autowired
    protected EditCacheService editCacheService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected ApprovalService approvalService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected PurchaseReceiptService purchaseReceiptService;

    @Autowired
    protected DicUnitDataWrap dicUnitDataWrap;

    @Autowired
    protected DicMaterialDataWrap dicMaterialDataWrap;

    @Autowired
    protected DicMaterialTypeDataWrap dicMaterialTypeDataWrap;

    @Autowired
    protected DicMaterialGroupDataWrap dicMaterialGroupDataWrap;

    @Autowired
    protected DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    protected BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;

    @Autowired
    protected BizReceiptDeliveryNoticeHeadDataWrap bizReceiptDeliveryNoticeHeadDataWrap;

    @Autowired
    protected BizReceiptDeliveryNoticeItemDataWrap bizReceiptDeliveryNoticeItemDataWrap;
    @Autowired
    protected BizReceiptRegisterHeadDataWrap bizReceiptRegisterHeadDataWrap;
    @Autowired
    protected BizReceiptRegisterItemDataWrap bizReceiptRegisterItemDataWrap;
    @Autowired
    private DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;
    @Autowired
    private DicPurchasePackageDataWrap dicPurchasePackageDataWrap;

    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @out ctx 出参 {@link MultiResultVO <> ("dicCarTypeDataWrap.list()":"库存地点下拉框")}
     */
    public void getLocationList(BizContext ctx) {
        // 从上下文获取工厂id
        Long ftyId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 查询库存地点下拉
        QueryWrapper<DicStockLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicStockLocation::getFtyId, ftyId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicStockLocationDataWrap.list(queryWrapper)));
    }

    /**
     * 页面初始化: 1、设置送货通知【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"送货通知","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptDeliveryNoticeHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setIsDirectScene(2).setIsRadioactivity(2).setIsSafe(2).setIsDanger(2).setProcurementMethod(1)
                        .setCreateUserDeptName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 送货通知单-分页
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO <BizReceiptDeliveryNoticeHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptDeliveryNoticeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user =  ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptDeliveryNoticeListVo> page = po.getPageObj(BizReceiptDeliveryNoticeListVo.class);
        // 送货通知单-分页
        bizReceiptDeliveryNoticeHeadDataWrap.getDeliveryNoticePageVoUnitized(page, wrapper);
        // 转dto
        List<BizReceiptDeliveryNoticeHeadDTO> dtoList = UtilCollection.toList(page.getRecords(), BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        // 设置送货通知分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizReceiptDeliveryNoticeHead>
     */
    private WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> setQueryWrapper(BizReceiptDeliveryNoticeSearchPO po,CurrentUser user) {
        if (null == po) {
            po = new BizReceiptDeliveryNoticeSearchPO();
        }
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        if(StringUtils.isNotEmpty(po.getChildMatCode())){
            Long matId = dictionaryService.getMatIdByMatCode(po.getChildMatCode());
            po.setChildMatId(matId);
        }
        String deliveryNoticeDesc = po.getDeliveryNoticeDescribe();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptDeliveryNoticeSearchPO::getReceiptCode,
                        BizReceiptDeliveryNoticeHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptDeliveryNoticeSearchPO::getReceiptType,
                        BizReceiptDeliveryNoticeHead.class, po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
                        BizReceiptDeliveryNoticeSearchPO::getReceiptStatus, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptDeliveryNoticeSearchPO::getLocationId,
                       BizReceiptDeliveryNoticeItem.class,locationIdList)
                .eq(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptDeliveryNoticeSearchPO::getReceiptCode,
                        ErpPurchaseReceiptHead.class, po.getPurchaseReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptDeliveryNoticeSearchPO::getMatCode,
                        DicMaterial.class, po.getMatCode())
                .eq(UtilNumber.isNotNull(po.getChildMatId()), BizReceiptDeliveryNoticeSearchPO::getMatId,
                        BizReceiptWaybill.class, po.getChildMatId())
                .between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptDeliveryNoticeSearchPO::getCreateTime,
                        BizReceiptDeliveryNoticeHead.class, po.getStartTime()==null ? po.getStartTime():DateUtil.beginOfDay(po.getStartTime()), po.getEndTime()==null?po.getEndTime():DateUtil.endOfDay(po.getEndTime()))
                .like(UtilString.isNotNullOrEmpty(deliveryNoticeDesc), BizReceiptDeliveryNoticeSearchPO::getDeliveryNoticeDescribe,
                        BizReceiptDeliveryNoticeHead.class, deliveryNoticeDesc)
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptDeliveryNoticeSearchPO::getUserName,
                        SysUser.class, po.getCreateUserName())
                .like(UtilString.isNotNullOrEmpty(po.getPurchasePackageCode()), BizReceiptDeliveryNoticeSearchPO::getPurchasePackageCode, DicPurchasePackage.class, po.getPurchasePackageCode())
                .like(UtilString.isNotNullOrEmpty(po.getPurchasePersonName()), BizReceiptDeliveryNoticeSearchPO::getPurchasePersonName, DicPurchasePackage.class, po.getPurchasePersonName());
        return wrapper;
    }

    /**
     * 送货通知单-详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"送货通知单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取送货通知单
        BizReceiptDeliveryNoticeHead deliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO = UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(deliveryNoticeHeadDTO);
        // 属性填充
        List<BizReceiptDeliveryNoticeItemDTO> itemDTOList = deliveryNoticeHeadDTO.getItemList();
        if (CollectionUtils.isNotEmpty(itemDTOList)) {
            Map<Long, String> itemMatMap = new HashMap<>(itemDTOList.size());
            for (int i = 0; i < itemDTOList.size(); i++) {
                BizReceiptDeliveryNoticeItemDTO itemDTO = itemDTOList.get(i);
                if (i == 0) {
                    deliveryNoticeHeadDTO.setPurchaseUserCode(itemDTO.getPurchaseUserCode())
                            .setPurchaseUserName(itemDTO.getPurchaseUserName())
                            .setContractCode(itemDTO.getContractCode())
                            .setContractName(itemDTO.getContractName())
                            .setSupplierCode(itemDTO.getSupplierCode())
                            .setSupplierName(itemDTO.getSupplierName());
                    deliveryNoticeHeadDTO.setReferReceiptCode(itemDTO.getReferReceiptCode());
                }
                itemMatMap.put(itemDTO.getMatId(), itemDTO.getMatCode());
            }
            List<BizReceiptWaybillDTO> waybillDTOList = deliveryNoticeHeadDTO.getWaybillDTOList();
            if (UtilCollection.isNotEmpty(waybillDTOList)) {
                for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
                    String parentMatCode = waybillDTO.getParentMatCode();
                    if (StringUtils.isBlank(parentMatCode)) {
                        Long parentMatId = waybillDTO.getParentMatId();
                        parentMatCode = itemMatMap.get(parentMatId);
                        if (StringUtils.isNotBlank(parentMatCode)) {
                            waybillDTO.setParentMatCode(parentMatCode);
                        }
                    }
                }
            }
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(deliveryNoticeHeadDTO);
        // 设置送货通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(deliveryNoticeHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 送货通知单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【单据复制】
             return buttonVO.setButtonPrint(true).setButtonRevoke(true);
            // return buttonVO.setButtonCopyReceipt(true);
        }
        return buttonVO;
    }

    /**
     * 保存-校验送货通知入参
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /*// 运单行号不存在
        Map<String, List<BizReceiptDeliveryNoticeItemDTO>> itemMap = po.getItemList().stream().collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getRid));
        List<BizReceiptWaybillDTO> waybillDTOList = po.getWaybillDTOList();
        for (int i = 0; i < waybillDTOList.size(); i++) {
            BizReceiptWaybillDTO waybillDTO = waybillDTOList.get(i);
            String rid = waybillDTO.getDeliveryNoticeItemRid();
            if (!itemMap.containsKey(rid)) {
                throw new WmsException(EnumReturnMsg.UNITIZED_DELIVERY_WAYBILL_RID_MISS, String.valueOf(i+1));
            }
        }*/
    }

    /**
     * 保存-送货通知单
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     * @out ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void saveDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String deliveryNoticeCode = po.getReceiptCode();
        if(UtilNumber.isEmpty(po.getId())){
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        if(UtilCollection.isNotEmpty(po.getWaybillDTOList())){
            DicPurchasePackage dicPurchasePackage = dicPurchasePackageDataWrap.getOne(new QueryWrapper<DicPurchasePackage>().lambda().eq(DicPurchasePackage::getPurchasePackageCode, po.getWaybillDTOList().get(0).getExtend2()));
            if(UtilObject.isNotNull(dicPurchasePackage) && UtilNumber.isNotEmpty(dicPurchasePackage.getId())){
                po.setPurchasePackageId(dicPurchasePackage.getId());
            }
        }
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新送货通知单
            bizReceiptDeliveryNoticeHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteDeliveryNoticeItem(po);
            // 修改器删除waybill
            this.deleteDeliveryNoticeWaybill(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            deliveryNoticeCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DELIVERY.getValue());
            po.setReceiptCode(deliveryNoticeCode);
            bizReceiptDeliveryNoticeHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        for (BizReceiptDeliveryNoticeItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        bizReceiptDeliveryNoticeItemDataWrap.saveBatchDto(po.getItemList());
        /* ********************** item处理结束 *************************/
        /* ********************** waybill处理开始 *************************/
        // 行项目按行号分组
        Map<String, List<BizReceiptDeliveryNoticeItemDTO>> itemMap = po.getItemList().stream().collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getRid));
        Map<String, Long> locationMap = new HashMap<>();
        Map<String, Long> unitMap = new HashMap<>();
        Long ftyId = po.getItemList().get(0).getFtyId();
        String ftyCode = po.getItemList().get(0).getFtyCode();
        for (BizReceiptWaybillDTO waybillDTO : po.getWaybillDTOList()) {
            waybillDTO.setDeliveryNoticeHeadId(po.getId());
            BizReceiptDeliveryNoticeItemDTO itemDTO = itemMap.get(waybillDTO.getDeliveryNoticeItemRid()).get(0);
            waybillDTO.setDeliveryNoticeItemId(itemDTO.getId());
            waybillDTO.setParentMatId(itemDTO.getMatId());
            waybillDTO.setWhId(itemDTO.getWhId());
            waybillDTO.setFtyId(ftyId);
            // 库存地点
            String locationCode = waybillDTO.getLocationCode();
            if (!locationMap.isEmpty() && locationMap.containsKey(locationCode)) {
                Long locationId = locationMap.get(locationCode);
                waybillDTO.setLocationId(locationId);
            } else {
                Long locationId = dictionaryService.getLocationIdCacheByCode(ftyCode, locationCode);
                waybillDTO.setLocationId(locationId);
                locationMap.put(locationCode, locationId);
            }
            // 单位
            String unitName = waybillDTO.getUnitName();
            if (!unitMap.isEmpty() && unitMap.containsKey(unitName)) {
                Long unitId = unitMap.get(unitName);
                waybillDTO.setUnitId(unitId);
            } else {
                Long unitId = dictionaryService.getUnitIdCacheByName(unitName);
                waybillDTO.setUnitId(unitId);
                unitMap.put(unitName, unitId);
            }
        }
        bizReceiptWaybillDataWrap.saveBatchDto(po.getWaybillDTOList());
        /* ********************** waybill处理结束 *************************/
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, deliveryNoticeCode);
    }

    /**
     * 删除送货通知单行项目
     *
     * @param headDTO 送货通知
     */
    private void deleteDeliveryNoticeItem(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        UpdateWrapper<BizReceiptDeliveryNoticeItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptDeliveryNoticeItem::getHeadId, headDTO.getId());
        bizReceiptDeliveryNoticeItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 删除送货通知单运单
     *
     * @param headDTO 送货通知
     */
    private void deleteDeliveryNoticeWaybill(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        UpdateWrapper<BizReceiptWaybill> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptWaybill::getDeliveryNoticeHeadId, headDTO.getId());
        bizReceiptWaybillDataWrap.physicalDelete(wrapper);
    }

    /**
     * 提交-校验送货通知入参
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验入参 ******** */
        this.checkSaveData(ctx);
        // 校验运单计量单位
        List<BizReceiptWaybillDTO> waybillDTOList = headDTO.getWaybillDTOList();
        if (CollectionUtils.isNotEmpty(waybillDTOList)) {
            String extend46 = waybillDTOList.get(0).getExtend46();
            Set<String> unitSet = new HashSet<>();
            for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
                // 单位
                String unitName = waybillDTO.getUnitName();
                if (StringUtils.isBlank(unitName)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_UNITCODE_CAN_NOT_BE_EMPTY);
                }
                if (unitSet.isEmpty() || !unitSet.contains(unitName)) {
                    Long unitId = dictionaryService.getUnitIdCacheByName(unitName);
                    if (UtilNumber.isEmpty(unitId)) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_UNITCODE_CAN_NOT_BE_EMPTY);
                    }
                    unitSet.add(unitName);
                }
                /* ******** 校验详情页“运单信息”中（模板AH列）“制造厂名称”是否唯一，若不唯一，则提示“制造厂名称称必须一致”； ******** */
                if (!extend46.equals(waybillDTO.getExtend46())){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_WAYBILL_MANUFACTURE_MUST_BE_SAME);
                }
            }
        }
        /* ******** 校验数量 ******** */
        this.checkItemQty(headDTO);
    }
    /**
     * 校验行项目提交数量
     *
     * @param headDTO 送货通知单
     */
    private void checkItemQty(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        // 装载qty已超过送货数量的行项目
        List<String> errorQtyExceedList = new ArrayList<>();
        headDTO.getItemList().forEach(itemDTO -> {
            if (itemDTO.getQty().compareTo(itemDTO.getCanDeliveryQty()) > 0) {
                errorQtyExceedList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorQtyExceedList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEED_DELIVERY_QTY, errorQtyExceedList.toString());
        }
    }

    /**
     * 提交送货通知
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交送货通知"}
     * @out ctx 出参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交的送货通知")}
     */
    public void submitDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存送货通知
        this.saveDeliveryNotice(ctx);
        // 计算主部件单价 设置行项目生产日期
        this.savePrice(ctx);
        // 创建物料主数据
        this.saveMaterial(ctx);
        // 更新送货通知head、item状态 - 已完成
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 计算主部件单价 设置行项目生产日期
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交送货通知"}
     * @out ctx 出参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交的送货通知")}
     */
    public void savePrice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 运单按行项目id分组
        Map<Long, List<BizReceiptWaybillDTO>> waybillMap = po.getWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getDeliveryNoticeItemId));
        for(BizReceiptDeliveryNoticeItemDTO itemDTO : po.getItemList()) {
            // 生产日期取对应运单中的最早日期
            itemDTO.setProductDate(waybillMap.get(itemDTO.getId()).stream().min(Comparator.comparing(BizReceiptWaybillDTO::getProductDate)).get().getProductDate());
        }
        // 更新行项目信息
        bizReceiptDeliveryNoticeItemDataWrap.updateBatchDtoById(po.getItemList());
    }

    /**
     * 创建物料主数据
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交送货通知"}
     * @out ctx 出参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交的送货通知")}
     */
    public void saveMaterial(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptDeliveryNoticeItemDTO> itemDTOList = po.getItemList();
        Integer trueFlag = EnumRealYn.TRUE.getIntValue();
        for (BizReceiptDeliveryNoticeItemDTO itemDTO : itemDTOList) {
            Long ftyId = itemDTO.getFtyId();
            Long matId = itemDTO.getMatId();
            dicMaterialFactoryDataWrap.updateUnitizedFlag(trueFlag, ftyId, matId);
        }
        // 物料主数据
        List<DicMaterialDTO> materialDTOList = new ArrayList<>();
        // edit by ChangBaoLong 已确认成套设备创建物料主数据时，默认设置物料类型为：CTSBZBJ-成套设备子部件；物料组为：1989-成套设备。额外需新增物料类型和物料组主数据
        // 物料类型
        // Set<DicMaterialType> materialTypeSet = new HashSet<>();
        // 物料组
        // Set<DicMaterialGroup> materialGroupSet = new HashSet<>();
        Long matTypeId = dictionaryService.getMatTypeIdByMatTypeCode(Const.MATERIAL_TYPE_CODE_CTSBZBJ);
        Long matGroupId = dictionaryService.getMatGroupIdByMatGroupCode(Const.MATERIAL_GROUP_CODE_1989);
        for (BizReceiptWaybillDTO waybillDTO : po.getWaybillDTOList()) {
            DicMaterialDTO materialDTO = new DicMaterialDTO();
            waybillDTO.setMatCode(bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_MATERIAL_CODE.getValue())); // 物料编码
            materialDTO.setMatCode(waybillDTO.getMatCode()); // 物料编码
            materialDTO.setMatName(waybillDTO.getMatName()); // 物料描述
            materialDTO.setParentMatId(waybillDTO.getParentMatId());
            materialDTO.setUnitId(waybillDTO.getUnitId()); // 计量单位id
            materialDTO.setMatTypeId(matTypeId); // 物料类型id
            materialDTO.setMatGroupId(matGroupId); // 物料组id
            // 物料类型
//            if(UtilNumber.isEmpty(dictionaryService.getMatTypeIdByMatTypeName(waybillDTO.getMatGroupName()))) {
//                DicMaterialType materialType = new DicMaterialType();
//                materialType.setMatTypeCode(waybillDTO.getMatGroupName()); // 物料类型编码
//                materialType.setMatTypeName(waybillDTO.getMatGroupName()); // 物料类型描述
//                materialTypeSet.add(materialType);
//            }
            // 物料组
//            if(UtilNumber.isEmpty(dictionaryService.getMatGroupIdByMatGroupName(waybillDTO.getMatGroupName()))) {
//                DicMaterialGroup materialGroup = new DicMaterialGroup();
//                materialGroup.setMatGroupCode(waybillDTO.getMatGroupName()); // 物料组编码
//                materialGroup.setMatGroupName(waybillDTO.getMatGroupName()); // 物料组描述
//                materialGroupSet.add(materialGroup);
//            }
            materialDTO.setLength(BigDecimal.ZERO); // 长度
            materialDTO.setWidth(BigDecimal.ZERO); // 宽度
            materialDTO.setHeight(BigDecimal.ZERO); // 高度
            materialDTO.setUnitLength(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_LENGTH.getValue()); // 长度/宽度/高度的单位
            materialDTO.setGrossWeight(BigDecimal.ZERO); // 毛重
            materialDTO.setNetWeight(BigDecimal.ZERO); // 净重
            materialDTO.setUnitWeight(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_WEIGHT.getValue()); // 重量的单位
            materialDTO.setWeightTolerance(BigDecimal.ZERO); // 重量容差
            materialDTO.setVolume(BigDecimal.ZERO); // 体积
            materialDTO.setUnitVolume(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_VOLUME.getValue()); // 体积的单位
            materialDTO.setShelfLife(0); // 保质期
            materialDTO.setUnitShelfLife(EnumDbDefaultValueString.DIC_MATERIAL_UNIT_SHELF_LIFE.getValue()); // 保质期的单位
            materialDTO.setIsShelfLife(EnumRealYn.FALSE.getIntValue()); // 是否启用保质期
            materialDTO.setIsFreeze(EnumRealYn.FALSE.getIntValue()); // 是否冻结
            materialDTO.setIsDangerous(EnumRealYn.FALSE.getIntValue()); // 是否危险物料
//            materialDTO.setShelfLifeMax(waybillDTO.getShelfLifeMax()); // 总货架寿命
//            materialDTO.setShelfLifeMin(waybillDTO.getShelfLifeMin()); // 最小货架寿命
            String extend60 = waybillDTO.getExtend60();
            if (StringUtils.isNotBlank(extend60)) {
                materialDTO.setShelfLifeMax(Integer.parseInt(extend60));
            }
            materialDTO.setShelfLifeMin(0);
            materialDTO.setPackageType(waybillDTO.getPackageType()); // 包装方式
//            materialDTO.setDepositType(waybillDTO.getDepositType()); // 存放方式
            materialDTO.setDepositType(99); // 默认不能为0，故用99表示默认不存在
            materialDTO.setMainFlag(EnumRealYn.FALSE.getIntValue());
            materialDTO.setIsCtCode(EnumRealYn.TRUE.getIntValue());
            materialDTOList.add(materialDTO);
        }
//        if(UtilCollection.isNotEmpty(materialTypeSet)) {
//            // 保存物料类型
//            dicMaterialTypeDataWrap.saveBatch(materialTypeSet);
//            // 刷新缓存
//            editCacheService.refreshDicMaterialTypeCache();
//        }
//        if(UtilCollection.isNotEmpty(materialGroupSet)) {
//            // 保存物料组
//            dicMaterialGroupDataWrap.saveBatch(materialGroupSet);
//            // 刷新缓存
//            editCacheService.refreshDicMaterialGroupCache();
//        }
        // 保存物料
        dicMaterialDataWrap.saveBatchDto(materialDTOList);
        // 刷新缓存
        editCacheService.refreshMatCacheByMatIdList(materialDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()));
        // 回填物料id
        Map<String, Long> matMap = materialDTOList.stream().collect(Collectors.toMap(o -> o.getMatCode(), o -> o.getId()));
        po.getWaybillDTOList().forEach(p -> p.setMatId(matMap.get(p.getMatCode())));
        // 更新运单信息
        bizReceiptWaybillDataWrap.updateMatIdById(po.getWaybillDTOList());
    }

    /**
     * 删除前校验
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知单删除入参"}
     * @out ctx 出参 {@link BizReceiptDeliveryNoticeDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取送货通知信息
        BizReceiptDeliveryNoticeHead deliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO =
                UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(deliveryNoticeHeadDTO);
        /* ******** 校验送货通知单head ******** */
        if (UtilObject.isNotNull(deliveryNoticeHeadDTO)) {
            if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue()
                    .equals(deliveryNoticeHeadDTO.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(deliveryNoticeHeadDTO.getItemList().stream().map(BizReceiptDeliveryNoticeItemDTO::getId)
                    .collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 撤销前校验
     */
    public void checkRevokeDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取送货通知信息
        BizReceiptDeliveryNoticeHead deliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO =
                UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(deliveryNoticeHeadDTO);
        /* ******** 校验送货通知单head ******** */
        if (UtilObject.isNotNull(deliveryNoticeHeadDTO)) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(deliveryNoticeHeadDTO.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
    }

    /**
     * 撤销送货通知单
     */
    public void revokeDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptRegisterItem> list = bizReceiptRegisterItemDataWrap.list(new QueryWrapper<BizReceiptRegisterItem>().lambda().eq(BizReceiptRegisterItem::getPreReceiptHeadId, po.getHeadId()));
        list.forEach(o->{
            if(!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(o.getItemStatus())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY);
            }
        });
        Set<Long> registerHeadIdList = list.stream().map(o->o.getHeadId()).collect(Collectors.toSet());
        Set<Long> registerItemIdList = list.stream().map(o->o.getId()).collect(Collectors.toSet());
        bizReceiptRegisterHeadDataWrap.multiPhysicalDeleteByIdList(registerHeadIdList);
        bizReceiptRegisterItemDataWrap.multiPhysicalDeleteByIdList(registerItemIdList);
        // 修改为草稿
        bizReceiptDeliveryNoticeHeadDataWrap.update(new UpdateWrapper<BizReceiptDeliveryNoticeHead>().lambda()
                .set(BizReceiptDeliveryNoticeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                .eq(BizReceiptDeliveryNoticeHead::getId, po.getHeadId()));
        bizReceiptDeliveryNoticeItemDataWrap.update(new UpdateWrapper<BizReceiptDeliveryNoticeItem>().lambda()
                .set(BizReceiptDeliveryNoticeItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                .eq(BizReceiptDeliveryNoticeItem::getHeadId, po.getHeadId()));
        // 保存操作日志 - 撤销
        receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE_AUTO, "",
                ctx.getCurrentUser().getId());
    }

    /**
     * 删除送货通知单
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知单删除入参"}
     */
    public void deleteDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除送货通知 ******** */
        if (po.isDeleteAll()) {
            // 删除送货通知head
            bizReceiptDeliveryNoticeHeadDataWrap.physicalDeleteById(po.getHeadId());
            // 删除送货通知item
            UpdateWrapper<BizReceiptDeliveryNoticeItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptDeliveryNoticeItem::getHeadId, po.getHeadId());
            bizReceiptDeliveryNoticeItemDataWrap.physicalDelete(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                    EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                    ctx.getCurrentUser().getId());
        } else {
            // 删除送货通知item
            bizReceiptDeliveryNoticeItemDataWrap.multiPhysicalDeleteByIdList(po.getItemIds());
        }
    }

    /**
     * 添加物料查询-基于采购订单
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO < BizReceiptDeliveryNoticePreHeadVo > :"送货通知单前续单据结果集"}
     */
    public void purchaseReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (EnumReceiptType.PURCHASE_RECEIPT.getValue().equals(po.getPreReceiptType())) {
            // 装载返回对象
            MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> returnVo = new MultiResultVO<>();
            // 调用SAP查询采购订单
            List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList = purchaseReceiptService
                    .getErpPurchaseReceiptItemList(po.setIsReturnFlag(EnumRealYn.FALSE.getIntValue()), user);
            // 上下文返回参数
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, this.purchaseDataFormat(returnVo, purchaseReceiptItemVoList));
        }
    }

    /**
     * 采购订单查询参转换
     *
     * @param returnVo 要设置的返回数据
     * @param purchaseReceiptItemVoList 采购订单信息
     * @return 物料信息
     */
    private MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> purchaseDataFormat(
            MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> returnVo,
            List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(purchaseReceiptItemVoList)) {
            // 装载返回数据
            List<BizReceiptDeliveryNoticePreHeadVo> headInfoList = new ArrayList<>();
            // 根据采购订单号分组
            LinkedHashMap<String, List<ErpPurchaseReceiptItemDTO>> purchaseMap =
                    purchaseReceiptItemVoList.stream().collect(Collectors
                            .groupingBy(ErpPurchaseReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
            Set<String> keys = purchaseMap.keySet();
            for (String key : keys) {
                // 装载返回数据head
                BizReceiptDeliveryNoticePreHeadVo headInfo = new BizReceiptDeliveryNoticePreHeadVo();
                // 装载返回数据item
                List<BizReceiptDeliveryNoticeItemDTO> itemInfoList = new ArrayList<>();
                List<ErpPurchaseReceiptItemDTO> purchaseItemList = purchaseMap.get(key);
                for (int i = 0; i < purchaseItemList.size(); i++) {
                    ErpPurchaseReceiptItemDTO purchaseDTO = purchaseItemList.get(i);
                    BizReceiptDeliveryNoticeItemDTO itemInfo = new BizReceiptDeliveryNoticeItemDTO();
                    /* ******** 设置head列字段 ******** */
                    if (i == 0) {
                        headInfo = UtilBean.newInstance(purchaseDTO, headInfo.getClass());
                        headInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                        headInfo.setPurchaseUserCode(purchaseDTO.getPurchaseUserCode());
                        headInfo.setPurchaseUserName(purchaseDTO.getPurchaseUserName());
                        headInfo.setContractCode(purchaseDTO.getContractCode());
                        headInfo.setContractName(purchaseDTO.getContractName());
                        headInfo.setSupplierCode(purchaseDTO.getSupplierCode());
                        headInfo.setSupplierName(purchaseDTO.getSupplierName());
                    }
                    /* ******** 设置item列字段 ******** */
                    itemInfo = UtilBean.newInstance(purchaseDTO, itemInfo.getClass());
                    itemInfo.setId(null);
                    itemInfo.setHeadId(null);
                    itemInfo.setRid(null);
                    itemInfo.setReceiptCode(Const.STRING_EMPTY);
                    itemInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                    itemInfo.setReferReceiptRid(purchaseDTO.getRid());
                    itemInfo.setPreReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setPreReceiptItemId(purchaseDTO.getId());
                    itemInfo.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setPreReceiptQty(purchaseDTO.getReceiptQty());
                    itemInfo.setReferReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setReferReceiptItemId(purchaseDTO.getId());
                    itemInfo.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                    // 获取缓存中仓库信息
                    itemInfo.setWhId(UtilObject.isNotNull(dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()))
                            ? dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()).getWhId() : 0L);
                    itemInfoList.add(itemInfo);
                }
                headInfo.setChildren(itemInfoList);
                headInfoList.add(headInfo);
            }
            returnVo.setResultList(headInfoList);
        }
        return returnVo;
    }

    /**
     * 生成到货登记单
     *
     * @param ctx - 到货通知单提交表单内容
     */
    public void genArrivalRegister(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 外观检查默认合格
        po.getWaybillDTOList().forEach(p -> p.setVisualCheck(EnumVisualCheck.QUALIFIED.getValue()).setUnitizedVisualCheck(EnumUnitizedVisualCheck.LH.getValue()));
        // 组装参数
        BizReceiptRegisterHeadDTO headDTO = new BizReceiptRegisterHeadDTO();
        List<BizReceiptRegisterItemDTO> itemDTOList = new ArrayList<>();
        // 【压水堆-WEB】到货登记，“是否执行开箱计划”默认为“是”
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_ARRIVAL_REGISTER.getValue()).setIsSafe(po.getIsSafe()).setIsUnbox(1)
                .setWaybillDTOList(po.getWaybillDTOList()).setPurchasePackageId(po.getPurchasePackageId()).setUnit(po.getUnit());
        headDTO.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());;
        for (BizReceiptDeliveryNoticeItemDTO itemDTO : po.getItemList()) {
            BizReceiptRegisterItemDTO receiptRegisterItemDTO = UtilBean.newInstance(itemDTO, BizReceiptRegisterItemDTO.class);
            receiptRegisterItemDTO.setPreReceiptHeadId(po.getId());
            receiptRegisterItemDTO.setPreReceiptItemId(itemDTO.getId());
            receiptRegisterItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue());
            receiptRegisterItemDTO.setPreReceiptQty(itemDTO.getQty());
            itemDTOList.add(receiptRegisterItemDTO);
        }
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxRegister = new BizContext();
        ctxRegister.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxRegister.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成到货登记单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_ARRIVAL_REGISTER_STOCK, ctxRegister);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
        Long ftyId=headDTO.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

    }

    /**
     * 准备更新送货通知状态
     *
     * @param headDTO 送货通知head
     * @param itemDTOList 送货通知item
     */
    public void updateStatus(BizReceiptDeliveryNoticeHeadDTO headDTO, List<BizReceiptDeliveryNoticeItemDTO> itemDTOList, Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新送货通知head状态
     *
     * @param headDto 送货通知head
     */
    private void updateHead(BizReceiptDeliveryNoticeHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptDeliveryNoticeHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新送货通知item状态
     *
     * @param itemDtoList 送货通知item
     */
    private void updateItem(List<BizReceiptDeliveryNoticeItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptDeliveryNoticeItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 开启操单据流
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启单据流")}
     */
    public void setExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"到货通知","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"到货通知及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "要保存操作日志的送货通知单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的送货通知单
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "要保存附件的送货通知单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存送货通知单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue(), user.getId());
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "要保存单据流的送货通知单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptDeliveryNoticeItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(headDTO.getReceiptType());
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(item.getReferReceiptType());
            dto.setPreReceiptHeadId(item.getReferReceiptHeadId());
            dto.setPreReceiptItemId(item.getReferReceiptItemId());
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 删除单据流
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知删除入参"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据流
            receiptRelationService.deleteReceiptTree(EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue(), po.getHeadId());
        }
    }

    /**
     * 删除单据附件
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知删除入参"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据附件
            receiptAttachmentService.deleteBizReceiptAttachment(po.getHeadId(),
                    EnumReceiptType.UNITIZED_DELIVERY_NOTICE.getValue());
        }
    }

    /**
     * 根据单据流查询返回成套设备到货通知Head
     */
    public BizReceiptDeliveryNoticeHead queryDeliveryNoticeHead(Integer receiptType, Long headId) {
        Long deliveryNoticeHeadId = receiptRelationService.queryDeliveryNoticeHeadId(receiptType, headId);
        return bizReceiptDeliveryNoticeHeadDataWrap.getById(deliveryNoticeHeadId);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 导出
     */
    public void export(BizContext ctx) {
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("运单"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        List<BizReceiptInputWaybillExportVO> exportVOList = UtilCollection.toList(po.getWaybillDTOList(), BizReceiptInputWaybillExportVO.class);
        exportVOList.forEach(o->{
            if(o.getIsMainParts() == 0){
                o.setIsMainPartsStr("否");
            } else if(o.getIsMainParts() == 1){
                o.setIsMainPartsStr("是");
            }
            // 储存级别
            if (o.getLocationCode().equals("W001")) {
                o.setLocationCode("A");
            } else if (o.getLocationCode().equals("W002")) {
                o.setLocationCode("B");
            } else if (o.getLocationCode().equals("W003")) {
                o.setLocationCode("C");
            } else if (o.getLocationCode().equals("W004")) {
                o.setLocationCode("D");
            } else if (o.getLocationCode().equals("W005")) {
                o.setLocationCode("E");
            }
        });
        UtilExcel.writeExcel(BizReceiptInputWaybillExportVO.class, exportVOList, bizCommonFile);
        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

}
