package com.inossem.wms.bizdomain.apply.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptToolBorrowApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptToolBorrowApplyItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.rel.entity.SysUserDeptOfficeRel;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptToolBorrowApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptToolBorrowApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptToolBorrowApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptToolBorrowApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptMatStockQueryPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptToolBorrowApplySearchPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputSignatureGraphDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.file.img.entity.BizCommonImage;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.file.service.datawrap.BizCommonImageDataWrap;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/30 15:06
 * @desc ToolBorrowApplyComponent
 */
@Service
@Slf4j
public class ToolBorrowApplyComponent {

    @Autowired
    private BizReceiptToolBorrowApplyHeadDataWrap bizReceiptToolBorrowApplyHeadDataWrap;

    @Autowired
    private BizReceiptToolBorrowApplyItemDataWrap bizReceiptToolBorrowApplyItemDataWrap;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private UserService userService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    private BizLabelDataDataWrap bizLabelDataDataWrap;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BizCommonImageDataWrap commonImageDataWrap;

    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;

    @Autowired
    private BizLabelReceiptRelDataWrap bizLabelReceiptRelDataWrap;

    /**
     * 页面初始化:
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"专用工器具借用","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        // 页面初始化设置
        BizResultVO<BizReceiptToolBorrowApplyHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptToolBorrowApplyHeadDTO().setReceiptType(EnumReceiptType.TOOL_BORROW_APPLY.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        if (UtilCollection.isNotEmpty(ctx.getCurrentUser().getUserDeptList())) {
            resultVO.getHead().setDeptId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptId())
                    .setCreateUserDeptName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptName());
        }
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptToolBorrowApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptToolBorrowApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptToolBorrowApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.TOOL_BORROW_APPLY.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptToolBorrowApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 专用工器具借用单-分页
     *
     * @in ctx 入参 {@link BizReceiptToolBorrowApplySearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO <BizReceiptToolBorrowApplyHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptToolBorrowApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptToolBorrowApplySearchPO> wrapper = this.setQueryWrapper(po, user);
        // 分页处理
        IPage<BizReceiptToolBorrowApplyHeadDTO> page = po.getPageObj(BizReceiptToolBorrowApplyHeadDTO.class);
        page.orders().forEach(obj -> {
            // 排序特殊处理
            obj.setColumn(obj.getColumn().replace("create_user_dept_name", "biz_receipt_tool_borrow_apply_head.dept_id"));
        });
        // 专用工器具借用单-分页
        bizReceiptToolBorrowApplyHeadDataWrap.selectPage(page, wrapper);
        // 转dto
        List<BizReceiptToolBorrowApplyHeadDTO> dtoList =
                UtilCollection.toList(page.getRecords(), BizReceiptToolBorrowApplyHeadDTO.class);
        // 填充关联属性
        dataFillService.fillAttr(dtoList);
        dtoList.forEach(dto -> {
            if (dto.getReceiptType().equals(EnumReceiptType.TOOL_BORROW_APPLY.getValue())) {
                dto.setCreateUserDeptName(dto.getDeptName());
            }
            if (dto.getReceiptType().equals(EnumReceiptType.TOOL_RETURN.getValue())) {
                Long preReceiptHeadId = dto.getItemList().get(0).getPreReceiptHeadId();
                BizReceiptToolBorrowApplyHead headDataWrapById = bizReceiptToolBorrowApplyHeadDataWrap.getById(preReceiptHeadId);
                dto.setPreReceiptHeadId(preReceiptHeadId);
                dto.setPreReceiptHeadCode(headDataWrapById.getReceiptCode());
                dto.setCreateUserDeptName(dto.getDeptName());
                dto.setBorrowTime(headDataWrapById.getCreateTime());
            }
        });
        // 设置专用工器具借用分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 专用工器具借用单-详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"专用工器具借用单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取专用工器具借用单
        BizReceiptToolBorrowApplyHead head = bizReceiptToolBorrowApplyHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptToolBorrowApplyHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptToolBorrowApplyHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置部门
        headDTO.setCreateUserDeptName(headDTO.getDeptName());
        // 填充签字信息
        this.fillSignature(headDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);

        // 设置专用工器具借用申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    private void fillSignature(BizReceiptToolBorrowApplyHeadDTO headDTO) {
        if (headDTO.getReceiptType().equals(EnumReceiptType.TOOL_RETURN.getValue())
                && headDTO.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            BizReceiptOutputSignatureGraphDTO createUserSignature = new BizReceiptOutputSignatureGraphDTO();
            BizReceiptOutputSignatureGraphDTO submitUserSignature = new BizReceiptOutputSignatureGraphDTO();
            BizReceiptOutputSignatureGraphDTO leaderSignature = new BizReceiptOutputSignatureGraphDTO();
            if (UtilNumber.isNotEmpty(headDTO.getSubmitUserId())) {
                SysUserDTO userDTO = userService.getSysUserInfoById(headDTO.getSubmitUserId());
                if (Objects.nonNull(userDTO)) {
                    Long commonImgId = userDTO.getCommonImgId();
                    BizCommonImage commonImage = commonImageDataWrap.getById(commonImgId);
                    if (UtilObject.isNotEmpty(commonImage)) {
                        submitUserSignature.setSignatureType(1);
                        submitUserSignature.setSignatureGraph(commonImage.getImgBase64());
                    }
                }
            }

            BizReceiptToolBorrowApplyHead borrowApplyHead = bizReceiptToolBorrowApplyHeadDataWrap.getById(headDTO.getItemList().get(0).getPreReceiptHeadId());
            headDTO.setPreReceiptHeadCode(borrowApplyHead.getReceiptCode());
            BizReceiptToolBorrowApplyHeadDTO bizReceiptToolBorrowApplyHeadDTO = UtilBean.newInstance(borrowApplyHead, BizReceiptToolBorrowApplyHeadDTO.class);
            dataFillService.fillAttr(bizReceiptToolBorrowApplyHeadDTO);
            if (UtilNumber.isNotEmpty(headDTO.getCreateUserId())) {
                SysUserDTO userDTO = userService.getSysUserInfoById(headDTO.getCreateUserId());
                if (Objects.nonNull(userDTO)) {
                    Long commonImgId = userDTO.getCommonImgId();
                    BizCommonImage commonImage = commonImageDataWrap.getById(commonImgId);
                    if (UtilObject.isNotEmpty(commonImage)) {
                        createUserSignature.setSignatureType(1);
                        createUserSignature.setSignatureGraph(commonImage.getImgBase64());
                    }
                }
            }
            List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(bizReceiptToolBorrowApplyHeadDTO.getReceiptCode());
            BizApproveRecordDTO bizApproveRecordDTO = approveList.get(1);
            if (UtilObject.isNotEmpty(bizApproveRecordDTO)) {
                leaderSignature.setSignatureType(1);
                leaderSignature.setSignatureGraph(bizApproveRecordDTO.getAutographData());
            }
            headDTO.setLeaderSignature(leaderSignature);
            headDTO.setSubmitUserSignature(submitUserSignature);
            headDTO.setCreateUserSignature(createUserSignature);
        }
    }

    /**
     * 按钮组
     *
     * @param headDTO 专用工器具借用单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptToolBorrowApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (headDTO.getReceiptType().equals(EnumReceiptType.TOOL_BORROW_APPLY.getValue()) && EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }

        if (headDTO.getReceiptType().equals(EnumReceiptType.TOOL_RETURN.getValue()) && EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSubmit(true);
        }
        // 已完成的归还单可以打印标签
        if (headDTO.getReceiptType().equals(EnumReceiptType.TOOL_RETURN.getValue())
                && headDTO.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }


    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizReceiptToolBorrowApplyHead>
     */
    private WmsQueryWrapper<BizReceiptToolBorrowApplySearchPO> setQueryWrapper(BizReceiptToolBorrowApplySearchPO po, CurrentUser user) {
        if (null == po) {
            po = new BizReceiptToolBorrowApplySearchPO();
        }

        String applyDescribe = po.getDescription();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptToolBorrowApplySearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptToolBorrowApplySearchPO::getReceiptCode,
                        BizReceiptToolBorrowApplyHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptToolBorrowApplySearchPO::getReceiptType,
                        BizReceiptToolBorrowApplyHead.class, po.getReceiptType())
                .eq(UtilNumber.isNotEmpty(po.getDeptId()), BizReceiptToolBorrowApplySearchPO::getDeptId, SysUserDeptOfficeRel.class, po.getDeptId())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
                        BizReceiptToolBorrowApplySearchPO::getReceiptStatus, po.getReceiptStatusList())
                .between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptToolBorrowApplySearchPO::getCreateTime,
                        BizReceiptToolBorrowApplyHead.class, po.getStartTime(), po.getEndTime())
                .between((UtilObject.isNotNull(po.getEstimatedReturnTimeStart()) && UtilObject.isNotNull(po.getEstimatedReturnTimeEnd())), BizReceiptToolBorrowApplySearchPO::getEstimatedReturnTime,
                        BizReceiptToolBorrowApplyHead.class, po.getEstimatedReturnTimeStart(), po.getEstimatedReturnTimeEnd())
                .like(UtilString.isNotNullOrEmpty(applyDescribe), BizReceiptToolBorrowApplySearchPO::getDescription, BizReceiptToolBorrowApplyHead.class, applyDescribe)
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptToolBorrowApplySearchPO::getUserName,
                        SysUser.class, po.getCreateUserName());
        if (UtilString.isNotNullOrEmpty(po.getCreateUserDeptName())) {
            List<DicDept> deptList = dicDeptDataWrap.list(new QueryWrapper<DicDept>().lambda().likeLeft(DicDept::getDeptName, po.getCreateUserDeptName()));
            List<Long> ids = deptList.stream().map(DicDept::getId).collect(Collectors.toList());
            wrapper.lambda().in(UtilCollection.isNotEmpty(ids), BizReceiptToolBorrowApplySearchPO::getDeptId, SysUserDeptOfficeRel.class, ids);
        }
        return wrapper;
    }


    /**
     * 保存-校验入参
     *
     * @in ctx 入参 {@link BizReceiptToolBorrowApplyHeadDTO : "专用工器具借用申请"}
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptToolBorrowApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        //
        if (UtilObject.isEmpty(po.getEstimatedReturnTime()) || UtilObject.isEmpty(po.getDescription()) || UtilObject.isEmpty(po.getBorrowExplain())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        po.getItemList().forEach(itemDTO -> {
            if (UtilNumber.isEmpty(itemDTO.getQty())) {
                throw new WmsException("数量不能为空");
            }
        });

    }

    /**
     * 保存-校验入参
     *
     * @in ctx 入参 {@link BizReceiptToolBorrowApplyHeadDTO : "专用工器具借用申请"}
     */
    public void checkReturn(BizContext ctx) {
        // 入参上下文
        BizReceiptToolBorrowApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        for (BizReceiptToolBorrowApplyItemDTO itemDTO : po.getItemList()) {
            if (itemDTO.getQty().compareTo(itemDTO.getPreReceiptQty().subtract(itemDTO.getReturnedQty())) > 0) {
                throw new WmsException("本次归还数量不能大于借出数量-已归还数量");
            }
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), itemDTO.getTargetTypeCode(), itemDTO.getTargetBinCode());
            if (UtilNumber.isEmpty(binId)) {
                throw new WmsException("目标仓位不存在");
            }
            itemDTO.setTargetBinId(binId);
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDTO.getAssembleDTOList()) {
                bizReceiptAssembleDTO.setQty(itemDTO.getQty());
            }
        }


    }

    /**
     * 提交归还单
     *
     * @param ctx
     */
    public void saveReturn(BizContext ctx) {
        // 入参上下文
        BizReceiptToolBorrowApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        BizReceiptToolBorrowApplyHead head = bizReceiptToolBorrowApplyHeadDataWrap.getById(po.getId());
        BizReceiptToolBorrowApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptToolBorrowApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        Map<Long, BizReceiptAssembleDTO> bizReceiptAssembleDTOMapOld = headDTO.getItemList().stream().flatMap(itemDTO -> itemDTO.getAssembleDTOList().stream()).collect(Collectors.toMap(BizReceiptAssembleDTO::getId, Function.identity()));
        Map<Long, BizReceiptAssembleDTO> bizReceiptAssembleDTOMapNew = po.getItemList().stream().flatMap(itemDTO -> itemDTO.getAssembleDTOList().stream()).collect(Collectors.toMap(BizReceiptAssembleDTO::getId, Function.identity()));
        boolean needSplit = true;
        List<BizReceiptToolBorrowApplyItemDTO> itemList = new ArrayList<>();
        po.getItemList().forEach(itemDTO -> {
            itemList.add(UtilBean.newInstance(itemDTO, BizReceiptToolBorrowApplyItemDTO.class));
            itemDTO.setReturnedQty(itemDTO.getQty().add(itemDTO.getReturnedQty()));

        });
        // 整单提交且数量一次性归还
        if (headDTO.getItemList().size() == po.getItemList().size() &&
                po.getItemList().stream().allMatch(itemDTO -> itemDTO.getQty().compareTo(itemDTO.getPreReceiptQty()) == 0)) {
            needSplit = false;
            po.setSubmitUserId(currentUser.getId());
            po.setSubmitTime(UtilDate.getNow());
            po.getItemList().forEach(itemDTO -> {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : itemDTO.getAssembleDTOList()) {
                    for (BizLabelReceiptRelDTO bizLabelReceiptRelDTO : bizReceiptAssembleDTO.getLabelDataList()) {
                        // 更新标签上的仓位和存储类型
                        BizLabelData bizLabelData = new BizLabelData().setId(bizLabelReceiptRelDTO.getLabelId()).setBinId(itemDTO.getTargetBinId()).setTypeId(itemDTO.getTargetTypeId());
                        bizLabelDataDataWrap.updateById(bizLabelData);
                    }
                }
            });
        }
        if (needSplit) {
            // 拆单提交
            // 更新行项目已归还数量
            bizReceiptToolBorrowApplyItemDataWrap.updateBatchDtoById(po.getItemList());
            // 已归还数量等于借用的数量时删除行项目
            po.getItemList().forEach(itemDTO -> {
                if (itemDTO.getReturnedQty().compareTo(itemDTO.getPreReceiptQty()) == 0) {
                    bizReceiptToolBorrowApplyItemDataWrap.physicalDeleteById(itemDTO.getId());
                }
            });
            // 重置assemble数量
            bizReceiptAssembleDTOMapNew.forEach((k, v) -> {
                if (bizReceiptAssembleDTOMapOld.containsKey(k) && bizReceiptAssembleDTOMapOld.get(k).getQty().compareTo(v.getQty()) != 0) {
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble().setId(k).setQty(bizReceiptAssembleDTOMapOld.get(k).getQty().subtract(v.getQty()));
                    bizReceiptAssembleDataWrap.updateById(bizReceiptAssemble);
                }
            });
            // 生成新的已完成的归还单
            if (UtilCollection.isNotEmpty(itemList)) {
                BizReceiptToolBorrowApplyHeadDTO newHeadDTO = UtilBean.deepCopyNewInstance(po, BizReceiptToolBorrowApplyHeadDTO.class);
                newHeadDTO.setSubmitUserId(currentUser.getId());
                newHeadDTO.setSubmitTime(UtilDate.getNow());
                newHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                newHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValue(EnumSequenceCode.TOOL_RETURN.getValue()));
                newHeadDTO.setId(null);
                bizReceiptToolBorrowApplyHeadDataWrap.saveDto(newHeadDTO);
                AtomicInteger atomicInteger = new AtomicInteger(1);
                itemList.forEach(itemDTO -> {
                    itemDTO.setReturnedQty(itemDTO.getQty().add(itemDTO.getReturnedQty()));
                    itemDTO.setId(null);
                    itemDTO.setRid(String.valueOf(atomicInteger.getAndIncrement()));
                    itemDTO.setHeadId(newHeadDTO.getId());
                    itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                });
                bizReceiptToolBorrowApplyItemDataWrap.saveBatchDto(itemList);

                // 特征表处理
                List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
                for (BizReceiptToolBorrowApplyItemDTO itemDTO : itemList) {
                    Long itemId = itemDTO.getId();
                    List<BizReceiptAssembleDTO> assembleList = itemDTO.getAssembleDTOList();
                    if (UtilCollection.isNotEmpty(assembleList)) {
                        for (BizReceiptAssembleDTO bizReceiptAssembleDTO : assembleList) {
                            bizReceiptAssembleDTO.setReceiptType(po.getReceiptType());
                            bizReceiptAssembleDTO.setReceiptHeadId(newHeadDTO.getId());
                            bizReceiptAssembleDTO.setReceiptItemId(itemId);
                            bizReceiptAssembleDTO.setId(null);
                            bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                                    ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                                    : bizReceiptAssembleDTO.getSpecType());
                            assembleDTOList.add(bizReceiptAssembleDTO);
                        }
                    }
                }
                bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
                // 处理标签
                this.handleLabel(itemList, newHeadDTO, assembleDTOList);
                // 保存单据流
                newHeadDTO.setItemList(itemList);
                this.saveReceiptTree(newHeadDTO);
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, newHeadDTO);
            }
        } else {
            // 更新状态已完成
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, po);
        }
        // 生成库存转移凭证
        this.generateTaskInsDoc(ctx);
        // 库存校验
        this.checkAndComputeForModifyStock(ctx);
        // 库存修改
        this.modifyStock(ctx);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, po.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);

    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void saveApply(BizContext ctx) {
        // 入参上下文
        BizReceiptToolBorrowApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.TOOL_BORROW_APPLY.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新专用工器具借用申请
            bizReceiptToolBorrowApplyHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);
            QueryWrapper<BizLabelReceiptRel> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizLabelReceiptRel::getReceiptHeadId, po.getId());
            bizLabelReceiptRelDataWrap.physicalDelete(queryWrapperAssemble);
            // 特征物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(po.getId());
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValue(EnumSequenceCode.TOOL_BORROW_APPLY.getValue());
            po.setReceiptCode(code);
            bizReceiptToolBorrowApplyHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存专用工器具借用申请head成功!单号{},主键{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptToolBorrowApplyItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        bizReceiptToolBorrowApplyItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("批量保存专用工器具借用申请item成功,code{},headId{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/

        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptToolBorrowApplyItemDTO itemDTO : po.getItemList()) {
            Long itemId = itemDTO.getId();
            List<BizReceiptAssembleDTO> assembleList = itemDTO.getAssembleDTOList();
            if (UtilCollection.isNotEmpty(assembleList)) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : assembleList) {
                    bizReceiptAssembleDTO.setReceiptType(po.getReceiptType());
                    bizReceiptAssembleDTO.setReceiptHeadId(po.getId());
                    bizReceiptAssembleDTO.setReceiptItemId(itemId);
                    bizReceiptAssembleDTO.setId(null);
                    bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                            ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                            : bizReceiptAssembleDTO.getSpecType());
                    assembleDTOList.add(bizReceiptAssembleDTO);
                }
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);

        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        for (BizReceiptAssembleDTO assembleDTO : assembleDTOList) {
            Long labelId = assembleDTO.getLabelId();
            Long itemId = assembleDTO.getReceiptItemId();
            if (UtilNumber.isNotEmpty(labelId)) {
                BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                labelReceiptRel.setLabelId(labelId);
                labelReceiptRel.setReceiptType(po.getReceiptType());
                labelReceiptRel.setReceiptHeadId(assembleDTO.getReceiptHeadId());
                labelReceiptRel.setReceiptItemId(itemId);
                labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                bizLabelReceiptRelList.add(labelReceiptRel);
            }
        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }


    private void handleLabel(List<BizReceiptToolBorrowApplyItemDTO> itemDTOList, BizReceiptToolBorrowApplyHeadDTO headDTO, List<BizReceiptAssembleDTO> assembleDTOList) {
        // 将所有标签操作数量大于0的按标签ID进行聚合，聚合时判断如果操作数量总和大于标签数量则异常
        // 逐个遍历行项目，如果某个行项目的存在标签，并判断这个标签的所有操作数量小于标签数量则拆分标签，拆分标签包含生成新标签数据和更新已有标签数量(扣除新标签数量)
        // 生成新的标签关联单据数据，并复制源标签关联单据到新标签上
        AtomicInteger rfidRid = new AtomicInteger(1);
        List<BizLabelData> labelNewList = new ArrayList<>();
        List<BizLabelData> labelUpdateList = new ArrayList<>();
        List<BizLabelReceiptRel> relNewList = new ArrayList<>();
        Map<Long, BizLabelData> labelEntityMap = new HashMap();
        Map<Long, List<BizReceiptToolBorrowApplyItemDTO>> itemDTOMap = itemDTOList.stream().collect(Collectors.groupingBy(BizReceiptToolBorrowApplyItemDTO::getId));
        Integer receiptType = headDTO.getReceiptType();
        Long headId = headDTO.getId();
        Long userId = headDTO.getCreateUserId();
        for (BizReceiptAssembleDTO assembleDTO : assembleDTOList) {
            BizReceiptToolBorrowApplyItemDTO itemDTO = itemDTOMap.get(assembleDTO.getReceiptItemId()).get(0);
            // 先物理删除标签单据关联
            labelReceiptRelService.physicalDeleteLabelReceiptRelByItemId(itemDTO.getId());
            Long labelId = assembleDTO.getLabelId();
            if (UtilNumber.isEmpty(labelId))
                continue;
            List<BizLabelReceiptRelDTO> relDTOList = assembleDTO.getLabelDataList();
            if (CollectionUtils.isEmpty(relDTOList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            List<BizLabelReceiptRelDTO> opLabelList = relDTOList.stream().filter(e -> e.getLabelId() != null && e.getLabelId().equals(labelId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(opLabelList)) {
                continue;
            }
            BigDecimal qty = assembleDTO.getQty();
            Map<Long, BizLabelReceiptRelDTO> labelDataMap = new HashMap<>();
            for (BizLabelReceiptRelDTO bizLabelDataDTO : opLabelList) {
                BizLabelReceiptRelDTO temp = labelDataMap.get(labelId);
                if (temp == null) {
                    bizLabelDataDTO.setQty(qty);
                    labelDataMap.put(labelId, bizLabelDataDTO);
                    continue;
                }
                temp.setQty(temp.getQty().add(qty));
                labelDataMap.put(labelId, temp);
            }
            for (Long sourceLabelId : labelDataMap.keySet()) {
                BizLabelReceiptRelDTO bizLabelData = labelDataMap.get(sourceLabelId);
                BizLabelData sourceLabelData = labelEntityMap.get(sourceLabelId);
                if (sourceLabelData == null) {
                    sourceLabelData = bizLabelDataDataWrap.getById(sourceLabelId);
                    if (sourceLabelData == null)
                        throw new WmsException(EnumReturnMsg.LABEL_NOT_EXIST);
                    labelEntityMap.put(sourceLabelId, sourceLabelData);
                }
                BizLabelData rel = null;
                BigDecimal operationQty = bizLabelData.getQty();
                if (operationQty.compareTo(sourceLabelData.getQty()) < 0) {
                    rel = UtilBean.newInstance(sourceLabelData, BizLabelData.class);
                    rel.setQty(operationQty);
                    rel.setId(null);
                    rel.setLabelCode(null);
                    rel.setSourceLabelId(sourceLabelId);
                    rel.setRid(rfidRid.getAndIncrement());
                    rel.setSnCode(null);
                    rel.setWhId(itemDTO.getWhId());
                    rel.setTypeId(itemDTO.getTargetTypeId());
                    rel.setBinId(itemDTO.getTargetBinId());
                    rel.setCellId(0L);
                    labelNewList.add(rel);
                    sourceLabelData.setQty(sourceLabelData.getQty().subtract(operationQty));
                    labelUpdateList.add(sourceLabelData);
                } else if (operationQty.compareTo(sourceLabelData.getQty()) == 0) {
                    rel = sourceLabelData;
                    sourceLabelData.setTypeId(itemDTO.getTargetTypeId());
                    sourceLabelData.setBinId(itemDTO.getTargetBinId());
                    labelUpdateList.add(sourceLabelData);
                } else {
                    throw new WmsException(EnumReturnMsg.LABEL_QTY_NOT_ENOUGH);
                }
                BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                labelReceiptRel.setLabelId(rel.getId());
                labelReceiptRel.setReceiptType(receiptType);
                labelReceiptRel.setReceiptHeadId(headId);
                labelReceiptRel.setReceiptItemId(itemDTO.getId());
                labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                labelReceiptRel.setPreReceiptHeadId(null);
                labelReceiptRel.setPreReceiptItemId(null);
                labelReceiptRel.setPreReceiptBinId(null);
                labelReceiptRel.setRid(rel.getRid());
                labelReceiptRel.setStatus(null);
                labelReceiptRel.setCreateUserId(userId);
                relNewList.add(labelReceiptRel);
            }
        }
        Map<Integer, BizLabelDataDTO> labelDataDTOMap = new HashMap<>();
        if (UtilCollection.isNotEmpty(labelNewList)) {
            // 插入批次标签
            List<String> labelCodeList = bizCommonService.getNextNValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue(), labelNewList.size());
            int idx = 0;
            for (BizLabelData labelData : labelNewList) {
                String code = labelCodeList.get(idx);
                labelData.setLabelCode(code);
                labelData.setSnCode(code);
                idx++;
            }
            List<BizLabelDataDTO> labelDataDTOList = UtilCollection.toList(labelNewList, BizLabelDataDTO.class);
            labelDataService.saveBatchDto(labelDataDTOList);
            // 复制关联关系
            labelReceiptRelService.copyRel(labelNewList);
            labelDataDTOList.forEach(e -> {
                labelDataDTOMap.put(e.getRid(), e);
            });
        }
        if (!CollectionUtils.isEmpty(labelUpdateList)) {
            labelDataService.multiUpdateLabelData(labelUpdateList);
        }
        List<BizReceiptAssemble> assembles = new ArrayList<>();
        if (UtilCollection.isNotEmpty(relNewList)) {
            // 设置新标签id
            for (BizLabelReceiptRel rel : relNewList) {
                BizLabelDataDTO labelDataDTO = labelDataDTOMap.get(rel.getRid());
                if (labelDataDTO != null) {
                    rel.setLabelId(labelDataDTO.getId());
                    Long receiptBinId = rel.getReceiptBinId();
                    BizReceiptAssemble bizReceiptAssemble = new BizReceiptAssemble().setId(receiptBinId).setLabelId(labelDataDTO.getId());
                    assembles.add(bizReceiptAssemble);
                }

            }
            labelReceiptRelService.saveBatch(relNewList);
            // 重置配货LabelId
            bizReceiptAssembleDataWrap.updateBatchByIdOptimize(assembles);
        }
    }

    /**
     * 删除专用工器具借用申请行项目
     *
     * @param headDTO 专用工器具借用申请
     */
    private void deleteItem(BizReceiptToolBorrowApplyHeadDTO headDTO) {
        UpdateWrapper<BizReceiptToolBorrowApplyItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptToolBorrowApplyItem::getHeadId,
                headDTO.getId());
        bizReceiptToolBorrowApplyItemDataWrap.physicalDelete(wrapper);
    }


    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的专用工器具借用申请
        BizReceiptToolBorrowApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptToolBorrowApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存专用工器具借用申请附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.TOOL_BORROW_APPLY.getValue(), user.getId());
        log.debug("保存专用工器具借用申请附件成功!");
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submitApply(BizContext ctx) {
        // 入参上下文
        BizReceiptToolBorrowApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存专用工器具借用申请
        this.saveApply(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
        // 更新专用工器具借用申请head、item状态 - 审核中
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }


    /**
     * 准备更新专用工器具借用申请状态
     *
     * @param headDTO     专用工器具借用申请head
     * @param itemDTOList 专用工器具借用申请item
     */
    public void updateStatus(BizReceiptToolBorrowApplyHeadDTO headDTO, List<BizReceiptToolBorrowApplyItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新专用工器具借用申请head状态
     *
     * @param headDto 专用工器具借用申请head
     */
    private void updateHead(BizReceiptToolBorrowApplyHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptToolBorrowApplyHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新专用工器具借用申请item状态
     *
     * @param itemDtoList 专用工器具借用申请item
     */
    private void updateItem(List<BizReceiptToolBorrowApplyItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptToolBorrowApplyItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }


    /**
     * 获取物料特性库存【非同时模式】
     * 专用工器具借用只能添加J047下CT开头的成套设备码，并且物资类型为TE: 安装工具
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptMatStockQueryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 构造查询条件
        BizReceiptAssembleRuleSearchPO pos = this.getBizReceiptAssembleRuleSearchPO(ctx, po);
        if (pos == null) return;
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValueBySdw(null, null, po.getReceiptType(), pos);
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            for (BizReceiptAssembleDTO dto : assembleDTOList) {
                dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            }
            if (UtilCollection.isNotEmpty(po.getItemList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptToolBorrowApplyItemDTO itemDTO : po.getItemList()) {
                    for (BizReceiptAssembleDTO dto : assembleDTOList) {
                        if (dto.getBinIdTemp().equals(itemDTO.getSourceBinId())) {
                            dto.setStockQty(dto.getStockQty().subtract(itemDTO.getQty()));
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                    && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                    && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    String typeCode = EnumDefaultStorageType.TOOL_BORROW.getTypeCode();
                    String binCode = EnumDefaultStorageType.TOOL_BORROW.getBinCode();
                    Long typeId = dictionaryService.getStorageTypeIdCacheByCode(assembleDTO.getWhCode(), typeCode);
                    Long binId = dictionaryService.getBinIdCacheByCode(assembleDTO.getWhCode(), typeCode, binCode);
                    assembleDTO.setInputTypeId(typeId);
                    assembleDTO.setInputBinId(binId);
                    assembleDTO.setInputBinCode(dictionaryService.getBinCacheById(binId).getBinCode());
                    assembleDTO.setInputTypeCode(dictionaryService.getStorageTypeCacheById(typeId).getTypeCode());
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                            && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue()))) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getNonCellList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                    && labelData.getMatId().equals(assembleDTO.getMatId())
                                    && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                    && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                    && labelData.getBinId().equals(binId)) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
        }
        // List<BizReceiptToolBorrowApplyItemDTO> itemDTOList = new ArrayList<>();
        // if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
        //     // 特性库存转行项目
        //     for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
        //         BizReceiptToolBorrowApplyItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptToolBorrowApplyItemDTO.class);
        //         itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
        //         itemDTO.setSourceBinId(assembleDTO.getBinIdTemp());
        //         itemDTO.setSourceTypeId(assembleDTO.getTypeIdTemp());
        //         itemDTOList.add(itemDTO);
        //     }
        //
        // }
        // MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        // matStockDTO.setToolBorrowApplyItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO<>(assembleRuleDTO));
    }

    private BizReceiptAssembleRuleSearchPO getBizReceiptAssembleRuleSearchPO(BizContext ctx, BizReceiptMatStockQueryPO po) {
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return null;
            }
            pos.setMatId(matId);
        }
        String matName = po.getMatName();
        if (StringUtils.isNotBlank(matName)) {
            List<DicMaterial> dicMaterialList = dicMaterialDataWrap.findByName(matName);
            if (CollectionUtils.isEmpty(dicMaterialList)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return null;
            }
            Set<Long> matIdSet = new HashSet<>(dicMaterialList.size());
            for (DicMaterial dicMaterial : dicMaterialList) {
                Long matId = dicMaterial.getId();
                matIdSet.add(matId);
            }
            Long matIdSearch = pos.getMatId();
            if (matIdSearch != null) {
                if (!matIdSet.contains(matIdSearch)) {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                    return null;
                }
            } else {
                pos.setMatIdSet(matIdSet);
            }
        }
        String binCode = po.getBinCode();
        Long whId = po.getWhId();
        if (StringUtils.isNotBlank(binCode) && !UtilNumber.isEmpty(whId)) {
            DicWh dicWh = dictionaryService.getWhCacheById(po.getWhId());
            if (dicWh == null) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return null;
            }
            DicWhStorageBinDTO binDTO = dictionaryService.getBinCacheByBinCode(dicWh.getWhCode(), binCode);
            if (binDTO == null) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return null;
            }
            pos.setBinId(binDTO.getId());
        }
        // 专用工器具借用只能添加J047下CT开头的成套设备码，并且物资类型为TE: 安装工具
        pos.setFtyId(dictionaryService.getFtyIdCacheByCode(EnumFactory.J047.getFtyCode()));
        pos.setExtend28(EnumMatType.TE.getFullDesc());
        pos.setStockStatus(stockStatus);
        String specStockCode = pos.getSpecStockCode();
        if (StringUtils.isBlank(specStockCode))
            pos.setSpecStockCode(null);
        // 设置成套设备
        pos.setIsUnitized(true);
        return pos;
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptToolBorrowApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 审批人校验
        // this.approveCheck(userDept);
        // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
        Long ftyId = headDTO.getItemList().get(0).getFtyId();
        if (UtilNumber.isEmpty(headDTO.getProfessionalEngineerUserId())) {
            throw new WmsException("承包商主管部门专业工程师未选择");
        }
        List<String> userCode = new ArrayList<>();
        userCode.add(dictionaryService.getSysUserCacheById(headDTO.getProfessionalEngineerUserId()).getUserCode());
        variables.put("userCode", userCode);
        variables.put("ftyId", ftyId);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

    }

    /**
     * 审批人校验
     *
     * @param userDeptList
     */
    private void approveCheck(List<MetaDataDeptOfficePO> userDeptList) {
        List<String> userCodeList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(userDeptList.get(0).getDeptCode(), userDeptList.get(0).getDeptOfficeCode(), EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
    }


    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptToolBorrowApplyHead head = bizReceiptToolBorrowApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptToolBorrowApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptToolBorrowApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            // 处理标签
            this.handleLabel(headDTO.getItemList(), headDTO, headDTO.getItemList().stream().flatMap(item -> item.getAssembleDTOList().stream()).collect(Collectors.toList()));
            // 生成库存转移凭证
            this.generateTaskInsDoc(ctx);
            // 库存校验
            this.checkAndComputeForModifyStock(ctx);
            // 库存修改
            this.modifyStock(ctx);
            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            // 生成归还单
            this.generateToolReturnReceipt(ctx);
        } else {
            // 更新状态已驳回
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 生成作业凭证
     *
     * @param ctx 系统上下文
     */
    public void generateTaskInsDoc(BizContext ctx) {
        log.info("生成ins凭证 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptToolBorrowApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        if (null == headDTO) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        StockInsMoveTypeDTO stockInsMoveTypeDTO = new StockInsMoveTypeDTO();
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            // 凭证code
            String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
            // rid
            AtomicInteger insDocRid = new AtomicInteger(1);
            List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
            List<StockInsDocBin> insDocBinList = new ArrayList<>();
            List<StockInsDocBinPo> insDocBinPoList = new ArrayList<>();
            headDTO.getItemList().forEach(item -> {
                // 增加
                StockInsDocBin insDocBinS = new StockInsDocBin();
                insDocBinS.setInsDocCode(code);
                insDocBinS.setInsDocRid(String.valueOf(insDocRid.getAndIncrement()));
                insDocBinS.setMatId(item.getMatId());
                insDocBinS.setBatchId(item.getBatchId());
                insDocBinS.setFtyId(item.getFtyId());
                insDocBinS.setLocationId(item.getLocationId());
                insDocBinS.setWhId(item.getWhId());
                insDocBinS.setTypeId(item.getTargetTypeId());
                insDocBinS.setBinId(item.getTargetBinId());
                insDocBinS.setCellId(0L);
                insDocBinS.setUnitId(item.getUnitId());
                insDocBinS.setMoveQty(item.getQty());
                insDocBinS.setDebitCredit(Const.DEBIT_S_ADD);
                insDocBinS.setStockStatus(stockCommonService.getStockStatus(headDTO.getReceiptType()));
                // 前续单据作业单
                insDocBinS.setPreReceiptHeadId(item.getHeadId());
                insDocBinS.setPreReceiptItemId(item.getId());
                insDocBinS.setPreReceiptType(headDTO.getReceiptType());
                insDocBinS.setPreReceiptBinId(null);
                // 参考单据入库单
                insDocBinS.setReferReceiptHeadId(null);
                insDocBinS.setReferReceiptItemId(null);
                insDocBinS.setReferReceiptType(null);
                insDocBinS.setReferReceiptBinId(null);
                insDocBinS.setMatDocCode(null);
                insDocBinS.setMatDocRid(null);
                // 扣减
                StockInsDocBin insDocBinH = new StockInsDocBin();
                insDocBinH.setInsDocCode(code);
                insDocBinH.setInsDocRid(String.valueOf(insDocRid.getAndIncrement()));
                insDocBinH.setMatId(item.getMatId());
                insDocBinH.setBatchId(item.getBatchId());
                insDocBinH.setFtyId(item.getFtyId());
                insDocBinH.setLocationId(item.getLocationId());
                insDocBinH.setWhId(item.getWhId());
                insDocBinH.setTypeId(item.getSourceTypeId());
                insDocBinH.setBinId(item.getSourceBinId());
                insDocBinH.setCellId(0L);
                insDocBinH.setUnitId(item.getUnitId());
                insDocBinH.setMoveQty(item.getQty());
                insDocBinH.setDebitCredit(Const.CREDIT_H_SUBTRACT);
                insDocBinH.setStockStatus(stockCommonService.getStockStatus(headDTO.getReceiptType()));
                // 前续单据作业单
                insDocBinH.setPreReceiptHeadId(item.getHeadId());
                insDocBinH.setPreReceiptItemId(item.getId());
                insDocBinH.setPreReceiptType(headDTO.getReceiptType());
                insDocBinH.setPreReceiptBinId(null);
                // 参考单据入库单
                insDocBinH.setReferReceiptHeadId(null);
                insDocBinH.setReferReceiptItemId(null);
                insDocBinH.setReferReceiptType(null);
                insDocBinH.setReferReceiptBinId(null);
                insDocBinH.setMatDocCode(null);
                insDocBinH.setMatDocRid(null);
                insDocBinList.add(insDocBinH);
                insDocBinList.add(insDocBinS);
            });
            stockInsMoveTypeDTO.setInsDocBatchList(insDocBatchList);
            stockInsMoveTypeDTO.setInsDocBinList(insDocBinList);
            stockInsMoveTypeDTO.setInsDocBinPoList(insDocBinPoList);
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypeDTO);
    }

    /**
     * 库存校验和数量计算
     *
     * @param ctx 系统上下文
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        log.info("库存校验和数量计算 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 修改库存
     *
     * @param ctx 系统上下文
     */
    public void modifyStock(BizContext ctx) {
        log.info("修改库存 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 生成草稿专用工器具归还单
     *
     * @param ctx
     */
    public void generateToolReturnReceipt(BizContext ctx) {
        BizReceiptToolBorrowApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        BizReceiptToolBorrowApplyHead headDataWrapById = bizReceiptToolBorrowApplyHeadDataWrap.getById(headDTO.getId());
        BizReceiptToolBorrowApplyHeadDTO bizReceiptToolBorrowApplyHeadDTO = UtilBean.newInstance(headDataWrapById, BizReceiptToolBorrowApplyHeadDTO.class);
        dataFillService.fillAttr(bizReceiptToolBorrowApplyHeadDTO);

        bizReceiptToolBorrowApplyHeadDTO.setReceiptType(EnumReceiptType.TOOL_RETURN.getValue());
        bizReceiptToolBorrowApplyHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        bizReceiptToolBorrowApplyHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValue(EnumSequenceCode.TOOL_RETURN.getValue()));
        bizReceiptToolBorrowApplyHeadDTO.setCreateTime(new Date());
        bizReceiptToolBorrowApplyHeadDTO.setModifyTime(new Date());
        bizReceiptToolBorrowApplyHeadDTO.setId(null);
        bizReceiptToolBorrowApplyHeadDataWrap.saveDto(bizReceiptToolBorrowApplyHeadDTO);
        log.debug("保存专用工器具归还head成功!单号{}", bizReceiptToolBorrowApplyHeadDTO.getReceiptCode());
        bizReceiptToolBorrowApplyHeadDTO.getItemList().forEach(
                item -> {
                    item.setHeadId(bizReceiptToolBorrowApplyHeadDTO.getId());
                    item.setPreReceiptHeadId(headDTO.getId());
                    item.setPreReceiptItemId(item.getId());
                    item.setPreReceiptQty(item.getQty());
                    item.setPreReceiptType(headDTO.getReceiptType());
                    item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                    item.setSourceTypeId(item.getTargetTypeId());
                    item.setSourceBinId(item.getTargetBinId());
                    item.setTargetTypeId(0L);
                    item.setTargetBinId(0L);
                    item.setReturnStatus(1);
                    // 设置归还数量为0
                    item.setQty(BigDecimal.ZERO);
                    item.setCreateTime(new Date());
                    item.setModifyTime(new Date());
                    item.setId(null);
                }
        );
        bizReceiptToolBorrowApplyItemDataWrap.saveBatchDto(bizReceiptToolBorrowApplyHeadDTO.getItemList());
        // 保存单据流
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, bizReceiptToolBorrowApplyHeadDTO);
        this.saveReceiptTree(bizReceiptToolBorrowApplyHeadDTO);

        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptToolBorrowApplyItemDTO itemDTO : bizReceiptToolBorrowApplyHeadDTO.getItemList()) {
            Long itemId = itemDTO.getId();
            List<BizReceiptAssembleDTO> assembleList = itemDTO.getAssembleDTOList();
            if (UtilCollection.isNotEmpty(assembleList)) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : assembleList) {
                    bizReceiptAssembleDTO.setReceiptType(bizReceiptToolBorrowApplyHeadDTO.getReceiptType());
                    bizReceiptAssembleDTO.setReceiptHeadId(bizReceiptToolBorrowApplyHeadDTO.getId());
                    bizReceiptAssembleDTO.setReceiptItemId(itemId);
                    bizReceiptAssembleDTO.setId(null);
                    bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                            ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                            : bizReceiptAssembleDTO.getSpecType());
                    assembleDTOList.add(bizReceiptAssembleDTO);
                }
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);

        // 特征表配货处理
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        for (BizReceiptAssembleDTO assembleDTO : assembleDTOList) {
            for (BizLabelReceiptRelDTO bizLabelReceiptRelDTO : assembleDTO.getLabelDataList()) {
                BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                labelReceiptRel.setLabelId(bizLabelReceiptRelDTO.getLabelId());
                labelReceiptRel.setReceiptType(bizReceiptToolBorrowApplyHeadDTO.getReceiptType());
                labelReceiptRel.setReceiptHeadId(assembleDTO.getReceiptHeadId());
                labelReceiptRel.setReceiptItemId(assembleDTO.getReceiptItemId());
                labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                bizLabelReceiptRelList.add(labelReceiptRel);
            }

        }
        if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        }
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptToolBorrowApplyHeadDTO : "要保持单据流的申请单"}
     */
    public void saveReceiptTree(BizReceiptToolBorrowApplyHeadDTO headDTO) {
        // 入参上下文 - 要保持单据流的申请单
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptToolBorrowApplyItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }
}
