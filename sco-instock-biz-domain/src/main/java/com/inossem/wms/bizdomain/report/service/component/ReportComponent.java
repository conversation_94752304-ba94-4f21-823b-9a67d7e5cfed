package com.inossem.wms.bizdomain.report.service.component;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchImgDataWrap;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoMaintainDataWrap;
import com.inossem.wms.bizbasis.common.dao.SequenceMapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizCommonReceiptAttachmentDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpProductionReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReserveReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpSaleReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnHeadDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.BizMaterialReturnItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageBinService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageBinDataWrap;
import com.inossem.wms.bizbasis.masterdata.purchasepackage.service.datawrap.DicPurchasePackageDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.stock.dao.StockBatchMapper;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyBinDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.paper.service.datawrap.BizReceiptPaperHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.bizdomain.report.dao.BizReportMapper;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnHeadDataWrap;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnItemDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportHeadDataWrap;
import com.inossem.wms.bizdomain.transport.service.datawrap.BizReceiptTransportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.constant.sap.SapConst;
import com.inossem.wms.common.constant.task.TaskConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.lifetime.EnumInspectResult;
import com.inossem.wms.common.enums.maintain.EnumMaintenanceType;
import com.inossem.wms.common.enums.report.EnumStockAgeType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.metadata.MetadataContext;
import com.inossem.wms.common.model.auth.rel.entity.SysUserDeptOfficeRel;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchImg;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyBin;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.paper.entity.BizReceiptPaperHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.bizdomain.report.dto.StockRetentionDTO;
import com.inossem.wms.common.model.bizdomain.report.po.*;
import com.inossem.wms.common.model.bizdomain.report.vo.*;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnHead;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnItem;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportHead;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportItem;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.PageObjectWithTotalVO;
import com.inossem.wms.common.model.common.enums.DeliveryTypeMapVO;
import com.inossem.wms.common.model.common.enums.MoveTypeMapVO;
import com.inossem.wms.common.model.common.enums.ReceiptTypeMapVO;
import com.inossem.wms.common.model.common.enums.SpecStockMapVO;
import com.inossem.wms.common.model.common.enums.StockStatusMapVO;
import com.inossem.wms.common.model.common.enums.ShippingTypeMapVO;
import com.inossem.wms.common.model.erp.entity.ErpMatDoc;
import com.inossem.wms.common.model.erp.entity.ErpProductionReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpReserveReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpSaleReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpStockBatch;
import com.inossem.wms.common.model.erp.entity.ErpStockTurnover;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnHead;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnItem;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.purchasepackage.entity.DicPurchasePackage;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.sequence.vo.SysSequencePrefixVO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPagePO;
import com.inossem.wms.common.model.stock.vo.StockInsDocBinVO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.common.util.sap.SapApiCallProxy;
import com.inossem.wms.system.log.service.datawrap.LogLoginLogDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 报表代码代码块
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */
@Service
@Slf4j
public class ReportComponent {
    @Autowired
    protected BizReportMapper bizReportMapper;

    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    protected SequenceMapper sequenceMapper;

    @Autowired
    protected ErpPurchaseReceiptHeadDataWrap erpPurchaseReceiptHeadDataWrap;
    @Autowired
    protected ErpProductionReceiptHeadDataWrap erpProductionReceiptHeadDataWrap;
    @Autowired
    protected ErpSaleReceiptHeadDataWrap erpSaleReceiptHeadDataWrap;
    @Autowired
    protected ErpReserveReceiptHeadDataWrap erpReserveReceiptHeadDataWrap;
    @Autowired
    protected DictionaryService dictionaryService;


    @Autowired
    private BatchImgService bizBatchImgService;
    @Autowired
    private BizBatchImgDataWrap bizBatchImgDataWrap;
    @Autowired
    private StockCommonService stockCommonService;
    @Autowired
    private BatchInfoService batchInfoService;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private WhStorageBinService whStorageBinService;
    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    private SapApiCallProxy sapApiCallProxy;
    @Autowired
    private BizReceiptPaperHeadDataWrap bizReceiptPaperHeadDataWrap;
    @Autowired
    private DicPurchasePackageDataWrap dicPurchasePackageDataWrap;
    @Autowired
    private StockBatchMapper stockBatchMapper;
    @Autowired
    private LogLoginLogDataWrap logLoginLogDataWrap;
    @Autowired
    private BizBatchInfoMaintainDataWrap bizBatchInfoMaintainDataWrap;
    @Autowired
    private BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;
    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;
    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;
    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;
    @Autowired
    private BizMaterialReturnHeadDataWrap bizMaterialReturnHeadDataWrap;
    @Autowired
    private BizReceiptTransportItemDataWrap bizReceiptTransportItemDataWrap;
    @Autowired
    private BizMaterialReturnItemDataWrap bizMaterialReturnItemDataWrap;
    @Autowired
    private BizReceiptTransportHeadDataWrap bizReceiptTransportHeadDataWrap;
    @Autowired
    private BizReceiptApplyBinDataWrap bizReceiptApplyBinDataWrap;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private BizReceiptReturnItemDataWrap bizReceiptReturnItemDataWrap;
    @Autowired
    private BizReceiptReturnHeadDataWrap bizReceiptReturnHeadDataWrap;
    @Autowired
    private BizReceiptRegisterItemDataWrap bizReceiptRegisterItemDataWrap;
    @Autowired
    private BizReceiptRegisterHeadDataWrap bizReceiptRegisterHeadDataWrap;
    @Autowired
    private UserService userService;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private DicWhStorageBinDataWrap dicWhStorageBinDataWrap;
    @Autowired
    private BizCommonReceiptAttachmentDataWrap bizCommonReceiptAttachmentDataWrap;

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 获取库存状态下拉
     *
     * @param ctx ctx
     */
    public void getStockStatusList(BizContext ctx) {

        MultiResultVO<StockStatusMapVO> vo = new MultiResultVO<>(EnumStockStatus.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 获取特殊库存下拉
     *
     * @param ctx ctx
     */
    public void getSpecStockList(BizContext ctx) {
        MultiResultVO<SpecStockMapVO> vo = new MultiResultVO<>(EnumSpecStock.toList());
        // 保留正常库存和其它入库库存
        vo.setResultList(vo.getResultList().stream().filter(p -> p.getSpecStock().equals(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_NORMAL.getValue())||p.getSpecStock().equals(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_OTHER_INPUT.getValue())).collect(Collectors.toList()));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 获取移动类型下拉
     *
     * @param ctx ctx
     */
    public void getMoveTypeList(BizContext ctx) {
        MultiResultVO<MoveTypeMapVO> vo = new MultiResultVO<>(EnumMoveType.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }


    /**
     * 业务凭证获取单据类型下拉
     *
     * @param ctx ctx
     */
    public void getReceiptTypeList(BizContext ctx) {
        MultiResultVO<ReceiptTypeMapVO> vo = new MultiResultVO<>(EnumReceiptType.toInsDocBatchReceiptList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询批次库存详情
     *
     * @param ctx ctx
     */
    public void selectStockBatchDetail(BizContext ctx, boolean exportFlag) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setIsUnitized(false);
        List<StockBatchVO> result;
        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectStockBatchDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (!exportFlag && totalCount > 0) {
            // 非导出时，设置批次图片和附件信息

            Set<Long> batchIds = result.stream().map(StockBatchVO::getBatchId).collect(Collectors.toSet());

            if (UtilCollection.isNotEmpty(batchIds)) {
                // 设置批次图片
                List<BizBatchImg> batchImgs = bizBatchImgDataWrap.list(new LambdaQueryWrapper<BizBatchImg>()
                        .in(BizBatchImg::getBatchId, batchIds)
                );
                Map<Long, List<BizBatchImg>> imgMap = batchImgs.stream().collect(Collectors.groupingBy(BizBatchImg::getBatchId));
                result.forEach(p -> p.setBizBatchImgDTOList(UtilCollection.toList(imgMap.get(p.getBatchId()), BizBatchImgDTO.class)));

                // 设置批次附件
                List<BizCommonReceiptAttachment> attachmentList = bizCommonReceiptAttachmentDataWrap.list(
                        new LambdaQueryWrapper<BizCommonReceiptAttachment>()
                                .in(BizCommonReceiptAttachment::getBatchId, batchIds));
                Map<Long, List<BizCommonReceiptAttachment>> attachmentFileMap = attachmentList.stream().collect(Collectors.groupingBy(BizCommonReceiptAttachment::getBatchId));

                result.forEach(p -> p.setBatchAttachmentFileList(attachmentFileMap.get(p.getBatchId())));
            }

        }
        dataFillService.fillAttr(result);

        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出批次库存Excel
     *
     * @param ctx 上下文
     */
    public void exportStockBatchDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("批次库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockBatchDetail(ctx, true);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockBatchVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询批次库存（库存金额）基于物料组分组
     *
     * @param ctx ctx
     */
    public void selectStockBatchGroupByMatGroup(BizContext ctx) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBatchVO> result;

        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        result = bizReportMapper.selectStockBatchGroupByMatGroup(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询批次库存（库存金额） 基于库存地点分组
     *
     * @param ctx ctx
     */
    public void selectStockBatchGroupByLocation(BizContext ctx) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBatchVO> result;

        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        result = bizReportMapper.selectStockBatchGroupByLocation(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询批次库存（库存金额）基于仓库号分组
     *
     * @param ctx ctx
     */
    public void selectStockBatchGroupByWh(BizContext ctx) {

        StockBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBatchVO> result;

        IPage<StockBatchVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBatchVO.class);
        }
        result = bizReportMapper.selectStockBatchGroupByWh(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBatchVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    @Autowired
    protected BizLabelDataDataWrap labelDataDataWrap;

    /**
     * 查询仓位库存详情
     *
     * @param ctx ctx
     */
    public void selectStockBinDetail(BizContext ctx, boolean exportFlag) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setIsUnitized(false);
        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectStockBinDetail(page, po);

        if (page != null) {
            totalCount = page.getTotal();
        }
        if (!exportFlag) {
            if(UtilCollection.isNotEmpty(result)){
                // 设置标签

                List<StockBinDTO> dtoList = UtilCollection.toList(result, StockBinDTO.class);

                List<BizLabelData> labelDataList = labelDataDataWrap.selectByStockBinList(dtoList);
                List<BizLabelDataDTO> bizLabelDataDTOList = UtilCollection.toList(labelDataList, BizLabelDataDTO.class);
                dataFillService.fillAttr(bizLabelDataDTOList);
                if (UtilCollection.isNotEmpty(bizLabelDataDTOList)) {
                    Map<String,
                            List<BizLabelDataDTO>> labelDataMap = bizLabelDataDTOList.stream()
                            .collect(Collectors.groupingBy(e -> e.getMatId() + Const.HYPHEN + e.getFtyId() + Const.HYPHEN + e.getLocationId()
                                    + Const.HYPHEN + e.getBatchId() + Const.HYPHEN + e.getWhId() + Const.HYPHEN + e.getTypeId() + Const.HYPHEN + e.getBinId()
                                    + Const.HYPHEN + e.getCellId()));
                    for (StockBinVO stockBinDTO : result) {
                        String key = stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId()
                                + Const.HYPHEN + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId()
                                + Const.HYPHEN + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                        List<BizLabelDataDTO> innerLabelDataList = labelDataMap.get(key);
                        stockBinDTO.setLabelDataList(innerLabelDataList);

                    }
                }
            }

            Set<Long> batchIds = result.stream().map(StockBinVO::getBatchId).collect(Collectors.toSet());

            if (UtilCollection.isNotEmpty(batchIds)) {
                // 设置批次图片
                List<BizBatchImg> batchImgs = bizBatchImgDataWrap.list(new LambdaQueryWrapper<BizBatchImg>()
                        .in(BizBatchImg::getBatchId, batchIds)
                );
                Map<Long, List<BizBatchImg>> imgMap = batchImgs.stream().collect(Collectors.groupingBy(BizBatchImg::getBatchId));
                result.forEach(p -> p.setBizBatchImgDTOList(UtilCollection.toList(imgMap.get(p.getBatchId()), BizBatchImgDTO.class)));

                // 设置批次附件
                List<BizCommonReceiptAttachment> attachmentList = bizCommonReceiptAttachmentDataWrap.list(
                        new LambdaQueryWrapper<BizCommonReceiptAttachment>()
                                .in(BizCommonReceiptAttachment::getBatchId, batchIds));
                Map<Long, List<BizCommonReceiptAttachment>> attachmentFileMap = attachmentList.stream().collect(Collectors.groupingBy(BizCommonReceiptAttachment::getBatchId));

                result.forEach(p -> p.setBatchAttachmentFileList(attachmentFileMap.get(p.getBatchId())));
            }
        }
        dataFillService.fillAttr(result);

        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出仓位库存Excel
     *
     * @param ctx 上下文
     */
    public void exportStockBinDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("仓位库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockBinDetail(ctx, true);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<StockBinVO> list = vo.getResultList();
        String langCode = this.getLangCodeFromRequest();
        for (StockBinVO exportVo : list) {
            exportVo.setPurchaseCode(exportVo.getBatchInfo().getPurchaseCode());
            exportVo.setPurchaseRid(exportVo.getBatchInfo().getPurchaseRid());
            exportVo.setContractCode(exportVo.getBatchInfo().getContractCode());
            if (UtilNumber.isNotEmpty(exportVo.getSubFirstParty())) {
                exportVo.setSubFirstPartyI18n(i18nTextCommonService.getNameMessage(langCode, "subFirstParty", exportVo.getSubFirstParty().toString()));
            }
        }
        UtilExcel.writeExcel(StockBinVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    /**
     * 查询仓位库存（库存金额） 基于仓库号分组
     *
     * @param ctx ctx
     */
    public void selectStockBinGroupByWh(BizContext ctx) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        result = bizReportMapper.selectStockBinGroupByWh(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询仓位库存（库存金额）基于存储类型分组
     *
     * @param ctx ctx
     */
    public void selectStockBinGroupByType(BizContext ctx) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        result = bizReportMapper.selectStockBinGroupByType(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询仓位库存（库存金额）基于存储区分组
     *
     * @param ctx ctx
     */
    public void selectStockBinGroupBySection(BizContext ctx) {

        StockBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinVO> result;

        IPage<StockBinVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinVO.class);
        }
        result = bizReportMapper.selectStockBinGroupBySection(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 全系统的库存积压分析
     *
     * @param ctx ctx
     */
    public void selectStockAnalyse(BizContext ctx) {

        StockAgeAnalysePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockAgeAnalyseVO> result;

        LocalDate currentDate = LocalDate.now();
        List<EnumStockAgeType> ageTypeList = Arrays.asList(EnumStockAgeType.values());
        for (EnumStockAgeType ageType : ageTypeList) {
            if (ageType.getEndDay() != null) {
                ageType.setStartDate(currentDate.plusDays(-ageType.getEndDay()));

            }
            ageType.setEndDate(currentDate.plusDays(-ageType.getStartDay() + 1));

        }

        po.setAgeTypeList(ageTypeList);
        result = bizReportMapper.selectStockAnalyse(po);
        if (result != null) {
            BigDecimal totalMoney;
            totalMoney = result.stream().map(StockAgeAnalyseVO::getStockMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal currentPercent = BigDecimal.ZERO;
            for (int i = 0; i < result.size(); i++) {
                StockAgeAnalyseVO analyseVO = result.get(i);
                if (i == result.size() - 1) {
                    analyseVO.setPercent(BigDecimal.ONE.subtract(currentPercent));
                } else {
                    analyseVO.setPercent(analyseVO.getStockMoney().divide(totalMoney, 3, RoundingMode.HALF_UP));
                    currentPercent = currentPercent.add(analyseVO.getPercent());
                }
            }
        }

        MultiResultVO<StockAgeAnalyseVO> vo = new MultiResultVO<>(result);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 基于仓库维度 查询存在积压超过1年的数据
     *
     * @param ctx ctx
     */
    public void selectStockAnalyseGroupByWh(BizContext ctx) {

        StockAgeAnalysePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockAgeAnalyseVO> result;

        LocalDate currentDate = LocalDate.now();
        List<EnumStockAgeType> ageTypeList = new ArrayList<>();
        ageTypeList.add(EnumStockAgeType.ENUM_STOCK_AGE_TYPE_FOUR);
        for (EnumStockAgeType ageType : ageTypeList) {
            if (ageType.getEndDay() != null) {
                ageType.setStartDate(currentDate.plusDays(-ageType.getEndDay()));
            }
            ageType.setEndDate(currentDate.plusDays(-ageType.getStartDay() + 1));
        }

        po.setAgeTypeList(ageTypeList);
        result = bizReportMapper.selectStockAnalyseGroupByWh(po);
        result = result.stream().filter(e -> EnumStockAgeType.ENUM_STOCK_AGE_TYPE_FOUR.getStockAgeType().equals(e.getStockAgeType()))
                .collect(Collectors.toList());

        MultiResultVO<StockAgeAnalyseVO> vo = new MultiResultVO<>(result);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 基于库存地点维度的库存积压分析
     *
     * @param ctx ctx
     */
    public void selectStockAnalyseGroupByLocation(BizContext ctx) {

        StockAgeAnalysePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockAgeAnalyseVO> result;

        List<StockAgeAnalyseLocationVO> locationAnalyseResult = new ArrayList<>();

        LocalDate currentDate = LocalDate.now();
        List<EnumStockAgeType> ageTypeList = Arrays.asList(EnumStockAgeType.values());
        for (EnumStockAgeType ageType : ageTypeList) {
            if (ageType.getEndDay() != null) {
                ageType.setStartDate(currentDate.plusDays(-ageType.getEndDay()));
            }
            ageType.setEndDate(currentDate.plusDays(-ageType.getStartDay() + 1));
        }

        po.setAgeTypeList(ageTypeList);
        result = bizReportMapper.selectStockAnalyseGroupByLocation(po);
        // 根据库存地点id分组
        Map<Long, List<StockAgeAnalyseVO>> locationAnalyseMap = result.stream().collect(Collectors.groupingBy(StockAgeAnalyseVO::getLocationId));

        for (Long locationId : locationAnalyseMap.keySet()) {
            StockAgeAnalyseLocationVO stockAnalyseLocationVO = new StockAgeAnalyseLocationVO();
            stockAnalyseLocationVO.setStockAnalyseVOList(locationAnalyseMap.get(locationId));
            if (UtilCollection.isNotEmpty(stockAnalyseLocationVO.getStockAnalyseVOList())) {
                StockAgeAnalyseVO vo = stockAnalyseLocationVO.getStockAnalyseVOList().get(0);
                stockAnalyseLocationVO.setFtyCode(vo.getFtyCode());
                stockAnalyseLocationVO.setFtyName(vo.getFtyName());
                stockAnalyseLocationVO.setLocationCode(vo.getLocationCode());
                stockAnalyseLocationVO.setLocationName(vo.getLocationName());
                // 计算百分比
                BigDecimal totalMoney;
                totalMoney = stockAnalyseLocationVO.getStockAnalyseVOList().stream().map(StockAgeAnalyseVO::getStockMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                stockAnalyseLocationVO.setStockMoney(totalMoney);
                BigDecimal currentPercent = BigDecimal.ZERO;
                for (int i = 0; i < stockAnalyseLocationVO.getStockAnalyseVOList().size(); i++) {
                    StockAgeAnalyseVO analyseVO = stockAnalyseLocationVO.getStockAnalyseVOList().get(i);
                    if (i == stockAnalyseLocationVO.getStockAnalyseVOList().size() - 1) {
                        analyseVO.setPercent(BigDecimal.ONE.subtract(currentPercent));
                    } else {
                        analyseVO.setPercent(totalMoney.compareTo(BigDecimal.ZERO) == BigDecimal.ROUND_UP ? BigDecimal.ZERO : analyseVO.getStockMoney().divide(totalMoney, 3, RoundingMode.HALF_UP));
                        currentPercent = currentPercent.add(analyseVO.getPercent());
                    }
                }
                Map<String, StockAgeAnalyseVO> stockAgeAnalyseVOMap =
                        stockAnalyseLocationVO.getStockAnalyseVOList().stream().collect(Collectors.toMap(StockAgeAnalyseVO::getStockAgeType, e -> e));
                ArrayList<StockAgeAnalyseVO> fomatList = new ArrayList<>();
                for (EnumStockAgeType ageType : ageTypeList) {
                    if (stockAgeAnalyseVOMap.containsKey(ageType.getStockAgeType())) {
                        fomatList.add(stockAgeAnalyseVOMap.get(ageType.getStockAgeType()));
                    } else {
                        StockAgeAnalyseVO fomatVO = new StockAgeAnalyseVO();
                        fomatVO.setStockAgeType(ageType.getStockAgeType());

                        fomatVO.setPercent(BigDecimal.ZERO);
                        fomatVO.setFtyCode(stockAnalyseLocationVO.getFtyCode());
                        fomatVO.setFtyName(stockAnalyseLocationVO.getFtyName());
                        fomatVO.setLocationCode(stockAnalyseLocationVO.getLocationCode());
                        fomatVO.setLocationName(stockAnalyseLocationVO.getLocationName());
                        fomatVO.setLocationId(locationId);
                        fomatList.add(fomatVO);
                    }
                }
                stockAnalyseLocationVO.setStockAnalyseVOList(fomatList);

            }
            locationAnalyseResult.add(stockAnalyseLocationVO);

        }

        MultiResultVO<StockAgeAnalyseLocationVO> vo = new MultiResultVO<>(locationAnalyseResult);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 业务凭证查询
     *
     * @param ctx ctx
     */
    public void selectStockInsDocBatch(BizContext ctx) {

        StockInsDocBatchSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 处理关联单据

        List<StockInsDocBatchVO> result = new ArrayList<>();
        IPage<StockInsDocBatchVO> page = null;
        Long totalCount = null;

        if (po == null) {
            PageObjectVO<StockInsDocBatchVO> vo = new PageObjectVO<>(result, totalCount);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
            return;
        }


        if (po.isPaging()) {
            page = po.getPageObj(StockInsDocBatchVO.class);
        }
        result = bizReportMapper.selectStockInsDocBatch(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        dataFillService.fillRlatAttrDataList(result);

        PageObjectVO<StockInsDocBatchVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 业务凭证查询导出Excel
     *
     * @param ctx 上下文
     */
    public void exportStockInsDocBatch(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("业务凭证"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockInsDocBatch(ctx);
        PageObjectVO<StockInsDocBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockInsDocBatchVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    /**
     * 将参考单据转换为id
     *
     * @param ctx ctx
     */
    public void referReceiptHandler(BizContext ctx) {
        Object po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po == null) {
            return;
        }

        String purchaseReceiptCode = UtilReflect.getValueByField("purchaseReceiptCode", po);
        String saleReceiptCode = UtilReflect.getValueByField("saleReceiptCode", po);
        String productionReceiptCode = UtilReflect.getValueByField("productionReceiptCode", po);
        String reserveReceiptCode = UtilReflect.getValueByField("reserveReceiptCode", po);

        Integer referReceiptType = null;
        Long referReceiptHeadId = null;
        int count = 0;
        if (UtilString.isNotNullOrEmpty(purchaseReceiptCode)) {
            count += 1;
        }
        if (UtilString.isNotNullOrEmpty(saleReceiptCode)) {
            count += 1;
        }
        if (UtilString.isNotNullOrEmpty(productionReceiptCode)) {
            count += 1;
        }
        if (UtilString.isNotNullOrEmpty(reserveReceiptCode)) {
            count += 1;
        }
        if (count > 1) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
        }

        // 采购订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(purchaseReceiptCode)) {
            QueryWrapper<ErpPurchaseReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpPurchaseReceiptHead::getReceiptCode, purchaseReceiptCode);
            ErpPurchaseReceiptHead head = erpPurchaseReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }

        // 销售订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(saleReceiptCode)) {
            QueryWrapper<ErpSaleReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpSaleReceiptHead::getReceiptCode, saleReceiptCode);
            ErpSaleReceiptHead head = erpSaleReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }
        // 预留订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(reserveReceiptCode)) {
            QueryWrapper<ErpReserveReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpReserveReceiptHead::getReceiptCode, reserveReceiptCode);
            ErpReserveReceiptHead head = erpReserveReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }
        // 生产订单号转referReceiptHeadId
        if (UtilString.isNotNullOrEmpty(productionReceiptCode)) {
            QueryWrapper<ErpProductionReceiptHead> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ErpProductionReceiptHead::getReceiptCode, productionReceiptCode);
            ErpProductionReceiptHead head = erpProductionReceiptHeadDataWrap.getOne(wrapper);
            if (head != null) {
                referReceiptHeadId = head.getId();
            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }
        }
        UtilReflect.setValueByField(po, "referReceiptType", referReceiptType);
        UtilReflect.setValueByField(po, "referReceiptHeadId", referReceiptHeadId);

    }

    /**
     * 将前置单据号转为前置单据id
     *
     * @param ctx ctx
     */
    public void preReceiptHandler(BizContext ctx) {
        Object po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        String preReceiptCode = UtilReflect.getValueByField("preReceiptCode", po);

        if (UtilString.isNotNullOrEmpty(preReceiptCode)) {
            String tableName = null;
            // 前置单据号转 preReceiptHeadId
            List<SysSequencePrefixVO> prefixVOList = sequenceMapper.selectAllPrefix();

            for (SysSequencePrefixVO prefixVO : prefixVOList) {
                if (UtilString.isNotNullOrEmpty(prefixVO.getPrefix())) {
                    String comparStr = preReceiptCode.substring(0, prefixVO.getPrefix().length());
                    if (comparStr.equalsIgnoreCase(prefixVO.getPrefix())) {
                        tableName = prefixVO.getTableName();
                        break;
                    }
                }
            }
            if (UtilString.isNotNullOrEmpty(tableName)) {
                IService mPlusService = MetadataContext.getMplusService(tableName);

                if (mPlusService != null) {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("receipt_code", preReceiptCode);
                    Object preObj = mPlusService.getOne(queryWrapper);
                    if (preObj != null) {

                        Long preReceiptHeadId = UtilReflect.getValueByField("id", preObj);

                        UtilReflect.setValueByField(po, "preReceiptHeadId", preReceiptHeadId);
                    } else {
                        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
                    }
                } else {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
                }

            } else {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, null);
            }

        }

    }

    /**
     * 作业量统计
     *
     * @param ctx ctx
     */
    public void selectTask(BizContext ctx) {

        TaskSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 处理关联单据

        List<TaskVO> result = new ArrayList<>();
        IPage<TaskVO> page = null;
        Long totalCount = null;

        if (po == null) {
            PageObjectVO<TaskVO> vo = new PageObjectVO<>(result, totalCount);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
            return;
        }


        if (po.isPaging()) {
            page = po.getPageObj(TaskVO.class);
        }
        result = bizReportMapper.selectTask(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        dataFillService.fillRlatAttrDataList(result);

        PageObjectVO<TaskVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 作业查询导出Excel
     *
     * @param ctx 上下文
     */
    public void exportTask(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("作业单"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectTask(ctx);
        PageObjectVO<TaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(TaskVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    /**
     * 库存对账 全局对账
     * 结果用于库存差异
     */
    public void stockDiff() {
        // 同步ERP库存到erp_stock_batch表中
        bizReportMapper.deleteInsStockToDiff();

        // 根据条件查询 erp库存 存入 erp_stock_diff 中
        bizReportMapper.insertErpStockToDiff(null);
        // 根据 fty_id, location_id, mat_id, spec_stock, spec_stock_code, batch_erp group by 查询 instock 库存 存入 erp_stock_diff 中

        // 查询仓位库存 插入到erp_stock_diff中 查询与插入分开 不建议使用 insert select ，防止锁stock_batch表
        Collection<DicStockLocationDTO> locationDTOS = dictionaryService.getAllLocationCache();

        for (DicStockLocationDTO locationDTO : locationDTOS) {
            List<StockDiffVO> insStockList = bizReportMapper.getInsStockByLocationId(locationDTO.getId());

            if (UtilCollection.isNotEmpty(insStockList)) {
                UtilDb.insertData(insStockList, bizReportMapper::insertInsStockToDiff, 5);
            }

        }

    }

    /**
     * 查询库存差异
     *
     * @param ctx ctx
     */
    public void selectStockDiff(BizContext ctx) {
        StockDiffSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockDiffVO> result = new ArrayList<>();
        IPage<StockDiffVO> page = null;
        Long totalCount = null;

        if (po == null) {
            PageObjectVO<StockDiffVO> vo = new PageObjectVO<>(result, totalCount);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
            return;
        }


        if (po.isPaging()) {
            page = po.getPageObj(StockDiffVO.class);
        }
        result = bizReportMapper.selectStockDiff(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }


        PageObjectVO<StockDiffVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 库存对账查询导出Excel
     *
     * @param ctx 上下文
     */
    public void exportStockDiff(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存对账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockDiff(ctx);
        PageObjectVO<StockDiffVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockDiffVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 库存金额分析 （erp库存）
     *
     * @param ctx ctx
     */
    public void erpStockMoneyAnalyse(BizContext ctx) {

        List<StockMoneyAnalyseVO> result = bizReportMapper.selectErpStockMoney();

        MultiResultVO<StockMoneyAnalyseVO> vo = new MultiResultVO<>(result);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询出入库记录 （最近7天）
     *
     * @param ctx ctx
     */
    public void selectInAndOut(BizContext ctx) {
        InAndOutAnalysePO po = new InAndOutAnalysePO();
        po.setEndDate(LocalDate.now());
        po.setStartDate(po.getEndDate().plusDays(-7));

        List<InAndOutAnalyseVO> result = bizReportMapper.selectInAndOut(po);

        // 将未查出的数据处理
        Map<String, InAndOutAnalyseVO> resultMap = result.stream().collect(Collectors.toMap(InAndOutAnalyseVO::getCreateDate, e -> e));
        List<InAndOutAnalyseVO> returnList = new ArrayList<>();

        for (int i = 0; i < 7; i++) {
            String date = po.getStartDate().plusDays(i).toString();
            if (resultMap.containsKey(date)) {
                returnList.add(resultMap.get(date));

            } else {
                InAndOutAnalyseVO inner = new InAndOutAnalyseVO();
                inner.setCreateDate(date);
                inner.setInNum(0);
                inner.setOutNum(0);
                returnList.add(inner);

            }
        }

        MultiResultVO<InAndOutAnalyseVO> vo = new MultiResultVO<>(returnList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 库存周转率报表 最近半年
     *
     * @param ctx ctx
     */
    public void selectStockTurnover(BizContext ctx) {
        StockTurnoverPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if (po == null) {
            po = new StockTurnoverPO();

        }
        LocalDate end = LocalDate.now().plusMonths(-1);

        LocalDate start = end.plusMonths(-5);


        po.setEndMonth(UtilLocalDateTime.getStringMonthForLocalDate(end));

        po.setStartMonth(UtilLocalDateTime.getStringMonthForLocalDate(start));

        List<StockTurnoverVO> result = bizReportMapper.selectStockTurnover(po);


        MultiResultVO<StockTurnoverVO> vo = new MultiResultVO<>(result);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 定时任务 每月统计 一次 期初库存 期末库存 月入金额 月出金额
     * 每月月底 执行一次
     * 项目上线时 导入一次期初库存
     */
    public void collectStockTurnover() {

        Collection<DicStockLocationDTO> locationDTOS = dictionaryService.getAllLocationCache();

        // 本月期末
        LocalDate endingMonth = LocalDate.now();

        // 下个月期初
        LocalDate beginningMonth = endingMonth.plusMonths(1);

        String year = endingMonth.getYear() + Const.STRING_EMPTY;
        String beginMonth = UtilLocalDateTime.getStringMonthForLocalDate(beginningMonth);
        String endMonth = UtilLocalDateTime.getStringMonthForLocalDate(endingMonth);

        for (DicStockLocationDTO locationDTO : locationDTOS) {
            List<ErpStockBatch> stockBatchList = bizReportMapper.selectStockByLocationId(locationDTO.getId());

            // erp_stock_batch计算期末库存 并插入下个月的期初库存入到erp_stock_turnover表中
            if (UtilCollection.isNotEmpty(stockBatchList)) {
                List<ErpStockTurnover> erpStockTurnoverList = new ArrayList<>();
                for (ErpStockBatch stockBatch : stockBatchList) {
                    ErpStockTurnover begin = new ErpStockTurnover();
                    begin.setId(UtilSequence.nextId());
                    begin.setYear(year);
                    begin.setMonth(beginMonth);
                    begin.setMatId(stockBatch.getMatId());
                    begin.setFtyId(stockBatch.getFtyId());
                    begin.setLocationId(stockBatch.getLocationId());
                    begin.setBeginningBalance(stockBatch.getMoney());
                    begin.setEndingBalance(BigDecimal.ZERO);
                    begin.setMonthInMoney(BigDecimal.ZERO);
                    begin.setMonthOutMoney(BigDecimal.ZERO);

                    ErpStockTurnover end = new ErpStockTurnover();
                    end.setId(UtilSequence.nextId());
                    end.setYear(year);
                    end.setMonth(endMonth);
                    end.setMatId(stockBatch.getMatId());
                    end.setFtyId(stockBatch.getFtyId());
                    end.setLocationId(stockBatch.getLocationId());
                    end.setBeginningBalance(BigDecimal.ZERO);
                    end.setEndingBalance(stockBatch.getMoney());
                    end.setMonthInMoney(BigDecimal.ZERO);
                    end.setMonthOutMoney(BigDecimal.ZERO);

                    erpStockTurnoverList.add(begin);
                    erpStockTurnoverList.add(end);

                }

                // 计算月入，月出 金额 插入到erp_stock_turnover表中
                List<ErpMatDoc> matDocList = bizReportMapper.selectInAndOutMoney(endMonth, locationDTO.getId());
                for (ErpMatDoc matDoc : matDocList) {
                    ErpStockTurnover stockTurnover = new ErpStockTurnover();
                    stockTurnover.setId(UtilSequence.nextId());
                    stockTurnover.setYear(year);
                    stockTurnover.setMonth(endMonth);
                    stockTurnover.setMatId(matDoc.getMatId());
                    stockTurnover.setFtyId(matDoc.getFtyId());
                    stockTurnover.setLocationId(matDoc.getLocationId());
                    stockTurnover.setBeginningBalance(BigDecimal.ZERO);
                    stockTurnover.setEndingBalance(BigDecimal.ZERO);
                    if (matDoc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT)) {
                        stockTurnover.setMonthOutMoney(matDoc.getMatDocMoney());
                        stockTurnover.setMonthInMoney(BigDecimal.ZERO);
                    } else if (matDoc.getDebitCredit().equals(Const.DEBIT_S_ADD)) {
                        stockTurnover.setMonthInMoney(matDoc.getMatDocMoney());
                        stockTurnover.setMonthOutMoney(BigDecimal.ZERO);
                    }

                    erpStockTurnoverList.add(stockTurnover);
                }

                UtilDb.insertData(erpStockTurnoverList, bizReportMapper::insertIntoStockTurnOver, 500);

            }
        }

    }

    /**
     * pda 查询仓位库存
     *
     * @param ctx ctx
     */
    public void getPdaStockBin(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        StockBinPdaSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po || UtilString.isNullOrEmpty(po.getLabelType()) || UtilString.isNullOrEmpty(po.getLabelSequence()) || UtilString
                .isNullOrEmpty(po.getResType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 库存搜索条件
        StockBinPO stockBinPo = new StockBinPO();
        // 查询非限制库存
        stockBinPo.setStockStatusSet(new HashSet<String>() {{
            add(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue().toString());
        }});
        //标签类型
        switch (po.getLabelType()) {
            // 物料标签
            case Const.LABEL_SORT_MAT:
                if (UtilString.isNullOrEmpty(po.getMaterialArgs())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
                /* ****************** 请求类型 ******************/
                // 扫描：01【按id查询】
                if (TaskConst.TYPE_SCAN.equals(po.getResType())) {
                    switch (po.getMaterialArgs()) {
                        case TaskConst.BATCH:
                            stockBinPo.setBatchId(Long.valueOf(po.getLabelSequence()));
                            break;
                        case TaskConst.MATERIAL:
                            stockBinPo.setMatId(Long.valueOf(po.getLabelSequence()));
                            break;
                        case TaskConst.LABEL:
                            List<BizLabelDataDTO> labelDataDTOList = labelDataService.listById(new ArrayList<Long>() {{
                                add(Long.valueOf(po.getLabelSequence()));
                            }});
                            if (UtilCollection.isEmpty(labelDataDTOList)) {
                                return;
                            }
                            if (labelDataDTOList.size() > 1) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                            }
                            BizLabelDataDTO labelDataDTO = labelDataDTOList.get(0);
                            stockBinPo.setFtyId(labelDataDTO.getFtyId());
                            stockBinPo.setLocationId(labelDataDTO.getLocationId());
                            stockBinPo.setWhId(labelDataDTO.getWhId());
                            stockBinPo.setMatId(labelDataDTO.getMatId());
                            stockBinPo.setTypeId(labelDataDTO.getTypeId());
                            stockBinPo.setBatchId(labelDataDTO.getBatchId());
                            stockBinPo.setBinId(labelDataDTO.getBinId());
                            break;
                    }
                }
                // 手输：02【按code或name查询】
                if (TaskConst.TYPE_MANUAL.equals(po.getResType())) {
                    switch (po.getMaterialArgs()) {
                        case TaskConst.BATCH:
                            // 按批次code查询批次
                            BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDtoByCode(po.getLabelSequence());
                            if (null == batchInfoDTO) {
                                return;
                            }
                            stockBinPo.setBatchId(batchInfoDTO.getId());
                            break;
                        case TaskConst.MATERIAL:
                            // 按物料code或name查询
                            QueryWrapper<DicMaterial> queryWrapper = new QueryWrapper<>();
                            queryWrapper.lambda().like(DicMaterial::getMatCode, po.getLabelSequence()).or()
                                    .like(DicMaterial::getMatName, po.getLabelSequence());
                            // 物料查询
                            List<DicMaterial> materialList = materialService.getMaterialList(queryWrapper);
                            if (UtilCollection.isNotEmpty(materialList)) {
                                stockBinPo
                                        .setMatIdList(new ArrayList<>(materialList.parallelStream().map(DicMaterial::getId).collect(Collectors.toSet())));
                            } else {
                                return;
                            }
                            break;
                        case TaskConst.LABEL:
                            // 按标签code查询
                            List<BizLabelDataDTO> labelDataDTOList = labelDataService.LabelDataListByCode(new ArrayList<String>() {{
                                add(po.getLabelSequence());
                            }});
                            if (UtilCollection.isEmpty(labelDataDTOList)) {
                                return;
                            }
                            if (labelDataDTOList.size() > 1) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                            }
                            BizLabelDataDTO labelDataDTO = labelDataDTOList.get(0);
                            stockBinPo.setFtyId(labelDataDTO.getFtyId());
                            stockBinPo.setLocationId(labelDataDTO.getLocationId());
                            stockBinPo.setWhId(labelDataDTO.getWhId());
                            stockBinPo.setMatId(labelDataDTO.getMatId());
                            stockBinPo.setTypeId(labelDataDTO.getTypeId());
                            stockBinPo.setBatchId(labelDataDTO.getBatchId());
                            stockBinPo.setBinId(labelDataDTO.getBinId());
                            break;
                    }
                }
                break;
            // 仓位标签
            case Const.LABEL_SORT_BIN:
                /* ****************** 请求类型 ******************/
                // 扫描：01【按id查询】
                if (TaskConst.TYPE_SCAN.equals(po.getResType())) {
                    stockBinPo.setBinId(Long.valueOf(po.getLabelSequence()));
                }
                // 手输：02【按code查询】
                if (TaskConst.TYPE_MANUAL.equals(po.getResType())) {
                    // 按仓位code查询
                    QueryWrapper<DicWhStorageBin> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().like(DicWhStorageBin::getBinCode, po.getLabelSequence());
                    // 物料查询
                    List<DicWhStorageBin> storageBinList = whStorageBinService.getWhStorageBinList(queryWrapper);
                    if (UtilCollection.isNotEmpty(storageBinList)) {
                        stockBinPo
                                .setBinIdList(new ArrayList<>(storageBinList.parallelStream().map(DicWhStorageBin::getId).collect(Collectors.toSet())));
                    } else {
                        return;
                    }
                }
                break;
        }

        List<StockBinDTO> stockBinDTOList = stockCommonService.getStockBinByStockBinPo(stockBinPo);
        log.info("获取扫描码的仓位库存-标签的库存信息 stockBinDTOList：{}", JSONObject.toJSONString(stockBinDTOList));
        if (UtilCollection.isNotEmpty(stockBinDTOList)) {
            // 仓位库存批次id集合
            Set<Long> batchIdSet = stockBinDTOList.stream().map(StockBinDTO::getBatchId).collect(Collectors.toSet());
            // 获取批次图片
            Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
            // 标签数据 根据batchId binId cellId 查询 对应标签
            List<BizLabelData> labelDataList = labelDataService.selectByStockBinList(stockBinDTOList);
            log.info("获取扫描码的仓位库存-标签的labelData信息 labelDataList：{}", JSONObject.toJSONString(labelDataList, SerializerFeature.IgnoreNonFieldGetter));
            Map<String, List<BizLabelData>> labelDataMap = null;
            if (UtilCollection.isNotEmpty(labelDataList)) {
                labelDataMap = labelDataList.stream().collect(Collectors.groupingBy(
                        label -> label.getMatId() + Const.HYPHEN + label.getFtyId() + Const.HYPHEN + label.getLocationId() + Const.HYPHEN + label
                                .getBatchId() + Const.HYPHEN + label.getWhId() + Const.HYPHEN + label.getTypeId() + Const.HYPHEN + label.getBinId()
                                + Const.HYPHEN + label.getCellId()));
            }

            // 遍历物料仓位库存
            for (StockBinDTO stockBinDTO : stockBinDTOList) {

                // 批次图片
                stockBinDTO.setBatchImgList(imgMap.get(stockBinDTO.getBatchId()));
                // 标签列表
                if (null != labelDataMap) {
                    String key =
                            stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId() + Const.HYPHEN
                                    + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId() + Const.HYPHEN
                                    + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                    stockBinDTO.setLabelDataList(labelDataMap.get(key));
                    if (UtilCollection.isNotEmpty(stockBinDTO.getLabelDataList())) {
                        stockBinDTO
                                .setStockQty(stockBinDTO.getLabelDataList().stream().map(BizLabelData::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {

                        stockBinDTO.setStockQty(
                                stockBinDTO.getQty().add(stockBinDTO.getQtyTemp()).add(stockBinDTO.getQtyFreeze()).add(stockBinDTO.getQtyInspection())
                                        .add(stockBinDTO.getQtyTransfer()).add(stockBinDTO.getQtyHaste()));
                    }
                }

            }
        }

        MultiResultVO<StockBinDTO> vo = new MultiResultVO<>(stockBinDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);

        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 查询重量库存详情
     *
     * @param ctx ctx
     */
    public void selectStockBinWeightDetail(BizContext ctx) {
        StockBinWeightSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<StockBinWeightVo> result;

        IPage<StockBinWeightVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockBinWeightVo.class);
        }
        result = bizReportMapper.selectStockBinWeightDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<StockBinWeightVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 重量库存导出
     *
     * @param ctx
     */
    public void exportStockBinWeightDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存重量"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockBinWeightDetail(ctx);
        PageObjectVO<StockBinWeightVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockBinWeightVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 电子秤报表查询
     *
     * @param ctx 入参上下文 {@link ElectronicScalePO : 电子秤查询对象}
     */
    public void selectElectronicScaleRecord(BizContext ctx) {
        // 入参上下文
        ElectronicScalePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<ElectronicScaleVO> result;

        IPage<ElectronicScaleVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ElectronicScaleVO.class);
        }
        result = bizReportMapper.selectElectronicScaleRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ElectronicScaleVO> vo = new PageObjectVO<>(result, totalCount);

        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }



    /**
     * 入库台账详情
     *
     * @param ctx ctx
     */
    public void selecInputLedgerRecord(BizContext ctx) {
        InputLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

    
        List<InputLedgerVo> result;

        IPage<InputLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(InputLedgerVo.class);
        }
       
        result = bizReportMapper.selectInputLedgerRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        
        dataFillService.fillAttr(result);

        PageObjectVO<InputLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    
    /**
     * 入库台账导出
     *
     * @param ctx
     */
    public void exportInputLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("入库台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selecInputLedgerRecord(ctx);
        PageObjectVO<InputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        String langCode = this.getLangCodeFromRequest();
        List<InputLedgerVo> list = vo.getResultList();
        for (InputLedgerVo exportVo : list) {
            if (UtilNumber.isNotEmpty(exportVo.getDeliveryAddress())) {
                exportVo.setDeliveryAddressI18n(i18nTextCommonService.getNameMessage(langCode, "deliveryAddress", exportVo.getDeliveryAddress().toString()));
                exportVo.setSendTypeI18n(i18nTextCommonService.getNameMessage(langCode,"sendType",UtilObject.getStringOrEmpty(exportVo.getSendType())));
            }
        }
        UtilExcel.writeExcel(InputLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }



    /**
     * 直抵现场导出详情
     *
     * @param ctx ctx
     */
    public void selectDirectSceneRecord(BizContext ctx) {
        DirectScenePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<DirectSceneVo> result;

        IPage<DirectSceneVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(DirectSceneVo.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectDirectSceneRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<DirectSceneVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 直抵现场导出
     *
     * @param ctx
     */
    public void exportDirectSceneRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("直抵现场台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectDirectSceneRecord(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 由于导出数据在国际化处理之前封装，故此处手工对文案内容进行映射处理
        for (DirectSceneVo directSceneVo : vo.getResultList()) {
            if (UtilNumber.isNotEmpty(directSceneVo.getReceiptStatus())) {
                if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已完成");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("作业中");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已作业");
                } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(directSceneVo.getReceiptStatus())) {
                    directSceneVo.setReceiptStatusI18n("已提交");
                }
            }
        }

        UtilExcel.writeExcel(DirectSceneVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 入库物资跟踪表详情
     *
     * @param ctx ctx
     */
    public void selectDeliveryTrackRecord(BizContext ctx) {
        DeliveryTrackPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<DeliveryTrackVo> result;

        IPage<DeliveryTrackVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(DeliveryTrackVo.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectDeliveryTrackRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<DeliveryTrackVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 入库物资跟踪表导出
     *
     * @param ctx
     */
    public void exportDeliveryTrackRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("直抵现场台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectDeliveryTrackRecord(ctx);
        PageObjectVO<InputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(DeliveryTrackVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    
    

    public void selectOutputLedgerRecord(BizContext ctx) {
        OutputLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<OutputLedgerVo> result;

        IPage<OutputLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(OutputLedgerVo.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectOutputLedgerRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (!CollectionUtils.isEmpty(result)) {
            String langCode = this.getLangCodeFromRequest();
            result.forEach(i -> {
                Integer isWriterOff = i.getIsWriteOff();
                String qtyStr = i.getQty();
//                BigDecimal money = i.getMoney();
                if (StringUtils.isNotBlank(qtyStr) && (isWriterOff != null) && isWriterOff.equals(EnumRealYn.FALSE.getIntValue())) {
                    BigDecimal qty = new BigDecimal(qtyStr);
                    if (qty.compareTo(BigDecimal.ZERO) > 0) {
                        i.setQty(qty.negate().toString());
//                        i.setMoney(money.negate());
                    }
                }
            });
        }
        PageObjectVO<OutputLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportOutputLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("出库台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectOutputLedgerRecord(ctx);
        PageObjectVO<OutputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        String langCode = this.getLangCodeFromRequest();
        for (OutputLedgerVo outputLedgerVo : vo.getResultList()) {
            outputLedgerVo.setInnerPlanStr("否");
            if(outputLedgerVo.getInnerPlan().equals("1")){
                outputLedgerVo.setInnerPlanStr("是");
            }
            outputLedgerVo.setReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", outputLedgerVo.getReceiptStatus().toString()));
            if (UtilNumber.isNotEmpty(outputLedgerVo.getPreReceiptStatus())) {
                outputLedgerVo.setPreReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", outputLedgerVo.getPreReceiptStatus().toString()));
            }
        }

        UtilExcel.writeExcel(OutputLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void selectReturnNewLedgerRecord(BizContext ctx) {
        ReturnNewLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<ReturnNewLedgerVo> result;

        IPage<ReturnNewLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ReturnNewLedgerVo.class);
        }
        result = bizReportMapper.selectReturnNewLedgerRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ReturnNewLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportReturnNewLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("退旧换新台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectReturnNewLedgerRecord(ctx);
        PageObjectVO<ReturnNewLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ReturnNewLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void selectToolLedgerRecord(BizContext ctx) {
        ToolLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<ToolLedgerVo> result;

        IPage<ToolLedgerVo> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ToolLedgerVo.class);
        }
        result = bizReportMapper.selectToolLedgerRecord(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectVO<ToolLedgerVo> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportToolLedgerRecord(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("工器具台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectToolLedgerRecord(ctx);
        PageObjectVO<ToolLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ToolLedgerVo.class, vo.getResultList(), bizCommonFile);

        // 推送MQ生成可下载文件数据
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询寿期台账详情
     *
     * @param ctx ctx
     */
    public void selectLifetimeDetail(BizContext ctx, boolean exportFlag) {

        LifetimeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<LifetimeVO> result;

        IPage<LifetimeVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(LifetimeVO.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectLifetimeDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (LifetimeVO lifetimeVO : result) {
                String packageTypeI18n = EnumPackageType.getDescByValue(lifetimeVO.getPackageType());
                String depositTypeI18n = EnumDepositType.getDescByValue(lifetimeVO.getDepositType());
                Integer inspectResult=lifetimeVO.getInspectResult();
                String inspectResultI18n = EnumInspectResult.getDescByValue(inspectResult);
                String scrapCauseI18n = EnumScrapFreezeCauseType.getDescByValue(lifetimeVO.getScrapCause());
                lifetimeVO.setPackageTypeI18n(packageTypeI18n);
                lifetimeVO.setDepositTypeI18n(depositTypeI18n);
                lifetimeVO.setInspectResultI18n(inspectResultI18n);
                lifetimeVO.setScrapCauseI18n(scrapCauseI18n);
                if(EnumInspectResult.DEMOTION.getValue().equals(inspectResult) || EnumInspectResult.SCRAP.getValue().equals(inspectResult)){
                    lifetimeVO.setDelayDate(null) ; //检定结果为“降级”或“报废”时，延期日期为空
                }
                if(EnumInspectResult.DELAY.getValue().equals(inspectResult) || EnumInspectResult.DEMOTION.getValue().equals(inspectResult)){
                    lifetimeVO.setScrapCause(null) ; //检定结果为“降级”或“延期”时，移动原因为空
                }
                String shelfLifeMax=lifetimeVO.getShelfLifeMax();
                if(StringUtils.isNotEmpty(shelfLifeMax)){
                    shelfLifeMax=shelfLifeMax+Const.MONTH_STR;
                }
                lifetimeVO.setShelfLifeMax(shelfLifeMax);

            }
        }

        PageObjectVO<LifetimeVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出寿期台账Excel
     *
     * @param ctx 上下文
     */
    public void exportLifetimeDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("寿期台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectLifetimeDetail(ctx, true);
        PageObjectVO<LifetimeVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(LifetimeVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询维保台账详情
     *
     * @param ctx ctx
     */
    public void selectMaintainDetail(BizContext ctx, boolean exportFlag) {

        MaintainSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<MaintainVO> result;

        IPage<MaintainVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(MaintainVO.class);
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null&&UtilCollection.isEmpty(po.getLocationIdList())){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
            po.setLocationIdList(locationIdList);
        }
        result = bizReportMapper.selectMaintainDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (MaintainVO maintainVO : result) {
                String packageTypeI18n = EnumPackageType.getDescByValue(maintainVO.getPackageType());
                String depositTypeI18n = EnumDepositType.getDescByValue(maintainVO.getDepositType());
                Integer maintenanceType=maintainVO.getMaintenanceType();
                String maintenanceTypeI18n = EnumMaintenanceType.getDescByValue(maintenanceType);
                maintainVO.setPackageTypeI18n(packageTypeI18n);
                maintainVO.setDepositTypeI18n(depositTypeI18n);
                maintainVO.setMaintenanceTypeI18n(maintenanceTypeI18n);
                Date maintenanceDateNormal=maintainVO.getMaintenanceDateNormal();
                Date maintenanceDatePro=maintainVO.getMaintenanceDatePro();
                Date maintenanceDateNext=null;
                String maintenanceProgram=""; //维护保养大纲  维保类型为“维护保养”时有值，其他两种类型为空
                String defectDescribe=""; //缺陷描述  维保类型为“库存状态检查”时有值，其他两种类型为空
                if(EnumMaintenanceType.DAILY_MAINTAIN.getValue().equals(maintenanceType) ){//包装更换
                    maintenanceDateNext=maintenanceDateNormal;
                }else if(EnumMaintenanceType.SPECIAL_MAINTAIN.getValue().equals(maintenanceType) ){//维护保养
                    maintenanceDateNext=maintenanceDatePro;
                    maintenanceProgram=maintainVO.getMaintenanceProgram();
                }else if(EnumMaintenanceType.DEFECT_MAINTAIN.getValue().equals(maintenanceType) ){//库存状态检查
                    defectDescribe=maintainVO.getDefectDescribe();
                }
                maintainVO.setMaintenanceDateNext(maintenanceDateNext);
                maintainVO.setMaintenanceProgram(maintenanceProgram);
                maintainVO.setDefectDescribe(defectDescribe);
            }
        }
        PageObjectVO<MaintainVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 导出维保台账Excel
     *
     * @param ctx 上下文
     */
    public void exportMaintainDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("维保台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectMaintainDetail(ctx, true);
        PageObjectVO<MaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(MaintainVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 查询退旧台账详情
     *
     * @param ctx ctx
     */
    public void selectReturnOldDetail(BizContext ctx, boolean exportFlag) {
        // 入参上下文
        ReturnOldSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        IPage<ReturnOldVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ReturnOldVO.class);
        }
        List<ReturnOldVO> result = bizReportMapper.selectReturnOldDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (ReturnOldVO returnOldVO : result) {
                String returnOldTypeI18n = EnumReturnOldType.getDescByValue(returnOldVO.getReturnOldType());
                returnOldVO.setReturnOldTypeI18n(returnOldTypeI18n);
                String estimateReturnOldTypeI18n = EnumReturnOldType.getDescByValue(returnOldVO.getEstimateReturnOldType());
                returnOldVO.setEstimateReturnOldTypeI18n(estimateReturnOldTypeI18n);
                switch (returnOldVO.getCreateType()) {
                    case 1: returnOldVO.setCreateTypeI18n("基于出库单入库"); break;
                    case 2: returnOldVO.setCreateTypeI18n("基于物料编码入库"); break;
                    default: returnOldVO.setCreateTypeI18n(Const.STRING_EMPTY); break;
                }
            }
        }
        PageObjectVO<ReturnOldVO> vo = new PageObjectVO<>(result, totalCount);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }
    /**
     * 导出退旧台账Excel
     * @param ctx 上下文
     */
    public void exportReturnOldDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("退旧台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectReturnOldDetail(ctx, true);
        PageObjectVO<ReturnOldVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ReturnOldVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void stockCompare(BizContext ctx) {
        StockCompareSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        IPage<StockCompareVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockCompareVO.class);
        }
        // 调用sap 收发存报表接口获取当月期末库存
        List<StockCompareVO> itemList = this.getSapStock(ctx);
        List<StockCompareVO> stockCompareVOS;
        PageObjectVO<StockCompareVO> vo;
        // 正向对比 sap - wms   不用分页
        if (po.getCompareType().equals(1)) {
            stockCompareVOS = bizReportMapper.selectStockCompareDetail(null);
            Map<String, StockCompareVO> sapMap = itemList.stream().collect(Collectors.toMap(StockCompareVO::getMatCode, Function.identity()));
            Map<String, StockCompareVO> wmsMap = stockCompareVOS.stream().collect(Collectors.toMap(StockCompareVO::getMatCode, Function.identity()));
            Set<String> sapMatCode = sapMap.keySet();
            Set<String> wmsMatCode = wmsMap.keySet();
            Set<String> differenceSet = Sets.union(sapMatCode, wmsMatCode);
            Map<String, StockCompareVO> result = Maps.newHashMap();
            for (String key : differenceSet) {
                if (sapMap.containsKey(key) && wmsMap.containsKey(key)) {
                    result.put(key, sapMap.get(key).setWmsQty(wmsMap.get(key).getWmsQty()));
                    continue;
                }
                if (sapMap.containsKey(key)) {
                    result.put(key, sapMap.get(key));
                    continue;
                }
                if (wmsMap.containsKey(key)) {
                    result.put(key, wmsMap.get(key));
                }

            }
            List<StockCompareVO> resultList = new ArrayList<>(result.values());
            resultList.forEach(c -> c.setDiffQty(c.getSapQty().subtract(c.getWmsQty())));
            // 对list 分页
            if (UtilCollection.isNotEmpty(resultList)) {
                if (po.isPaging()) {
                    vo = new PageObjectVO<>(Lists.partition(resultList, po.getPageSize()).get(po.getPageIndex() - 1), (long) resultList.size());
                } else {
                    vo = new PageObjectVO<>(resultList, (long) resultList.size());
                }
            } else {
                vo = new PageObjectVO<>(new ArrayList<>(), 0L);
            }

        } else {
            stockCompareVOS = bizReportMapper.selectStockCompareDetail(page);
            if (page != null) {
                totalCount = page.getTotal();
            }
            stockCompareVOS.forEach(c -> c.setSapQty(itemList.stream().filter(e -> c.getMatCode().equals(e.getMatCode())).findFirst().orElse(new StockCompareVO().setSapQty(BigDecimal.ZERO)).getSapQty()));
            stockCompareVOS.forEach(c -> c.setDiffQty(c.getWmsQty().subtract(c.getSapQty())));
            vo = new PageObjectVO<>(stockCompareVOS, totalCount);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);

    }

    /**
     * 导出库存对比结果Excel
     *
     * @param ctx 上下文
     */
    public void exportStockCompareDetail(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存对比结果"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.stockCompare(ctx);
        StockCompareSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        PageObjectVO<StockCompareVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // List<String> columns1 = Lists.newArrayList("ftyCode", "matCode", "matName", "unitCode", "sapQty", "wmsQty", "diffQty");
        // List<String> columns2 = Lists.newArrayList("ftyCode", "matCode", "matName", "unitCode", "wmsQty", "sapQty", "diffQty");
        // // 改变列顺序
        // if (po.getCompareType().equals(1)) {
        //     UtilExcel.setExcelIndex(StockCompareVO.class, columns1);
        // } else {
        //     UtilExcel.setExcelIndex(StockCompareVO.class, columns2);
        // }
        UtilExcel.writeExcel(StockCompareVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    private List<StockCompareVO> getSapStock(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), Const.STRING_EMPTY, SapConst.TYPE_TWO);
        params.put("callerLong", "1");
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IT_MATNR", new JSONArray());
        params.put("IV_WERKS", EnumFactory.J047.getFtyCode());
        params.put("IV_SPMON", UtilDate.getStringDateForDate(new Date(), DateTimeFormatter.ofPattern("yyyyMM")));
        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_JXCBB);
        log.debug("SAP收发存库存入参：" + params);
        JSONObject returnObject = sapApiCallProxy.callSapApi(erpUrl, params);
        log.debug("SAP收发存库存出参：" + params);
        List<StockCompareVO> itemList = Lists.newArrayList();
        if (returnObject != null) {
            JSONObject paramsOfReturn = returnObject.getJSONObject("I_RETURN");
            if (Const.ERP_RETURN_TYPE_S.equals(paramsOfReturn.getString("CODE"))) {
                JSONArray paramsOfData = returnObject.getJSONArray("T_DATA");
                if (paramsOfData != null) {
                    paramsOfData.forEach(e -> {
                        JSONObject obj = (JSONObject) e;
                        StockCompareVO stockCompareVO = new StockCompareVO();
                        stockCompareVO.setFtyCode(EnumFactory.J047.getFtyCode());
                        stockCompareVO.setMatCode(obj.getString("MATNR"));
                        stockCompareVO.setMatName(obj.getString("MAKTX"));
                        stockCompareVO.setUnitCode(obj.getString("MEINS"));
                        stockCompareVO.setUnitName(obj.getString("MSEHL"));
                        stockCompareVO.setSapQty(UtilObject.getBigDecimalOrZero(obj.getString("QMSL")));
                        stockCompareVO.setWmsQty(BigDecimal.ZERO);
                        itemList.add(stockCompareVO);
                    });
                }
            }
        }
        return itemList;
    }

    /**
     * 查询项目数据分类统计
     *
     * @param ctx
     */
    public void selectProjectDataStatistics(BizContext ctx) {
        ProjectDataStatisticsSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po == null) {
            po = new ProjectDataStatisticsSearchPO();
        }
        if (UtilObject.isEmpty(po.getCreateTimeEnd()) && UtilObject.isEmpty(po.getCreateTimeStart())) {
            try {
                po.setCreateTimeStart(DateUtils.parseDate("2023-01-01", "yyyy-MM-dd"));
                po.setCreateTimeEnd(DateUtils.parseDate(DateUtil.today(), "yyyy-MM-dd"));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        // 设计院图纸(张)
        int unitizedDesignPaperCount = bizReceiptPaperHeadDataWrap.list(new QueryWrapper<BizReceiptPaperHead>()
                .lambda().eq(BizReceiptPaperHead::getReceiptType, EnumReceiptType.UNITIZED_DESIGN_PAPER.getValue())).stream().collect(Collectors.groupingBy(BizReceiptPaperHead::getFileCode)).size();
        // 承包商图纸（张）
        int unitizedConstructionPaperCount = bizReceiptPaperHeadDataWrap.list(new QueryWrapper<BizReceiptPaperHead>()
                .lambda().eq(BizReceiptPaperHead::getReceiptType, EnumReceiptType.UNITIZED_CONSTRUCTION_PAPER.getValue())).stream().collect(Collectors.groupingBy(BizReceiptPaperHead::getFileCode)).size();
        // 合同数量（条）
        int contractCount = dicPurchasePackageDataWrap.list().stream().collect(Collectors.groupingBy(DicPurchasePackage::getContractCode)).size();
        // 采购包数量（条）
        int purchasePackageCount = dicPurchasePackageDataWrap.list().stream().collect(Collectors.groupingBy(DicPurchasePackage::getPurchasePackageCode)).size();

        // 库存数量（条）
        int ctMatStockBatchItemCount = stockBatchMapper.selectCtMatStockBatchItemCount();
        int ctMatStockBatchReserveCount = stockBatchMapper.selectCtMatStockBatchReserveCount();
        int ctMatStockBatchFreezeCount = stockBatchMapper.selectCtMatStockBatchFreezeCount();
        int loginCount = (int)logLoginLogDataWrap.count();
        List<Long> ftyIds = new ArrayList<>(dictionaryService.getAllFtyCache()).stream().filter(f -> f.getFtyCode().endsWith("047")).map(DicFactoryDTO::getId).collect(Collectors.toList());

        List<DicWhStorageBinDTO> dicWhStorageBinDTOS = UtilCollection.toList(dicWhStorageBinDataWrap.list(
                new QueryWrapper<DicWhStorageBin>().lambda().in(DicWhStorageBin::getFtyId, ftyIds)), DicWhStorageBinDTO.class);
        long binCount = dicWhStorageBinDTOS.stream().filter(c -> c.getIsDefault() == 0).count();
        ProjectDataStatisticsVO projectDataStatisticsVO = new ProjectDataStatisticsVO();
        projectDataStatisticsVO.setUnitizedDesignPaperCount(unitizedDesignPaperCount);
        projectDataStatisticsVO.setUnitizedConstructionPaperCount(unitizedConstructionPaperCount);
        projectDataStatisticsVO.setContractCount(contractCount);
        projectDataStatisticsVO.setPurchasePackageCount(purchasePackageCount);
        projectDataStatisticsVO.setCtMatStockBatchItemCount(ctMatStockBatchItemCount);
        projectDataStatisticsVO.setCtMatStockBatchReserveCount(ctMatStockBatchReserveCount);
        projectDataStatisticsVO.setCtMatStockBatchFreezeCount(ctMatStockBatchFreezeCount);
        projectDataStatisticsVO.setLoginCount(loginCount);
        projectDataStatisticsVO.setBinCount((int) binCount);
        projectDataStatisticsVO.setRegisterBatchCount(bizReportMapper.selectRegisterBatchCount(po));
        projectDataStatisticsVO.setRegisterBoxCount(bizReportMapper.selectRegisterBoxCount(po));
        projectDataStatisticsVO.setRegisterUpCount(bizReportMapper.selectRegisterUpCount(po));
        projectDataStatisticsVO.setInspectBatchCount(bizReportMapper.selectInspectBatchCount(po));
        projectDataStatisticsVO.setInspectItemCount(bizReportMapper.selectInspectItemCount(po));
        projectDataStatisticsVO.setNcrCount(bizReportMapper.selectNcrCount(po));
        projectDataStatisticsVO.setGvnCount(bizReportMapper.selectGvnCount(po));
        projectDataStatisticsVO.setRequirementCount(bizReportMapper.selectRequirementCount(po));
        projectDataStatisticsVO.setRequirementItemCount(bizReportMapper.selectRequirementItemCount(po));
        projectDataStatisticsVO.setApplyCount(bizReportMapper.selectApplyCount(po));
        projectDataStatisticsVO.setApplyItemCount(bizReportMapper.selectApplyItemCount(po));
        projectDataStatisticsVO.setOutboundCount(bizReportMapper.selectOutboundCount(po));
        projectDataStatisticsVO.setOutboundItemCount(bizReportMapper.selectOutboundItemCount(po));
        projectDataStatisticsVO.setNotOutboundCount(bizReportMapper.selectNotOutboundCount(po));
        projectDataStatisticsVO.setNotOutboundItemCount(bizReportMapper.selectNotOutboundItemCount(po));
        projectDataStatisticsVO.setMaterialReturnBatchCount(bizReportMapper.selectMaterialReturnBatchCount(po));
        projectDataStatisticsVO.setMaterialReturnItemCount(bizReportMapper.selectMaterialReturnItemCount(po));
        projectDataStatisticsVO.setReturnCount(bizReportMapper.selectReturnCount(po));
        projectDataStatisticsVO.setReturnItemCount(bizReportMapper.selectReturnItemCount(po));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, projectDataStatisticsVO);

    }

    /**
     * 物资滞留库存
     *
     * @param ctx
     */
    public void selectMatRetentionStock(BizContext ctx) {
        MatRetentionStockSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isEmpty(po)) {
            po = new MatRetentionStockSearchPO();
        }
        List<StockRetentionDTO> stockRetentionDTOS = bizReportMapper.selectStockRetention(po);
        MatRetentionStockVO matRetentionStockVO = new MatRetentionStockVO();
        matRetentionStockVO.setMatRetentionStockShowVOList(stockRetentionDTOS);

        IPage<MatRetentionStockDetailVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(MatRetentionStockDetailVO.class);
        }
        List<MatRetentionStockDetailVO> matRetentionStockDetailVOS = bizReportMapper.selectMatRetentionStockDetail(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        PageObjectWithTotalVO<MatRetentionStockDetailVO, MatRetentionStockVO> vo = new PageObjectWithTotalVO<>(matRetentionStockDetailVOS, totalCount, matRetentionStockVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);

    }

    public void exportMatRetentionStock(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物资滞留库存统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectMatRetentionStock(ctx);

        PageObjectWithTotalVO<MatRetentionStockDetailVO, MatRetentionStockVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(MatRetentionStockDetailVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void selectStockValidityPeriod(BizContext ctx) {
        // 入参上下文
        StockValidityPeriodSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        IPage<StockValidityPeriodWarningVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(StockValidityPeriodWarningVO.class);
        }
        List<StockValidityPeriodWarningVO> result = bizReportMapper.selectStockValidityPeriod(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        result.forEach(item -> {
            item.setIsSafeStr("否");
            item.setProcurementMethodStr("自主采购");
            if (Const.ONE.equals(item.getIsSafe())) {
                item.setIsSafeStr("是");
            }
            if (Const.ONE.equals(item.getProcurementMethod())) {
                item.setProcurementMethodStr("联合采购");
            }
        });
        PageObjectVO<StockValidityPeriodWarningVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportStockValidityPeriod(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("库存有效期预警"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectStockValidityPeriod(ctx);

        PageObjectVO<StockValidityPeriodWarningVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(StockValidityPeriodWarningVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    public void selectMatStateTrack(BizContext ctx) {
        // 入参上下文
        StockValidityPeriodSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        IPage<MatStateTrackVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(MatStateTrackVO.class);
        }
        List<MatStateTrackVO> result = bizReportMapper.selectMatStateTrack(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        result.forEach(item -> {
            item.setIsSafeStr("否");
            item.setProcurementMethodStr("自主采购");
            if (Const.ONE.equals(item.getIsSafe())) {
                item.setIsSafeStr("是");
            }
            if (Const.ONE.equals(item.getProcurementMethod())) {
                item.setProcurementMethodStr("联合采购");
            }
            // 处理到货登记数量
            this.handleArrivalRegister(item);
            // 处理质检会签数量
            this.handleInspect(item);
            // 处理验收入库数量
            this.handleInput(item);
            // 处理物资返运数量
            this.handleMaterialReturn(item);
            // 处理冻结数量
            this.handleFreeze(item);
            // 处理预留数量
            this.handleReserve(item);
            // 处理出库数量
            this.handleOutput(item);
            // 处理退库数量
            this.handleReturn(item);
        });
        PageObjectVO<MatStateTrackVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);

    }

    private void handleReturn(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getReturnItemIds())) {
            List<MatStockTrackReceiptVO> returnHeads = new ArrayList<>();
            List<String> returnItems = Arrays.stream(item.getReturnItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizReceiptReturnItem> returnItemList = bizReceiptReturnItemDataWrap.listByIds(returnItems);
            returnItemList.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getQty());
                BizReceiptReturnHead head = bizReceiptReturnHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getModifyTime());
                returnHeads.add(matStockTrackReceiptVO);
            });
            item.setReturnHeads(returnHeads);
        }
    }

    private void handleOutput(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getOutputItemIds())) {
            List<MatStockTrackReceiptVO> outputHeads = new ArrayList<>();
            List<String> outputItems = Arrays.stream(item.getOutputItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizReceiptOutputItem> outputItemList = bizReceiptOutputItemDataWrap.listByIds(outputItems);
            outputItemList.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getQty());
                BizReceiptOutputHead head = bizReceiptOutputHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                outputHeads.add(matStockTrackReceiptVO);
            });
            item.setOutputHeads(outputHeads);
        }
    }

    private void handleReserve(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getReserveBinIds())) {
            List<MatStockTrackReceiptVO> reserveHeads = new ArrayList<>();
            List<String> reserveBinIds = Arrays.stream(item.getReserveBinIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizReceiptApplyBin> bizReceiptApplyBins = bizReceiptApplyBinDataWrap.listByIds(reserveBinIds);
            bizReceiptApplyBins.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getQty());
                BizReceiptApplyHead head = bizReceiptApplyHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                reserveHeads.add(matStockTrackReceiptVO);
            });
            item.setReserveHeads(reserveHeads);
        }
    }

    private void handleFreeze(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getTransportItemIds())) {
            List<MatStockTrackReceiptVO> freezeHeads = new ArrayList<>();
            List<String> materialReturnItemIds = Arrays.stream(item.getTransportItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizReceiptTransportItem> transportItems = bizReceiptTransportItemDataWrap.listByIds(materialReturnItemIds);
            transportItems.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getQty());
                BizReceiptTransportHead head = bizReceiptTransportHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getModifyTime());
                freezeHeads.add(matStockTrackReceiptVO);
            });
            item.setFreezeHeads(freezeHeads);
        }
    }

    private void handleMaterialReturn(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getMaterialReturnItemIds())) {
            List<MatStockTrackReceiptVO> materialReturnHeads = new ArrayList<>();
            List<String> materialReturnItemIds = Arrays.stream(item.getMaterialReturnItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizMaterialReturnItem> matReturnItemIds = bizMaterialReturnItemDataWrap.listByIds(materialReturnItemIds);
            matReturnItemIds.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getReturnQty());
                BizMaterialReturnHead head = bizMaterialReturnHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getModifyTime());
                materialReturnHeads.add(matStockTrackReceiptVO);
            });
            item.setMaterialReturnHeads(materialReturnHeads);
        }
    }

    private void handleInput(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getInputItemIds())) {
            List<MatStockTrackReceiptVO> inputHeads = new ArrayList<>();
            List<String> inputItemIds = Arrays.stream(item.getInputItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizReceiptInputItem> inputItems = bizReceiptInputItemDataWrap.listByIds(inputItemIds);
            inputItems.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getQty());
                BizReceiptInputHead head = bizReceiptInputHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                inputHeads.add(matStockTrackReceiptVO);
            });
            item.setInputHeads(inputHeads);
        }
    }

    private void handleInspect(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getSignInspectItemIds())) {
            List<MatStockTrackReceiptVO> qualifiedInspectHeads = new ArrayList<>();
            List<MatStockTrackReceiptVO> unqualifiedInspectHeads = new ArrayList<>();
            List<MatStockTrackReceiptVO> unArrivalInspectHeads = new ArrayList<>();
            List<String> signInspectItemIds = Arrays.stream(item.getSignInspectItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(signInspectItemIds)) {
                List<BizReceiptInspectItem> receiptInspectItems = bizReceiptInspectItemDataWrap.listByIds(signInspectItemIds);
                List<BizReceiptInspectItem> qualifiedItems = receiptInspectItems.stream().filter(c -> c.getQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                List<BizReceiptInspectItem> unqualifiedItems = receiptInspectItems.stream().filter(c -> c.getUnqualifiedQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                List<BizReceiptInspectItem> unArrivalItems = receiptInspectItems.stream().filter(c -> c.getUnarrivalQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                qualifiedItems.forEach(c -> {
                    MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                    matStockTrackReceiptVO.setQty(c.getQty());
                    BizReceiptInspectHead head = bizReceiptInspectHeadDataWrap.getById(c.getHeadId());
                    matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                    matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                    qualifiedInspectHeads.add(matStockTrackReceiptVO);
                });
                unqualifiedItems.forEach(c -> {
                    MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                    matStockTrackReceiptVO.setQty(c.getUnqualifiedQty());
                    BizReceiptInspectHead head = bizReceiptInspectHeadDataWrap.getById(c.getHeadId());
                    matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                    matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                    unqualifiedInspectHeads.add(matStockTrackReceiptVO);
                });
                unArrivalItems.forEach(c -> {
                    MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                    matStockTrackReceiptVO.setQty(c.getUnarrivalQty());
                    BizReceiptInspectHead head = bizReceiptInspectHeadDataWrap.getById(c.getHeadId());
                    matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                    matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                    unArrivalInspectHeads.add(matStockTrackReceiptVO);
                });
            }
            item.setQualifiedInspectHeads(qualifiedInspectHeads);
            item.setUnqualifiedInspectHeads(unqualifiedInspectHeads);
            item.setUnArrivalInspectHeads(unArrivalInspectHeads);
        }
    }

    private void handleArrivalRegister(MatStateTrackVO item) {
        if (UtilString.isNotNullOrEmpty(item.getRegisterItemIds())) {
            List<MatStockTrackReceiptVO> registerHeads = new ArrayList<>();
            List<String> registerItemIds = Arrays.stream(item.getRegisterItemIds().split(Const.COMMA)).distinct().filter(e -> UtilNumber.isNotEmpty(Long.valueOf(e))).collect(Collectors.toList());
            List<BizReceiptRegisterItem> receiptRegisterItems = bizReceiptRegisterItemDataWrap.listByIds(registerItemIds);
            receiptRegisterItems.forEach(c -> {
                MatStockTrackReceiptVO matStockTrackReceiptVO = new MatStockTrackReceiptVO();
                matStockTrackReceiptVO.setQty(c.getQty());
                BizReceiptRegisterHead head = bizReceiptRegisterHeadDataWrap.getById(c.getHeadId());
                matStockTrackReceiptVO.setReceiptCode(head.getReceiptCode());
                matStockTrackReceiptVO.setFinishTime(head.getSubmitTime());
                registerHeads.add(matStockTrackReceiptVO);
            });
            item.setRegisterHeads(registerHeads);
        }
    }


    public void exportMatStateTrack(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物项状态跟踪"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectMatStateTrack(ctx);

        PageObjectVO<MatStateTrackVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(MatStateTrackVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    public void selectReservedStock(BizContext ctx) {
        // 入参上下文
        StockValidityPeriodSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        IPage<ReservedEmptyStockVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ReservedEmptyStockVO.class);
        }
        List<ReservedEmptyStockVO> result = bizReportMapper.selectReservedStock(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        result.forEach(item -> {
            BigDecimal qty = bizReportMapper.selectArrivedButNotInputQty(item);
            item.setArrivedButNotInputQty(UtilNumber.isNotEmpty(qty) ? qty : BigDecimal.ZERO);
            item.setNotInputPurchaseQty(item.getReservedQty().subtract(item.getStockQty()).subtract(item.getArrivedButNotInputQty()));
        });

        PageObjectVO<ReservedEmptyStockVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportReservedStock(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("待预留无库存"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectReservedStock(ctx);

        PageObjectVO<ReservedEmptyStockVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ReservedEmptyStockVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    public void selectToolBorrowReport(BizContext ctx) {
        // 入参上下文
        ToolBorrowStatisticsSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilString.isNotNullOrEmpty(po.getBorrowDept())) {
            List<DicDept> deptList = dicDeptDataWrap.list(new QueryWrapper<DicDept>().lambda().eq(DicDept::getDeptName, po.getBorrowDept()));
            if (UtilCollection.isNotEmpty(deptList)) {
                List<SysUserDeptOfficeRel> sysUserDeptOfficeRels = sysUserDeptOfficeRelDataWrap.list(new QueryWrapper<SysUserDeptOfficeRel>().lambda().in(SysUserDeptOfficeRel::getDeptId, deptList.stream().map(DicDept::getId).collect(Collectors.toList())));
                po.setBorrowUserIdList(sysUserDeptOfficeRels.stream().map(SysUserDeptOfficeRel::getUserId).collect(Collectors.toList()));
            }
        }
        IPage<ToolBorrowStatisticsVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(ToolBorrowStatisticsVO.class);
        }
        List<ToolBorrowStatisticsVO> result = bizReportMapper.selectToolBorrowReport(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        result.forEach(item -> {
            item.setBorrowDept(userService.getSysUserInfoById(item.getBorrowCreateUserId()).getDeptName());
        });
        PageObjectVO<ToolBorrowStatisticsVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    public void exportToolBorrowReport(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("专用工器具统计"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        this.selectToolBorrowReport(ctx);

        PageObjectVO<ToolBorrowStatisticsVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        UtilExcel.writeExcel(ToolBorrowStatisticsVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 报表-库存凭证查询
     */
    public void getStockInsDocBinPage(BizContext ctx) {
        // 入参上下文
        StockInsDocBinPagePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        IPage<StockInsDocBinVO> page = po.getPageObj(StockInsDocBinVO.class);
        page.orders().forEach(obj -> {
            // 字符转数值排序
            obj.setColumn(obj.getColumn().replace("ins_doc_rid", "ins_doc_rid + 0"));
        });
        WmsQueryWrapper<StockInsDocBinPagePO> queryWrapper = new WmsQueryWrapper();
        if(UtilObject.isNotEmpty(po.getCreateTimeStart())){
            po.setCreateTimeEnd(UtilDate.plusDays(po.getCreateTimeEnd(), 1));
        }
        queryWrapper.lambda()
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), StockInsDocBinPagePO::getMatCode, po.getMatCode())
                .in(UtilCollection.isNotEmpty(po.getMatCodeList()), StockInsDocBinPagePO::getMatCode, po.getMatCodeList())
                .eq(UtilString.isNotNullOrEmpty(po.getBatchCode()), StockInsDocBinPagePO::getBatchCode, po.getBatchCode())
                .in(UtilCollection.isNotEmpty(po.getBatchCodeList()), StockInsDocBinPagePO::getBatchCode, po.getBatchCodeList())
                .eq(UtilString.isNotNullOrEmpty(po.getPreReceiptCode()), StockInsDocBinPagePO::getPreReceiptCode, po.getPreReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getReferReceiptCode()), StockInsDocBinPagePO::getReferReceiptCode, po.getReferReceiptCode())
                .between(UtilObject.isNotEmpty(po.getCreateTimeEnd()) && UtilObject.isNotEmpty(po.getCreateTimeStart()), StockInsDocBinPagePO::getCreateTime, po.getCreateTimeStart(), po.getCreateTimeEnd())
                .eq(UtilString.isNotNullOrEmpty(po.getMatDocCode()), StockInsDocBinPagePO::getMatDocCode, po.getMatDocCode());
        // 查询sql
        List<StockInsDocBinVO> dtoList = bizReportMapper.getStockInsDocBinPage(page, queryWrapper);
        dataFillService.fillAttr(dtoList);
        PageObjectVO<StockInsDocBinVO> vo = new PageObjectVO<>(dtoList, page.getTotal());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 报表-运单台账查询
     *
     * @param ctx 入参上下文
     */
    public void getDeliveryWaybillLedgerPage(BizContext ctx) {
        // 入参上下文
        DeliveryWaybillLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po.getDestinationTakeDeliveryTimeEnd())) {
            po.setDestinationTakeDeliveryTimeEnd(UtilDate.getEndOfDay(po.getDestinationTakeDeliveryTimeEnd()));
        }
        if (UtilObject.isNotNull(po.getActualDepartureTimeEnd())) {
            po.setActualDepartureTimeEnd(UtilDate.getEndOfDay(po.getActualDepartureTimeEnd()));
        }
        if (UtilObject.isNotNull(po.getCustomsClearanceCompletionTimeEnd())) {
            po.setCustomsClearanceCompletionTimeEnd(UtilDate.getEndOfDay(po.getCustomsClearanceCompletionTimeEnd()));
        }
        if (UtilObject.isNotNull(po.getActualPortOfArrivalTimeEnd())) {
            po.setActualPortOfArrivalTimeEnd(UtilDate.getEndOfDay(po.getActualPortOfArrivalTimeEnd()));
        }

        if (UtilString.isNotNullOrEmpty(po.getBatchCode())) {
            po.setBatchCode(po.getBatchCode().replaceAll("[ ,;]+", ","));
            List<String> strList = Arrays.stream(po.getBatchCode().split(",")).collect(Collectors.toList());
            po.setBatchCodeList(strList);
            po.setBatchCode(null);
        }

        IPage<DeliveryWaybillLedgerVO> page = null;
        Long totalCount = null;
        if (po.isPaging()) {
            page = po.getPageObj(DeliveryWaybillLedgerVO.class);
        }

        List<DeliveryWaybillLedgerVO> result = bizReportMapper.selectDeliveryWaybillLedgerPage(page, po);
        if (page != null) {
            totalCount = page.getTotal();
        }
        if (result != null) {
            for (DeliveryWaybillLedgerVO vo : result) {
                vo.setBookingSpaceTime(UtilDate.getBeginOfDay(vo.getBookingSpaceTime()));
                vo.setSubmitRedemptionDocFinishTime(UtilDate.getBeginOfDay(vo.getSubmitRedemptionDocFinishTime()));
                vo.setIncomeTaxExemptionFinishTime(UtilDate.getBeginOfDay(vo.getIncomeTaxExemptionFinishTime()));
                vo.setEstimatedShippingTime(UtilDate.getBeginOfDay(vo.getEstimatedShippingTime()));
                vo.setActualDepartureTime(UtilDate.getBeginOfDay(vo.getActualDepartureTime()));
                vo.setExportCustomsDeclarationTime(UtilDate.getBeginOfDay(vo.getExportCustomsDeclarationTime()));
                vo.setActualPortOfArrivalTime(UtilDate.getBeginOfDay(vo.getActualPortOfArrivalTime()));
                vo.setEstimatedPortOfArrivalTime(UtilDate.getBeginOfDay(vo.getEstimatedPortOfArrivalTime()));
                vo.setCustomsClearanceCompletionTime(UtilDate.getBeginOfDay(vo.getCustomsClearanceCompletionTime()));
                vo.setDestinationTakeDeliveryTime(UtilDate.getBeginOfDay(vo.getDestinationTakeDeliveryTime()));
                vo.setReceiveDate(UtilDate.getBeginOfDay(vo.getReceiveDate()));
                vo.setKlqBackDocTime(UtilDate.getBeginOfDay(vo.getKlqBackDocTime()));
                vo.setKlqBackFiApprovalTime(UtilDate.getBeginOfDay(vo.getKlqBackFiApprovalTime()));
                
                // 到港-到场周期/天 = 到场时间AG - 实际到港时间AC
                vo.setReceiveArrivalDays(-UtilDate.getDaysDifference(vo.getReceiveDate(), vo.getActualPortOfArrivalTime()));
                // 托收延误周期/天 = 托收办理完成日期T - 实际到港时间AC
                vo.setRedemptionArrivalDays(-UtilDate.getDaysDifference(vo.getSubmitRedemptionDocFinishTime(), vo.getActualPortOfArrivalTime()));
                // 所得税延延误周期/天 = 所得税免税批复日期V - 实际到港时间AC
                vo.setFinishArrivalDays(-UtilDate.getDaysDifference(vo.getIncomeTaxExemptionFinishTime(), vo.getActualPortOfArrivalTime()));
                // 内陆运输延误周期/天 = 到场时间AG - 货物清关完成时间AE
                vo.setReceiveCompletionDays(-UtilDate.getDaysDifference(vo.getReceiveDate(), vo.getCustomsClearanceCompletionTime()));
                // 清关周期/自然日 = 清关完成时间减去以下3个数据的最大值 1-到港时间 2-托收单据放单(收到)时间/银行F.I批复时间（两者取高值) 3-所得税免税批复时间
                Date maxDate = UtilDate.maxDate(vo.getKlqBackFiApprovalTime(), vo.getKlqBackDocTime());
                maxDate = UtilDate.maxDate(vo.getActualPortOfArrivalTime(), maxDate);
                maxDate = UtilDate.maxDate(vo.getIncomeTaxExemptionFinishTime(), maxDate);
                vo.setCompletionDays(-UtilDate.getDaysDifference(vo.getCustomsClearanceCompletionTime(), maxDate));
                // 清关周期/工作日 = 去掉清关周期/自然日中的周六周天
                vo.setCompletionWeekDays(UtilDate.getWorkDays(vo.getCustomsClearanceCompletionTime(), maxDate));
                vo.setCompletionWeekDays(UtilNumber.isNotEmpty(vo.getCompletionWeekDays()) && vo.getCompletionWeekDays() < 0 ? -vo.getCompletionWeekDays() : 0);
                // 物流周期主要影响因素 = 【当托收延误周期/天】 或 【所得税延延误周期/天大于30时】，展示数据（哪个大于30天就展示哪个）
                vo.setReason("");
                if (vo.getRedemptionArrivalDays() > 30) {
                    vo.setReason(vo.getReason() + "托收滞后于到港时间" + vo.getRedemptionArrivalDays() + "天;");
                }
                if (vo.getFinishArrivalDays() > 30) {
                    vo.setReason(vo.getReason() + "所得税免税滞后于到港时间" + vo.getFinishArrivalDays() + "天;");
                }
            }
        }

        PageObjectVO<DeliveryWaybillLedgerVO> vo = new PageObjectVO<>(result, totalCount);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 报表-运单台账导出
     *
     * @param ctx 入参上下文
     */
    public void exportDeliveryWaybillLedger(BizContext ctx) {

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物流管理台账"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        DeliveryWaybillLedgerPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setPaging(false);
        this.getDeliveryWaybillLedgerPage(ctx);

        PageObjectVO<DeliveryWaybillLedgerVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        String langCode = this.getLangCodeFromRequest();
        for (DeliveryWaybillLedgerVO ledgerVO : vo.getResultList()) {
            ledgerVO.setReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", ledgerVO.getReceiptStatus().toString()));
            ledgerVO.setDeliveryTypeI18n(i18nTextCommonService.getNameMessage(langCode, "deliveryType", ledgerVO.getDeliveryType().toString()));
            ledgerVO.setShippingTypeI18n(i18nTextCommonService.getNameMessage(langCode, "shippingType", ledgerVO.getShippingType().toString()));
        }

        UtilExcel.writeExcel(DeliveryWaybillLedgerVO.class, vo.getResultList(), bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取送货类型下拉
     *
     * @param ctx ctx
     */
    public void getDeliveryTypeList(BizContext ctx) {
        MultiResultVO<DeliveryTypeMapVO> vo = new MultiResultVO<>(EnumDeliveryType.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 获取运输方式下拉
     *
     * @param ctx ctx
     */
    public void getShippingTypeList(BizContext ctx) {
        MultiResultVO<ShippingTypeMapVO> vo = new MultiResultVO<>(EnumShippingType.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }
}
