package com.inossem.wms.bizdomain.apply.service.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialGroupDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptMatApplyHeadDataWrap;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptMatApplyItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.auth.EnumUserJob;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.approval.entity.BizApprovalReceiptInstanceRel;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptMatApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptMatApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptMatApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptMatApplyItem;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptMatApplyItemImportPO;
import com.inossem.wms.common.model.bizdomain.apply.po.BizReceiptMatApplySearchPO;
import com.inossem.wms.common.model.bizdomain.apply.vo.BizReceiptMatApplyItemExportVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.mat.base.entity.DicMaterialGroup;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalReceiptInstanceRelDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 物料编码申请 component
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
@Slf4j
public class MatApplyComponent {

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private BizReceiptMatApplyHeadDataWrap bizReceiptMatApplyHeadDataWrap;

    @Autowired
    private BizReceiptMatApplyItemDataWrap bizReceiptMatApplyItemDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private BizApprovalReceiptInstanceRelDataWrap bizApprovalReceiptInstanceRelDataWrap;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private DicMaterialGroupDataWrap dicMaterialGroupDataWrap;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 页面初始化:
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptMatApplyHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptMatApplyHeadDTO()
                        .setReceiptType(EnumReceiptType.MAT_APPLY.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptMatApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptMatApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 开启审批
     *
     * @param ctx 入参上下文
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptMatApplyHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.MAT_APPLY.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 分页查询
     *
     * @param ctx 入参上下文
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptMatApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptMatApplySearchPO> wrapper = this.setQueryWrapper(po, user);
        // 分页处理
        IPage<BizReceiptMatApplyHeadDTO> page = po.getPageObj(BizReceiptMatApplyHeadDTO.class);
        if (page.orders().isEmpty()) {
            page.orders().add(OrderItem.desc("create_time"));
        } else {
            page.orders().forEach(obj -> {
                // 排序特殊处理
                obj.setColumn(obj.getColumn().replace("unit_name", "unit_id"));
                obj.setColumn(obj.getColumn().replace("mat_group_name", "mat_group_id"));
            });
        }

        bizReceiptMatApplyHeadDataWrap.selectPage(page, wrapper);

        List<BizReceiptMatApplyHeadDTO> dtoList = page.getRecords();
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);

        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 配置查询条件
     *
     * @param po   入参
     * @param user 当前用户
     * @return 查询条件
     */
    private WmsQueryWrapper<BizReceiptMatApplySearchPO> setQueryWrapper(BizReceiptMatApplySearchPO po, CurrentUser user) {
        if (null == po) {
            po = new BizReceiptMatApplySearchPO();
        }
        if (UtilObject.isNotNull(po.getCreateTimeEnd())) {
            po.setCreateTimeEnd(UtilDate.getEndOfDay(po.getCreateTimeEnd()));
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptMatApplySearchPO> wrapper = new WmsQueryWrapper<>();
        BizReceiptMatApplySearchPO finalPo = po;
        wrapper.lambda()
                .eq(Boolean.TRUE, BizReceiptMatApplySearchPO::getIsDelete, BizReceiptMatApplyHead.class, EnumRealYn.FALSE.getIntValue())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptMatApplySearchPO::getReceiptCode, BizReceiptMatApplyHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptMatApplySearchPO::getReceiptType, BizReceiptMatApplyHead.class, po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptMatApplySearchPO::getReceiptStatus, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptMatApplySearchPO::getMatCode, BizReceiptMatApplyItem.class, po.getMatCode())
                .and(UtilString.isNotNullOrEmpty(finalPo.getMatName()), q ->
                        q.like(Boolean.TRUE, BizReceiptMatApplySearchPO::getMatName, BizReceiptMatApplyItem.class, finalPo.getMatName())
                        .or().like(Boolean.TRUE, BizReceiptMatApplySearchPO::getMatNameEnShort, BizReceiptMatApplyItem.class, finalPo.getMatName()))
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptMatApplySearchPO::getUserName, SysUser.class, po.getCreateUserName())
                .between(UtilObject.isNotEmpty(po.getCreateTimeStart()) && UtilObject.isNotEmpty(po.getCreateTimeEnd()), BizReceiptMatApplySearchPO::getCreateTime, BizReceiptMatApplyHead.class, po.getCreateTimeStart(), po.getCreateTimeEnd())
                .groupBy(BizReceiptMatApplyHead.class, BizReceiptMatApplySearchPO::getId);
        return wrapper;
    }

    /**
     * 获取单据详情
     *
     * @param ctx 入参上下文
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptMatApplyHead head = bizReceiptMatApplyHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptMatApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptMatApplyHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);

        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));

        // 设置申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 物料编码申请单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptMatApplyHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(receiptStatus)) {
            // 待处理 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        return buttonVO;
    }

    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptMatApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    /**
     * 提交-校验入参
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizReceiptMatApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 基础数据校验
        this.checkSaveData(ctx);
        // 行项目不能为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 物料编码必填
        if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus())) {
            for (BizReceiptMatApplyItemDTO itemDTO : po.getItemList()) {
                if (UtilString.isNullOrEmpty(itemDTO.getMatCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
                }
            }
        }
        // 单据状态校验
        if (UtilNumber.isNotEmpty(po.getId())) {
            BizReceiptMatApplyHead head = bizReceiptMatApplyHeadDataWrap.getById(po.getId());
            if (UtilObject.isNotNull(head) && !head.getReceiptStatus().equals(po.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
            }
        }
    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void saveApply(BizContext ctx) {
        // 入参上下文
        BizReceiptMatApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.MAT_APPLY.getValue());
        if (UtilNumber.isEmpty(po.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(po.getReceiptStatus())) {
            po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新物料编码申请
            bizReceiptMatApplyHeadDataWrap.updateDtoById(po);
            // 物理删除行项目
            bizReceiptMatApplyItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptMatApplyItem>().eq(BizReceiptMatApplyItem::getHeadId, po.getId()));
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.MAT_APPLY.getValue());
            po.setReceiptCode(code);
            bizReceiptMatApplyHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** item处理开始 *************************/
        // 行项目处理
        if (UtilCollection.isNotEmpty(po.getItemList())) {
            int rid = 1;
            for (BizReceiptMatApplyItemDTO item : po.getItemList()) {
                item.setId(null);
                item.setHeadId(po.getId());
                item.setRid(String.valueOf(rid++));
                item.setItemStatus(po.getReceiptStatus());
            }
            bizReceiptMatApplyItemDataWrap.saveBatchDto(po.getItemList());
        }
        /* ********************** item处理结束 *************************/
        log.debug("保存物料编码申请head成功!单号{},主键{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的物料编码申请
        BizReceiptMatApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptMatApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存物料编码申请附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
        log.debug("保存物料编码申请附件成功!");
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submitApply(BizContext ctx) {
        // 入参上下文
        BizReceiptMatApplyHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存物料编码申请
        this.saveApply(ctx);
    }

    /**
     * 更新物料编码申请状态
     *
     * @param headDTO 物料编码申请head
     */
    public void updateStatus(BizReceiptMatApplyHeadDTO headDTO, List<BizReceiptMatApplyItemDTO> itemList, Integer status) {
        if (UtilObject.isNotNull(headDTO)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            bizReceiptMatApplyHeadDataWrap.updateDtoById(headDTO);
        }
        if (UtilCollection.isNotEmpty(itemList)) {
            for (BizReceiptMatApplyItemDTO item : itemList) {
                item.setItemStatus(status);
            }
            bizReceiptMatApplyItemDataWrap.updateBatchDtoById(itemList);
        }
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptMatApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headDTO.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(headDTO.getReceiptStatus())) {
            // 审批人校验
            this.approveCheck(ctx);
            // 发起流程审批
            Long receiptId = headDTO.getId();
            String receiptCode = headDTO.getReceiptCode();
            Integer receiptType = headDTO.getReceiptType();
            Map<String, Object> variables = new HashMap<>();

            // 物料编码申请：“请审批”[公司+部门]用户姓名+“提交的流程”+申请原因（取物料编码申请抬头申请原因）
            variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getReason());

            workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getReason());
            workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

            // 更新物料编码申请head - 审核中
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());

            // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
            hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());

        } else if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(headDTO.getReceiptStatus())) {

            // 更新物料编码申请head - 已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
//        // 设置为自动审批通过
//        BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
//        wfReceiptCo.setReceiptHeadId(headDTO.getId());
//        wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
//        wfReceiptCo.setInitiator(ctx.getCurrentUser());
//        this.approvalCallback(wfReceiptCo);
    }

    /**
     * 审批人校验
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    private void approveCheck(BizContext ctx) {
        // 一级审批节点 设备部专工
        List<String> level1UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.ED.getCode(), null, EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "1", EnumDept.ED.getName(), " ", EnumUserJob.LEVEL_1_APPROVAL.getName());
        }
        // 二级审批节点 设备部部长
        List<String> level2UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.ED.getCode(), null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "2", EnumDept.ED.getName(), " ", EnumUserJob.LEVEL_2_APPROVAL.getName());
        }
    }


    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    // @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizReceiptMatApplyHead head = bizReceiptMatApplyHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptMatApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptMatApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue());
        } else {

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if (EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())) {
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "物料编码申请的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getCreateUserId()).getUserCode()), head.getReceiptCode());
            } else {
                // 更新状态已驳回
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "物料编码申请的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getCreateUserId()).getUserCode()), head.getReceiptCode());
            }
        }
    }


    /**
     * 获取物料组
     *
     * @in ctx 查询入参
     */
    public void getMatGroup(BizContext ctx) {
        BizReceiptMatApplySearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            po = new BizReceiptMatApplySearchPO();
        }
        QueryWrapper<DicMaterialGroup> queryWrapper = new QueryWrapper<>();
        if (UtilString.isNotNullOrEmpty(po.getMatGroupName())) {
            queryWrapper.lambda().like(DicMaterialGroup::getMatGroupCode, po.getMatGroupName())
                    .or().like(DicMaterialGroup::getMatGroupName, po.getMatGroupName());
        }
        List<DicMaterialGroup> dicMaterialGroupList = dicMaterialGroupDataWrap.list(queryWrapper);
        List<BizReceiptMatApplyItemDTO> dtoList = UtilCollection.toList(dicMaterialGroupList, BizReceiptMatApplyItemDTO.class);
        for (BizReceiptMatApplyItemDTO itemDTO : dtoList) {
            // 根据物料组设置行项目评估类
            this.setItemExtEvaluationClassificationByMatGroupCode(itemDTO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dtoList));
    }

    /**
     * 根据物料组设置行项目评估类
     */
    private void setItemExtEvaluationClassificationByMatGroupCode(BizReceiptMatApplyItemDTO itemDTO) {
        List<String> list1160 = Arrays.asList("Y101", "Y102", "Y103", "Y104", "Y105", "Y106", "Y107", "Y108", "Y109", "Y110", "Y111", "Y112", "Y113", "Y114", "Y115", "Y116", "Y118", "Y119", "Y120", "Y121", "Y199");
        if (list1160.contains(itemDTO.getMatGroupCode())) {
            itemDTO.setExtEvaluationClassification("1160");
        }
        List<String> list1152 = Arrays.asList("Y201");
        if (list1152.contains(itemDTO.getMatGroupCode())) {
            itemDTO.setExtEvaluationClassification("1152");
        }
        List<String> list1110 = Arrays.asList("Y117", "Y202", "Y401", "Y402", "Y403", "Y404", "Y405", "Y406", "Y407", "Y408", "Y409");
        if (list1110.contains(itemDTO.getMatGroupCode())) {
            itemDTO.setExtEvaluationClassification("1110");
        }
    }

    /**
     * 获取文件描述
     */
    public static String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 获取文件名
     */
    public static String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 导出
     *
     * @in ctx 入参
     */
    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("物料编码申请"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        BizReceiptMatApplyHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptMatApplyItemExportVO> exportVOList = UtilCollection.toList(headDTO.getItemList(), BizReceiptMatApplyItemExportVO.class);

        UtilExcel.writeExcel(BizReceiptMatApplyItemExportVO.class, exportVOList, bizCommonFile, false);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 删除
     *
     * @in ctx 入参
     */
    public void remove(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 单据状态校验
        BizReceiptMatApplyHead head = bizReceiptMatApplyHeadDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        /* ********* 删除设施信息 ******** */
        bizReceiptMatApplyHeadDataWrap.removeById(id);
        bizReceiptMatApplyItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptMatApplyItem>().eq(BizReceiptMatApplyItem::getHeadId, id));
    }

    /**
     * 撤销
     *
     * @in ctx 入参上下文
     */
    public void revoke(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 单据状态校验
        BizReceiptMatApplyHead head = bizReceiptMatApplyHeadDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        if (!EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }

        // 属性填充
        BizReceiptMatApplyHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptMatApplyHeadDTO.class);
        dataFillService.fillAttr(headDTO);

        // 更新领料申请单 - 草稿
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());

        // 删除待办
        workflowService.deleteTodo(head.getId());

        // 获取审批实例关系
        BizApprovalReceiptInstanceRel approvalRel = bizApprovalReceiptInstanceRelDataWrap.getOne(
                new QueryWrapper<BizApprovalReceiptInstanceRel>()
                        .lambda()
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, id)
                        .eq(BizApprovalReceiptInstanceRel::getApproveStatus, EnumApprovalStatus.APPROVING.getValue())
                        .orderByDesc(BizApprovalReceiptInstanceRel::getCreateTime).last("limit 1")
        );

        if (UtilObject.isNull(approvalRel)) {
            log.warn("物料编码申请[{}]未找到有效的审批实例", head.getReceiptCode());
            return;
        }

        // 撤销审批流程
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(approvalRel.getProcessInstanceId());
        workflowService.revoke(revokeDTO);
        log.debug("物料编码申请[{}]审批流程撤销完成", head.getReceiptCode());

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

    /**
     * 导入
     */
    public void importMatApply(BizContext ctx) {
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        // 获取物料编码申请行项目导入数据
        try {
            List<BizReceiptMatApplyItemImportPO> matApplyItemImportPOList = (List<BizReceiptMatApplyItemImportPO>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptMatApplyItemImportPO.class);

            if (UtilCollection.isEmpty(matApplyItemImportPOList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }

            List<BizReceiptMatApplyItemDTO> itemDTOList = new ArrayList<>();

            // 数据校验
            int excelEmptyRow = 1;
            for (BizReceiptMatApplyItemImportPO importPO : matApplyItemImportPOList) {
                if (UtilString.isNullOrEmpty(importPO.getMaterialName()) ||
                        UtilString.isNullOrEmpty(importPO.getDescInfo()) ||
                        UtilString.isNullOrEmpty(importPO.getMaterialNameEn()) ||
                        UtilString.isNullOrEmpty(importPO.getDescInfoEn()) ||
                        UtilString.isNullOrEmpty(importPO.getUnitName()) ||
                        UtilString.isNullOrEmpty(importPO.getMatGroupCode())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_REQUIRED_MSG, String.valueOf(excelEmptyRow));
                }
                String matName = importPO.getMaterialName() + "^" + importPO.getDescInfo() + "^" + importPO.getApplicableEquipment() + "/" + importPO.getSystems();
                if (matName.length() > 40) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_LENGTH_TOO_LONG, matName, "40");
                }
                String matNameEn = importPO.getMaterialNameEn() + "^" + importPO.getDescInfoEn() + "^" + importPO.getApplicableEquipmentEn() + "/" + importPO.getSystemsEn();
                if (matNameEn.length() > 40) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_LENGTH_TOO_LONG, matNameEn, "40");
                }
                excelEmptyRow++;
            }

            for (BizReceiptMatApplyItemImportPO importPO : matApplyItemImportPOList) {
                BizReceiptMatApplyItemDTO itemDTO = UtilBean.newInstance(importPO, BizReceiptMatApplyItemDTO.class);
                // 校验单位
                Long unitId = dictionaryService.getUnitIdCacheByCode(importPO.getUnitName());
                if (UtilNumber.isEmpty(unitId)) {
                    unitId = dictionaryService.getUnitIdCacheByName(importPO.getUnitName());
                }
                if (UtilNumber.isEmpty(unitId)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_UNIT_NOT_EXIST, importPO.getUnitName());
                }
                itemDTO.setUnitId(unitId);
                // 校验物料组
                Long matGroupId = dictionaryService.getMatGroupIdByMatGroupCode(importPO.getMatGroupCode());
                if (UtilNumber.isEmpty(matGroupId)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_GROUP_NOT_EXIST, importPO.getMatGroupCode());
                }
                itemDTO.setMatGroupId(matGroupId);
                // 根据物料组设置行项目评估类
                this.setItemExtEvaluationClassificationByMatGroupCode(itemDTO);
                itemDTO.setMatTypeCode("YECD");
                itemDTO.setIndustryCode("M");
                itemDTO.setPriceControlCode("V");
                itemDTO.setPriceUnit("1");
                itemDTO.setPurchaseGroupCode("110");
                itemDTO.setProfitCenterCode("P1104");
                itemDTOList.add(itemDTO);
            }
            // 填充关联属性
            dataFillService.fillRlatAttrDataList(itemDTOList);
            // 设置上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(itemDTOList));
        } catch (IOException e) {
            log.error("导入物料编码申请异常", e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_READ_EXCEPTION);
        }
    }

    /**
     * 导入物料编码
     *
     * @param ctx 入参上下文
     */
    public void importMatCode(BizContext ctx) {
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        String json = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        BizReceiptMatApplyHeadDTO headDTO = null;
        try {
            headDTO = JSON.parseObject(json, BizReceiptMatApplyHeadDTO.class);
        } catch (Exception e) {
            log.error("JSON解析失败", e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }

        // 获取物料编码申请行项目导入数据
        try {
            List<BizReceiptMatApplyItemExportVO> matApplyItemImportPOList = (List<BizReceiptMatApplyItemExportVO>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptMatApplyItemExportVO.class);

            if (UtilCollection.isEmpty(matApplyItemImportPOList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }

            List<BizReceiptMatApplyItemDTO> itemDTOList = new ArrayList<>();

            // 数据校验
            Map<String, BizReceiptMatApplyItemDTO> matNameMap = headDTO.getItemList().stream().collect(Collectors.groupingBy(BizReceiptMatApplyItemDTO::getRid, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));

            int excelEmptyRow = 1;
            for (BizReceiptMatApplyItemExportVO importPO : matApplyItemImportPOList) {
                if (UtilString.isNullOrEmpty(importPO.getMatName()) ||
                        UtilString.isNullOrEmpty(importPO.getMatNameEnShort())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_REQUIRED_MSG, String.valueOf(excelEmptyRow));
                }
                BizReceiptMatApplyItemDTO itemDTO = matNameMap.get(importPO.getRid());
                if (UtilObject.isNotNull(itemDTO)) {
                    itemDTO.setMatCode(importPO.getMatCode());
                    itemDTOList.add(itemDTO);
                } else {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_NOT_EXIST, importPO.getRid());
                }
                excelEmptyRow++;
            }

            // 设置上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(itemDTOList));

        } catch (IOException e) {
            log.error("导入物料编码申请异常", e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_READ_EXCEPTION);
        }
    }

}
