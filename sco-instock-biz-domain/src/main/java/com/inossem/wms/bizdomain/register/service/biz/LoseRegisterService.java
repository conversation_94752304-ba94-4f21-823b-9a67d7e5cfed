package com.inossem.wms.bizdomain.register.service.biz;

import com.inossem.wms.bizdomain.register.service.component.LoseRegisterComponent;
import com.inossem.wms.bizdomain.register.service.component.RegisterCommonComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 遗失登记 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-10
 */
@Service
public class LoseRegisterService {

    @Autowired
    protected RegisterCommonComponent registerCommonComponent;

    @Autowired
    protected LoseRegisterComponent loseRegisterComponent;

    /**
     * 查询遗失类型下拉
     *
     * @return 遗失类型下拉框
     */
    public void getLoseTypeDown(BizContext ctx) {

        // 查询遗失类型下拉
        loseRegisterComponent.getLoseTypeDown(ctx);

    }

    /**
     * 遗失登记-初始化
     *
     * @return 遗失登记单
     */
    public void init(BizContext ctx) {

        // 页面初始化
        loseRegisterComponent.setInit(ctx);

    }

    /**
     * 查询遗失登记单列表-分页
     *
     * @param ctx-po 登记单分页查询入参
     * @return 遗失登记单列表
     */
    public void getPage(BizContext ctx) {

        // 查询遗失登记单列表-分页
        loseRegisterComponent.setPage(ctx);

    }

    /**
     * 查询遗失登记单详情
     *
     * @param ctx-id 遗失登记单抬头表主键
     * @return 遗失登记单详情
     */
    public void getInfo(BizContext ctx) {

        // 遗失登记单详情
        loseRegisterComponent.getInfo(ctx);

        // 设置详情页单据流
        registerCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        registerCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        registerCommonComponent.setExtendOperationLog(ctx);

        // 开启审批
        registerCommonComponent.setExtendWf(ctx);

    }

    /**
     * 遗失登记-保存
     *
     * @param ctx-po 保存遗失登记表单参数
     * @return ctx-receiptCode 遗失登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 登记单保存校验
        registerCommonComponent.checkSaveRegister(ctx);

        // 保存登记单
        registerCommonComponent.saveApply(ctx);

        // 保存附件
        registerCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 遗失登记-提交
     *
     * @param ctx-po 提交遗失登记表单参数
     * @return ctx-receiptCode 遗失登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 登记单保存校验
        registerCommonComponent.checkSaveRegister(ctx);

        // 提交遗失登记单
        loseRegisterComponent.submitLoseRegister(ctx);

        // 保存附件
        registerCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

        // 发起审批
        loseRegisterComponent.startWorkFlow(ctx);

        // edit by ChangBaoLong 由于加了审批流，下面这部分注释掉了，如果注错了那也没找我，找杨鑫，杨鑫给你解决一切bug
/*

        // 生成移动类型
        loseRegisterComponent.generateInsMoveTypeAndCheck(ctx);

        // InStock过账
        registerCommonComponent.postToInStock(ctx);

        // 单据状态已完成
        registerCommonComponent.updateStatusCompleted(ctx);
*/
        // edit by ChangBaoLong 由于加了审批流，下面这部分注释掉了，如果注错了那也没找我，找杨鑫，杨鑫给你解决一切bug

    }

    /**
     * 遗失登记-过账
     *
     * @param ctx-po 提交遗失登记表单参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {

        // 生成移动类型
        loseRegisterComponent.generateInsMoveTypeAndCheck(ctx);

        // InStock过账
        registerCommonComponent.postToInStock(ctx);

        // 单据状态已完成
        registerCommonComponent.updateStatusCompleted(ctx);

    }

    /**
     * 遗失登记-删除
     *
     * @param ctx-id 遗失登记单抬头表主键
     * @return ctx-receiptCode 遗失登记单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 刪除登记单
        registerCommonComponent.deleteInfo(ctx);

        // 删除登记单单据流
        registerCommonComponent.deleteReceiptTree(ctx);

        // 删除登记单附件
        registerCommonComponent.deleteReceiptAttachment(ctx);

        // 保存操作日志
        registerCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 遗失登记-前续单据
     *
     * @param ctx-po 查询条件
     * @return 遗失登记行项目
     */
    public void getPreReceiptItem(BizContext ctx) {

        // 查询前序单据
        loseRegisterComponent.getPreReceiptItem(ctx);

    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    @WmsMQListener(tags = TagConst.APPROVAL_CALLBACK_LOSE_REGISTER)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {

        // 审批回调
        loseRegisterComponent.approvalCallback(wfReceiptCo);

    }

    /**
     * 遗失登记-冲销
     *
     * @param ctx-po 冲销入参
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 生成冲销移动类型
        loseRegisterComponent.generateInsWriteOffMoveTypeAndCheck(ctx);

        // InStock过账
        registerCommonComponent.writeOffToInStock(ctx);

        // 单据状态已冲销
        registerCommonComponent.updateStatusWriteOff(ctx);

        // 更新上游单据的状态 —— 归还单对应行项目和抬头状态修改为草稿
        registerCommonComponent.updateUpstreamReceiptStatusByLoseWriteOff(ctx);

    }

}
