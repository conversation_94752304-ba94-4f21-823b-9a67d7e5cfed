package com.inossem.wms.bizdomain.register.controller;

import com.inossem.wms.bizdomain.register.service.biz.DamageRegisterService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptRegisterSearchPO;
import com.inossem.wms.common.model.bizdomain.register.po.BizReceiptSearchPrePO;
import com.inossem.wms.common.model.bizdomain.register.vo.BizRecieptRegisterPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.enums.register.LoseTypeMapVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 损坏登记 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-14
 */
@RestController
public class DamageRegisterController {

    @Autowired
    protected DamageRegisterService damageRegisterService;

    /**
     * 查询损坏类型下拉
     *
     * @return 损坏类型下拉框
     */
    @ApiOperation(value = "查询损坏类型下拉", tags = {"工器具管理-损坏登记"})
    @GetMapping(path = "/register/damage-register/damage-type-down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<LoseTypeMapVO>> getDamageTypeDown(BizContext ctx) {
        damageRegisterService.getDamageTypeDown(ctx);
        MultiResultVO<LoseTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 损坏登记-初始化
     *
     * @return 损坏登记单
     */
    @ApiOperation(value = "损坏登记-初始化", tags = {"工器具管理-损坏登记"})
    @GetMapping(value = "/register/damage-register/inits", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRegisterHeadDTO>> init(BizContext ctx) {
        damageRegisterService.init(ctx);
        BizResultVO<BizReceiptRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询损坏登记单列表-分页
     *
     * @param po 登记单分页查询入参
     * @return 损坏登记单列表
     */
    @ApiOperation(value = "查询损坏登记单列表-分页", tags = {"工器具管理-损坏登记"})
    @PostMapping(value = "/register/damage-register/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizRecieptRegisterPageVO>> getPage(@RequestBody BizReceiptRegisterSearchPO po, BizContext ctx) {
        damageRegisterService.getPage(ctx);
        PageObjectVO<BizRecieptRegisterPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询损坏登记单详情
     *
     * @param id 损坏登记单抬头表主键
     * @return 损坏登记单详情
     */
    @ApiOperation(value = "查询损坏登记单详情", tags = {"工器具管理-损坏登记"})
    @GetMapping(value = "/register/damage-register/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptRegisterHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        damageRegisterService.getInfo(ctx);
        BizResultVO<BizReceiptRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 损坏登记-保存
     *
     * @param po 保存损坏登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "损坏登记-保存", tags = {"工器具管理-损坏登记"})
    @PostMapping(value = "/register/damage-register/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        damageRegisterService.save(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SAVE_SUCCESS, receiptCode);
    }

    /**
     * 损坏登记-提交
     *
     * @param po 提交损坏登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "损坏登记-提交", tags = {"工器具管理-损坏登记"})
    @PostMapping(value = "/register/damage-register/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        damageRegisterService.submit(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_SUBMIT_SUCCESS, receiptCode);
    }

    /**
     * 损坏登记-过账
     *
     * @param po 提交损坏登记表单参数
     * @return 国际化提示
     */
    @ApiOperation(value = "损坏登记-过账", tags = {"工器具管理-损坏登记"})
    @PostMapping(value = "/register/damage-register/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult post(@RequestBody BizReceiptRegisterHeadDTO po, BizContext ctx) {
        damageRegisterService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 损坏登记-删除
     *
     * @param id 损坏登记单抬头表主键
     * @return 国际化提示
     */
    @ApiOperation(value = "损坏登记-删除", tags = {"工器具管理-损坏登记"})
    @DeleteMapping(value = "/register/damage-register/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> delete(@PathVariable("id") Long id, BizContext ctx) {
        damageRegisterService.delete(ctx);
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_DELETE_SUCCESS, receiptCode);
    }

    /**
     * 损坏登记-前续单据
     *
     * @param po 查询条件
     * @return 损坏登记行项目
     */
    @ApiOperation(value = "损坏登记-前续单据", tags = {"工器具管理-损坏登记"})
    @PostMapping(value = "/register/damage-register/pre-receipt-item", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptRegisterItemDTO>> getPreReceiptItem(@RequestBody BizReceiptSearchPrePO po, BizContext ctx) {
        damageRegisterService.getPreReceiptItem(ctx);
        MultiResultVO<BizReceiptRegisterItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 损坏登记-冲销
     *
     * @param po 查询条件
     * @return 损坏登记行项目
     */
    @ApiOperation(value = "损坏登记-冲销", tags = {"工器具管理-损坏登记"})
    @PostMapping(value = "/register/damage-register/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        damageRegisterService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_REGISTER_WRITEOFF_SUCCESS, po.getReceiptCode());
    }
}
