package com.inossem.wms.bizdomain.output.service.biz;


import com.alibaba.excel.metadata.Head;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReserveReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReserveReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicFactoryDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputBinDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.po.InitOutputImportPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.erp.dto.ErpReserveReceiptHeadDTO;
import com.inossem.wms.common.model.erp.dto.ErpReserveReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpReserveReceiptItem;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.factory.entity.DicFactory;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.excel.UtilExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * yaCoo
 */
@Service
public class InitOutputService {

    @Autowired
    DicMaterialDataWrap dicMaterialDataWrap;
    @Autowired
    DicFactoryDataWrap dicFactoryDataWrap;
    @Autowired
    DicStockLocationDataWrap dicStockLocationDataWrap;
    @Autowired
    DictionaryService dictionaryService;
    @Autowired
    BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    BizCommonService bizCommonService;
    @Autowired
    BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    BizReceiptOutputBinDataWrap bizReceiptOutputBinDataWrap;
    @Autowired
    ErpReserveReceiptHeadDataWrap erpReserveReceiptHeadDataWrap;
    @Autowired
    ErpReserveReceiptItemDataWrap erpReserveReceiptItemDataWrap;

    @Transactional
    public void importOutputInit(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);

        try {
            //获取EXCEL数据
            List<InitOutputImportPO> importList = (List<InitOutputImportPO>) UtilExcel.readExcelData(file.getInputStream(), InitOutputImportPO.class);
            //判断EXCEL中主键重复的值
            Map<String, List<InitOutputImportPO>> checkMap = importList.stream().collect(Collectors.groupingBy(item -> item.getMatDocCode() + "-" + item.getMatDocRid()));
            for (String key : checkMap.keySet()) {
                List<InitOutputImportPO> checkList = checkMap.get(key);
                if (checkList.size() > 1) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE, key);
                }
            }
            /**
             * 0、校验库存地点和工厂编码，物料，不存在直接异常
             * 1、批量保存批次信息
             * 1.1、批量生成预留信息
             * 2、将收集到的批次信息id写入出库单的bin表，构造bin,item,head
             * 3、批量保存出库
             */

            //0、校验库存地点和工厂编码，物料，不存在直接异常
            Set<String> matCodeSet = new HashSet<>();
            Set<String> ftyCodeSet = new HashSet<>();
            Set<String> locationCodeSet = new HashSet<>();
            //收集excel里面的物料，工厂，库存地点编码
            importList.forEach(u -> {
                matCodeSet.add(u.getMatCode());
                ftyCodeSet.add(u.getFtyCode());
                locationCodeSet.add(u.getLocationCode());
            });
            //查询数据库中的物料，如果查出来的数量比物料编码的数量少，则有物料不存在
            QueryWrapper<DicMaterial> materialQueryWrapper = new QueryWrapper<>();
            materialQueryWrapper.lambda().in(DicMaterial::getMatCode, matCodeSet);
            List<DicMaterial> materials = dicMaterialDataWrap.list(materialQueryWrapper);
            if (materials.size() < matCodeSet.size()) {
                Set<String> existsSet = materials.stream().map(DicMaterial::getMatCode).collect(Collectors.toSet());
                matCodeSet.removeAll(existsSet);
                throw new WmsException("物料编码找不到:" + matCodeSet);
            }
            Map<String, DicMaterial> materialMaps = materials.stream().collect(Collectors.toMap(DicMaterial::getMatCode, u->u));
            //查询工厂
            QueryWrapper<DicFactory> ftyQueryWrapper = new QueryWrapper<>();
            ftyQueryWrapper.lambda().in(DicFactory::getFtyCode, ftyCodeSet).select(DicFactory::getId, DicFactory::getFtyCode);
            List<DicFactory> factories = dicFactoryDataWrap.list(ftyQueryWrapper);
            if (factories.size() < ftyCodeSet.size()) {
                Set<String> existsSet = factories.stream().map(DicFactory::getFtyCode).collect(Collectors.toSet());
                ftyCodeSet.removeAll(existsSet);
                throw new WmsException("工厂编码找不到:" + ftyCodeSet);
            }
            Map<String, Long> ftyMap = factories.stream().collect(Collectors.toMap(DicFactory::getFtyCode, DicFactory::getId));
            //查询库存地点
            QueryWrapper<DicStockLocation> locQueryWrapper = new QueryWrapper<>();
            locQueryWrapper.lambda().in(DicStockLocation::getLocationCode, locationCodeSet).in(DicStockLocation::getFtyId,ftyMap.values());
            List<DicStockLocation> locations = dicStockLocationDataWrap.list(locQueryWrapper);
            if (locations.size() < locationCodeSet.size()) {
                Set<String> existsSet = locations.stream().map(DicStockLocation::getLocationCode).collect(Collectors.toSet());
                locationCodeSet.removeAll(existsSet);
                throw new WmsException("库存地点编码找不到:" + locationCodeSet);
            }
            Map<String, DicStockLocation> locationMap = locations.stream().collect(Collectors.toMap(u->u.getFtyId()+u.getLocationCode(),u->u));


            //1、批量保存批次信息
            List<String> batchCodes = importList.stream().map(InitOutputImportPO::getBatchCode).collect(Collectors.toList());
            List<BizBatchInfo> existsBatchList = bizBatchInfoDataWrap.list(new QueryWrapper<BizBatchInfo>() {{
                lambda().in(BizBatchInfo::getBatchCode, batchCodes);
            }});
            existsBatchList.forEach(bizBatchInfo->{
                String key = bizBatchInfo.getMatId()+bizBatchInfo.getFtyId()+bizBatchInfo.getBatchCode()+bizBatchInfo.getSpecStock()+bizBatchInfo.getSpecStockCode();
                bizBatchInfo.setDescription(key);
            });
            List<String> existsList = existsBatchList.stream().map(bizBatchInfo -> bizBatchInfo.getMatId() + bizBatchInfo.getFtyId() + bizBatchInfo.getBatchCode() + bizBatchInfo.getSpecStock() + bizBatchInfo.getSpecStockCode()).collect(Collectors.toList());
            Set<String> batchUkSet = new HashSet();
            List<BizBatchInfo> batchinfos = new ArrayList<>();
            importList.forEach(u -> {
                BizBatchInfo bizBatchInfo = new BizBatchInfo();
                bizBatchInfo.setBatchCode(u.getBatchCode());
                bizBatchInfo.setFtyId(ftyMap.get(u.getFtyCode()));
                bizBatchInfo.setMatId(materialMaps.get(u.getMatCode()).getId());
                bizBatchInfo.setSpecStock("Q");
                bizBatchInfo.setSpecStockCode(u.getWbsCode());
                DicMaterialFactoryDTO dicMaterialFactory = dictionaryService.getDicMaterialFactoryByUniqueKey(bizBatchInfo.getMatId(), bizBatchInfo.getFtyId());
                if (UtilObject.isNull(dicMaterialFactory)) {
                    throw new WmsException("物料工厂没找到:" + u.getMatCode() + "-" + u.getFtyCode());
                }
                bizBatchInfo.setIsSingle(dicMaterialFactory.getIsSingle());
                bizBatchInfo.setTagType(dicMaterialFactory.getTagType());
                String key = bizBatchInfo.getMatId()+bizBatchInfo.getFtyId()+bizBatchInfo.getBatchCode()+bizBatchInfo.getSpecStock()+bizBatchInfo.getSpecStockCode();
                bizBatchInfo.setDescription(key);


                if(!batchUkSet.contains(key)&&!existsList.contains(key)){
                    batchinfos.add(bizBatchInfo);
                    batchUkSet.add(key);
                }
            });
            if (UtilCollection.isNotEmpty(batchinfos)) {
                bizBatchInfoDataWrap.saveBatchDto(batchinfos);
            }
            batchinfos.addAll(existsBatchList);
            Map<String, Long> batchMap = batchinfos.stream().collect(Collectors.toMap(BizBatchInfo::getDescription, BizBatchInfo::getId));
            //1.1、批量生成预留信息
            Map<String, List<InitOutputImportPO>> reserves = importList.stream().collect(Collectors.groupingBy(InitOutputImportPO::getReserveCode));
            List<ErpReserveReceiptHeadDTO>  reserveList = new ArrayList<>();
            reserves.forEach((key,imports)->{
                ErpReserveReceiptHeadDTO dto = new ErpReserveReceiptHeadDTO();
                dto.setReceiptCode(key);
                dto.setReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue());
                dto.setErpReceiptType(String.valueOf(EnumReceiptType.RESERVE_RECEIPT.getValue()));
                dto.setErpReceiptTypeName("期初领料单");
                dto.setIsReturnFlag(0);
                dto.setCreateTime(new Date());
                dto.setModifyTime(new Date());
                dto.setErpCreateTime(new Date());
                dto.setItemDTOList(new ArrayList<>());

                Set<String> ridSet = new HashSet<>();
                imports.forEach(importData->{
                    if(!ridSet.contains(importData.getReserveRid())){
                        ErpReserveReceiptItemDTO item = new ErpReserveReceiptItemDTO();
                        item.setRid(importData.getReserveRid());
                        item.setWbsCode(importData.getWbsCode());
                        item.setSpecStock("Q");
                        item.setSpecStockCode(importData.getWbsCode());
                        item.setFtyId(ftyMap.get(importData.getFtyCode()));
                        DicStockLocation dicStockLocation = locationMap.get(item.getFtyId()+importData.getLocationCode());
                        item.setLocationId(dicStockLocation.getId());
                        item.setWhId(dicStockLocation.getWhId());
                        item.setMatId(materialMaps.get(importData.getMatCode()).getId());
                        item.setCreateTime(new Date());
                        item.setModifyTime(new Date());
                        item.setImportKey(importData.getReserveCode()+"-"+importData.getReserveRid());
                        dto.getItemDTOList().add(item);
                        ridSet.add(importData.getReserveRid());
                    }
                });

                reserveList.add(dto);
            });
            erpReserveReceiptHeadDataWrap.saveBatchDto(reserveList);

            List<ErpReserveReceiptItemDTO> erpItemList = new ArrayList<>();
            reserveList.forEach(u->{
                List<ErpReserveReceiptItemDTO> itemDTOList = u.getItemDTOList();
                itemDTOList.forEach(i->{
                    i.setHeadId(u.getId());
                });
                erpItemList.addAll(itemDTOList);
            });
            erpReserveReceiptItemDataWrap.saveBatchDto(erpItemList);
            Map<String,ErpReserveReceiptItemDTO> erpItemMap = erpItemList.stream().collect(Collectors.toMap(ErpReserveReceiptItemDTO::getImportKey,u->u));


            //2、将收集到的批次信息id写入出库单的bin表，构造bin,item,head
            List<BizReceiptOutputHeadDTO> headDTOList = new ArrayList<>();
            Map<String, List<InitOutputImportPO>> matDocsMap = importList.stream().collect(Collectors.groupingBy(InitOutputImportPO::getMatDocCode));
            Iterator<String> iterator = matDocsMap.keySet().iterator();
            while (iterator.hasNext()){
                String matDoc = iterator.next();
                List<InitOutputImportPO> initOutputImportPOS = matDocsMap.get(matDoc);

                //构造单据头
                BizReceiptOutputHeadDTO outputInfo = new BizReceiptOutputHeadDTO();
                outputInfo.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
                String receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_OUTPUT.getValue());
                outputInfo.setReceiptCode(receiptCode);
                outputInfo.setCreateUserId(1L);
                outputInfo.setCreateTime(new Date());
                outputInfo.setModifyTime(new Date());
                outputInfo.setModifyUserId(1L);
                outputInfo.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                outputInfo.setRemark("期初领料单");
                outputInfo.setItemDTOList(new ArrayList<>());

                initOutputImportPOS.forEach(u -> {
                    //构造item
                    BizReceiptOutputItemDTO itemDto = new BizReceiptOutputItemDTO();
                    itemDto.setQty(u.getQty());
                    itemDto.setReturnQty(u.getReturnQty());
                    itemDto.setFtyId(ftyMap.get(u.getFtyCode()));
                    DicStockLocation dicStockLocation = locationMap.get(itemDto.getFtyId()+u.getLocationCode());
                    itemDto.setLocationId(dicStockLocation.getId());
                    itemDto.setWhId(dicStockLocation.getWhId());
                    DicMaterial material = materialMaps.get(u.getMatCode());
                    itemDto.setMatId(material.getId());
                    itemDto.setMatDocCode(u.getMatDocCode());
                    itemDto.setMatDocRid(u.getMatDocRid());
                    itemDto.setSpecStockCode(u.getWbsCode());
                    itemDto.setSpecStock("Q");
                    itemDto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                    itemDto.setRid(u.getMatDocRid());
                    LocalDateTime now = LocalDateTime.now();
                    Date date = new Date();
                    itemDto.setDocDate(date);
                    itemDto.setPostingDate(date);
                    itemDto.setUnitId(material.getUnitId());
                    itemDto.setMatDocYear(String.valueOf(now.getYear()));
                    itemDto.setIsPost(1);
                    itemDto.setIsWriteOff(0);
                    itemDto.setCreateTime(date);
                    itemDto.setModifyTime(date);
                    itemDto.setCreateUserId(1L);
                    itemDto.setModifyUserId(1L);
                    itemDto.setReferReceiptType(EnumReceiptType.RESERVE_RECEIPT.getValue());
                    String key = u.getReserveCode() + "-" + u.getReserveRid();
                    itemDto.setReferReceiptHeadId(erpItemMap.get(key).getHeadId());
                    itemDto.setReferReceiptItemId(erpItemMap.get(key).getId());

                    outputInfo.getItemDTOList().add(itemDto);

                    //构造行项目bin
                    String batchKey = itemDto.getMatId()+itemDto.getFtyId()+u.getBatchCode()+"Q"+u.getWbsCode();
                    Long batchId = batchMap.get(batchKey);
                    BizReceiptOutputBinDTO outputBinDTO = new BizReceiptOutputBinDTO();
                    outputBinDTO.setBatchId(batchId);
                    outputBinDTO.setQty(u.getQty());
                    outputBinDTO.setTaskQty(u.getQty());
                    outputBinDTO.setReturnQty(u.getReturnQty());
                    outputBinDTO.setTypeId(0L);
                    outputBinDTO.setBinId(0L);
                    outputBinDTO.setMatDocRid(u.getMatDocRid());
                    outputBinDTO.setTaskItemId(0L);
                    outputBinDTO.setCreateTime(new Date());
                    outputBinDTO.setCreateUserId(1L);
                    outputBinDTO.setModifyTime(new Date());
                    outputBinDTO.setModifyUserId(1L);

                    itemDto.setBinDTOList(new ArrayList<BizReceiptOutputBinDTO>(){{add(outputBinDTO);}});
                });

                headDTOList.add(outputInfo);
            }

            //3、批量保存出库
            bizReceiptOutputHeadDataWrap.saveBatchDto(headDTOList);

            List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
            headDTOList.forEach(u->{
                u.getItemDTOList().forEach(i->{
                    i.setHeadId(u.getId());
                });
                itemDTOList.addAll(u.getItemDTOList());
            });

            //item
            bizReceiptOutputItemDataWrap.saveBatchDto(itemDTOList);

            List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
            itemDTOList.forEach(u->{
                u.getBinDTOList().forEach(i->{
                    i.setHeadId(u.getHeadId());
                    i.setItemId(u.getId());
                    i.setBid("1");
                });
                binDTOList.addAll(u.getBinDTOList());
            });

            bizReceiptOutputBinDataWrap.saveBatchDto(binDTOList);

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

}
