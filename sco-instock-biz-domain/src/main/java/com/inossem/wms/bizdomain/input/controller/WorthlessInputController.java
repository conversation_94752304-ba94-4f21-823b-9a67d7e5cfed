package com.inossem.wms.bizdomain.input.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.bizdomain.input.service.biz.WorthlessInputService;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 零价值入库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */

@RestController
@Api(tags = "入库管理-零价值入库")
public class WorthlessInputController {

    @Autowired
    private WorthlessInputService worthlessInputService;

    /**
     * 零价值入库-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "零价值入库-初始化", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        worthlessInputService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 零价值入库-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 零价值入库单分页
     */
    @ApiOperation(value = "零价值入库-分页", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInputHeadVO>> getPage(@RequestBody BizReceiptInputSearchPO po,
        BizContext ctx) {
        worthlessInputService.getPage(ctx);
        PageObjectVO<BizReceiptInputHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 零价值入库-详情
     *
     * @param id 零价值入库主键
     * @param ctx 入参上下文 {"id":"零价值入库主键"}
     */
    @ApiOperation(value = "零价值入库-详情", tags = {"入库管理-零价值入库"})
    @GetMapping(value = "/input/worthless-inputs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        worthlessInputService.getInfo(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 零价值入库-保存
     *
     * @param po 保存零价值入库表单参数
     * @param ctx 入参上下文 {"po":"保存零价值入库表单参数"}
     */
    @ApiOperation(value = "零价值入库-保存", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        worthlessInputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WORTHLESS_INPUT_SAVE_SUCCESS, code);
    }

    /**
     * 零价值入库-提交
     *
     * @param po 提交零价值入库表单参数
     * @param ctx 入参上下文 {"po":"提交零价值入库表单参数"}
     */
    @ApiOperation(value = "零价值入库-提交", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        worthlessInputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WORTHLESS_INPUT_SUBMIT_SUCCESS, code);
    }

    /**
     * 零价值入库-过账
     *
     * @param po 保存零价值入库表单参数
     * @param ctx 入参上下文 {"po":"保存零价值入库表单参数"}
     */
    @ApiOperation(value = "零价值入库-过账", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/post", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> post(@RequestBody BizReceiptInputHeadDTO po, BizContext ctx) {
        worthlessInputService.post(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WORTHLESS_INPUT_POST_SUCCESS, po.getReceiptCode());
    }

    /**
     * 零价值入库-冲销
     *
     * @param po 零价值入库冲销表单参数
     * @param ctx 入参上下文 {"po":"零价值入库冲销表单参数"}
     */
    @ApiOperation(value = "零价值入库-冲销", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/write-off", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInputWriteOffPO po, BizContext ctx) {
        worthlessInputService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WORTHLESS_INPUT_WRITEOFF_SUCCESS, po.getReceiptCode());
    }

    /**
     * 零价值入库单-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "零价值入库单-删除", tags = {"入库管理-零价值入库"})
    @DeleteMapping("/input/worthless-inputs/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        worthlessInputService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_WORTHLESS_INPUT_DELETE_SUCCESS, po.getReceiptCode());
    }

    /**
     * 零价值入库-回填出库物料【批次、批次特性】
     *
     * @param po 入库物料信息
     * @param ctx 入参上下文 {"po":"入库物料信息"}
     */
    @ApiOperation(value = "零价值入库-回填出库物料属性值", tags = {"入库管理-零价值入库"})
    @PostMapping(value = "/input/worthless-inputs/set-mat-info", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptInputItemDTO>> setMatInfo(@RequestBody BizReceiptInputHeadDTO po,
        BizContext ctx) {
        worthlessInputService.setMatInfo(ctx);
        MultiResultVO<BizReceiptInputItemDTO> inputItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(inputItemDTOList);
    }

}