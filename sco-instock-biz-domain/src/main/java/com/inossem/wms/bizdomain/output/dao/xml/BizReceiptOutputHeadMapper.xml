<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.output.dao.BizReceiptOutputHeadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead">
        <id column="id" property="id"/>
        <result column="receipt_code" property="receiptCode"/>
        <result column="receipt_type" property="receiptType"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="modify_user_id" property="modifyUserId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, receipt_code, receipt_type, receipt_status,remark, is_delete, create_time, modify_time, create_user_id, modify_user_id
    </sql>

    <select id="selectOutputPageVoList" resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO">
        select biz_receipt_output_head.*,
            sys_user.user_name as create_user_name,biz_receipt_output_item.move_type_id,
            ah.receipt_code apply_receipt_code
        from biz_receipt_output_head
        inner join biz_receipt_output_item ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
        AND  biz_receipt_output_head.is_delete = 0
        AND  biz_receipt_output_item.is_delete = 0
        inner join sys_user ON biz_receipt_output_head.create_user_id = sys_user.id
        LEFT JOIN biz_receipt_apply_head ah ON ah.id = biz_receipt_output_item.pre_receipt_head_id
        LEFT JOIN dic_material ON biz_receipt_output_item.mat_id = dic_material.id
        AND  sys_user.is_delete = 0
        ${ew.customSqlSegment}
        group by biz_receipt_output_head.id
        order by biz_receipt_output_head.create_time desc
    </select>


    <select id="selectRepairOutputPageVoListByPo" resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO"
            parameterType="com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO">
        select head.*,
        sys_user.user_name as create_user_name
        from biz_receipt_output_head head
        inner join biz_receipt_output_item item ON head.id = item.head_id
        inner join biz_receipt_output_bin bin ON item.id = bin.item_id
        inner join sys_user ON head.create_user_id = sys_user.id
        inner JOIN dic_material dm ON dm.id = item.mat_id
        inner JOIN biz_batch_info bbi on bbi.id = bin.batch_id and bbi.mat_id = item.mat_id
        LEFT JOIN biz_receipt_repair_apply_head app on item.pre_receipt_head_id = app.id
        AND  head.is_delete = 0
        AND  item.is_delete = 0
        AND  sys_user.is_delete = 0
        and dm.is_delete = 0
        and bbi.is_delete = 0
        and app.is_delete = 0
        <where>
            head.receipt_type = #{po.receiptType}
            <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
                AND head.receipt_status in
                <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="po.receiptCode != null and po.receiptCode != '' ">
                AND head.receipt_code = #{po.receiptCode}
            </if>
            <if test="po.toolCode != null and po.toolCode != '' ">
                AND bbi.batch_code = #{po.toolCode}
            </if>
            <if test="po.toolCode != null and po.toolCode != '' ">
                AND app.receipt_code = #{po.preReceiptCode}
            </if>
            <if test="po.createUserName != null and po.createUserName != '' ">
                AND sys_user.user_name = #{po.createUserName}
            </if>
            <if test="po.matName != null and po.matName != '' ">
                AND dm.mat_name like concat( '%',#{po.matName}, '%')
            </if>
            <if test="po.startTime != null and po.endTime != null">
                AND DATE(head.create_time)
                BETWEEN #{po.startTime, jdbcType=TIMESTAMP} AND DATE_ADD(#{po.endTime, jdbcType=TIMESTAMP},INTERVAL
                1 DAY)
            </if>
            <if test="po.locationIdList != null and po.locationIdList.size() > 0">
                AND item.location_id in
                <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by head.id
        order by head.create_time desc
    </select>


    <select id="getToolOutputPageVoList" resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO">
        select biz_receipt_output_head.*,
               sys_user.user_name as create_user_name
        from biz_receipt_output_head
                 inner join biz_receipt_output_item ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
                 inner join sys_user ON biz_receipt_output_head.create_user_id = sys_user.id
                 inner join dic_material ON biz_receipt_output_item.mat_id = dic_material.id
            AND  biz_receipt_output_head.is_delete = 0
            AND  biz_receipt_output_item.is_delete = 0
            AND  sys_user.is_delete = 0
            ${ew.customSqlSegment}
        group by biz_receipt_output_head.id
        order by biz_receipt_output_head.create_time desc
    </select>

    <!-- (批量)出库查询已创建数 -->
    <select id="getWmsCreatedQtyByList" resultType="com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO">
        SELECT
        IFNULL( SUM(biz_receipt_output_item.qty), 0 ) AS created_qty,
        ANY_VALUE ( biz_receipt_output_head.receipt_code ) receipt_code,
        biz_receipt_output_item.pre_receipt_item_id,
        biz_receipt_output_item.pre_receipt_type
        FROM
        biz_receipt_output_head
        LEFT JOIN biz_receipt_output_item ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
        WHERE
        <!-- receipt_status > 10 排除草稿状态的单据  -->
        biz_receipt_output_head.is_delete = 0 AND receipt_status >10
        <if test="id != null">
            AND biz_receipt_output_head.id != #{id}
        </if>
        <!-- mat_doc_code=''排除掉已同步SAP的单据(已同步/冲销) -->
        AND biz_receipt_output_item.mat_doc_code = ''
        <if test=" list!=null and list.size()>0 ">
            AND (biz_receipt_output_item.pre_receipt_item_id,biz_receipt_output_item.pre_receipt_type) in
            <foreach collection="list" open="(" close=")" separator="," item="item">
                (#{item.preReceiptItemId},#{item.preReceiptType})
            </foreach>
        </if>
        GROUP BY biz_receipt_output_item.pre_receipt_item_id,biz_receipt_output_item.pre_receipt_type
    </select>

    <select id="selectBorrowOutputPageVoList" resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO">
        SELECT
        broh.id,
        broh.receipt_code,
        broh.receipt_type,
        broh.receipt_status,
        broh.remark,
        broh.create_time,
        brah.borrow_type,
        su1.user_name createUserName,
        su2.user_name modifyUserName
        FROM biz_receipt_output_head broh
        LEFT JOIN sys_user su1 ON broh.create_user_id = su1.id
        LEFT JOIN sys_user su2 ON broh.modify_user_id = su2.id
        LEFT JOIN biz_receipt_output_item broi ON broh.id = broi.head_id AND broi.is_delete = 0
        LEFT JOIN biz_receipt_output_bin brob ON broh.id = brob.head_id AND brob.is_delete = 0
        LEFT JOIN biz_receipt_apply_head brah ON broi.pre_receipt_head_id = brah.id AND brah.is_delete = 0
        LEFT JOIN biz_batch_info bbi ON brob.batch_id = bbi.id AND bbi.is_delete = 0
        LEFT JOIN dic_material dm ON broi.mat_id = dm.id AND dm.is_delete = 0
        WHERE broh.is_delete = 0
        <if test="po.receiptCode != null and po.receiptCode != ''">
            AND broh.receipt_code = #{po.receiptCode}
        </if>
        <if test="po.receiptType != null and po.receiptType != ''">
            AND broh.receipt_type = #{po.receiptType}
        </if>
        <if test="po.receiptStatusList != null and po.receiptStatusList.size() > 0">
            AND broh.receipt_status in
            <foreach collection="po.receiptStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="po.preReceiptCode != null and po.preReceiptCode != ''">
            AND brah.receipt_code = #{po.preReceiptCode}
        </if>
        <if test="po.toolCode != null and po.toolCode != ''">
            AND bbi.batch_code = #{po.toolCode}
        </if>
        <if test="po.matName != null and po.matName != ''">
            AND dm.mat_name LIKE CONCAT('%',#{po.matName},'%' )
        </if>
        <if test="po.startTime !=null and po.endTime != null ">
            AND DATE_FORMAT(broh.create_time,'%Y-%m-%d') BETWEEN DATE_FORMAT(#{po.startTime},'%Y-%m-%d') AND DATE_FORMAT(#{po.endTime},'%Y-%m-%d')
        </if>
        <if test="po.createUserName != null and po.createUserName != ''">
            AND su1.user_name LIKE CONCAT('%',#{po.createUserName},'%' )
        </if>
        <if test="po.locationIdList != null and po.locationIdList.size() > 0">
            AND broi.location_id in
            <foreach collection="po.locationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY broh.id
        ORDER BY broh.create_time DESC
    </select>
    <select id="selectOutputInfoPageVoList"
            resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO">
        SELECT
            biz_receipt_output_head.*,
            sys_user.user_name AS create_user_name,
            biz_receipt_output_item.move_type_id,
            biz_receipt_apply_head.`out_info_id`,
            biz_receipt_apply_head.`receipt_code` AS pre_receipt_code,
	IFNULL(biz_receipt_apply_head.`receive_type`, IF(biz_receipt_output_head.wbs_id>0, 2, 1)) receive_type,
            `biz_receipt_output_info`.`apply_time`,
            `biz_receipt_output_info`.`mat_dept_id`,
            `biz_receipt_output_info`.`mat_receiver_id`,
            `biz_receipt_output_info`.`receipt_num`,
            `biz_receipt_apply_item`.`reserved_order_code` ,
             dic_dept.dept_name mat_dept_name
        FROM
            biz_receipt_output_head
                INNER JOIN biz_receipt_output_item
                           ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
                LEFT JOIN `biz_receipt_apply_head` ON biz_receipt_output_item.`pre_receipt_head_id` = `biz_receipt_apply_head`.id
                LEFT JOIN `biz_receipt_output_info` ON `biz_receipt_apply_head`.`out_info_id` = `biz_receipt_output_info`.`id`
                LEFT JOIN `dic_dept` dic_dept ON dic_dept.`id` = `biz_receipt_output_info`.`mat_dept_id`
                LEFT JOIN `biz_receipt_apply_item`
                          ON biz_receipt_output_item.`pre_receipt_item_id` = `biz_receipt_apply_item`.`id`
                INNER JOIN sys_user
                           ON biz_receipt_output_head.create_user_id = sys_user.id
                               AND biz_receipt_output_head.is_delete = 0
                               AND biz_receipt_output_item.is_delete = 0
                               AND sys_user.is_delete = 0
                INNER JOIN dic_material
                           ON dic_material.id = biz_receipt_output_item.mat_id
            ${ew.customSqlSegment}
        group by biz_receipt_output_head.id
        order by biz_receipt_output_head.create_time desc

    </select>

    <select id="selectOutputInfoPageVoListUnitized"
            resultType="com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO">
        SELECT
            biz_receipt_output_head.*,
            sys_user.user_name AS create_user_name,
            biz_receipt_output_item.move_type_id,
            biz_receipt_apply_head.`out_info_id`,
            biz_receipt_apply_head.`island`,
            biz_receipt_apply_head.`is_exact`,
            biz_receipt_apply_head.`receipt_code` AS pre_receipt_code,
            `biz_receipt_output_info`.`apply_time`,
            `biz_receipt_output_info`.`mat_dept_id`,
            `biz_receipt_output_info`.`mat_receiver_id`,
            `biz_receipt_output_info`.`receipt_num`,
            `biz_receipt_apply_item`.`reserved_order_code` ,
            dic_dept.dept_name mat_dept_name
        FROM
            biz_receipt_output_head
                INNER JOIN biz_receipt_output_item ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
                LEFT JOIN biz_receipt_require_head ON biz_receipt_require_head.id = biz_receipt_output_item.refer_receipt_head_id
                LEFT JOIN biz_receipt_require_item ON biz_receipt_require_item.id = biz_receipt_output_item.refer_receipt_item_id
                LEFT JOIN `biz_receipt_apply_head` ON biz_receipt_output_item.`pre_receipt_head_id` = `biz_receipt_apply_head`.id
                LEFT JOIN `biz_receipt_apply_transfer` ON `biz_receipt_apply_head`.`id` = `biz_receipt_apply_transfer`.`receipt_head_id`
                LEFT JOIN dic_material  ON biz_receipt_apply_transfer.mat_id = dic_material.id
                LEFT JOIN `biz_receipt_output_info` ON `biz_receipt_apply_head`.`out_info_id` = `biz_receipt_output_info`.`id`
                LEFT JOIN `dic_dept` dic_dept ON dic_dept.`id` = `biz_receipt_output_info`.`mat_dept_id`
                LEFT JOIN `biz_receipt_apply_item` ON biz_receipt_output_item.`pre_receipt_item_id` = `biz_receipt_apply_item`.`id`
                LEFT JOIN biz_receipt_apply_bin ON biz_receipt_apply_head.id = biz_receipt_apply_bin.head_id
                LEFT JOIN dic_material p ON p.id = biz_receipt_apply_bin.mat_id
                INNER JOIN sys_user
                           ON biz_receipt_output_head.create_user_id = sys_user.id
                               AND biz_receipt_output_head.is_delete = 0
                               AND biz_receipt_output_item.is_delete = 0
                               AND sys_user.is_delete = 0
            ${ew.customSqlSegment}
        group by biz_receipt_output_head.id
        order by biz_receipt_output_head.create_time desc

    </select>

    <select id="selectRemarkByOutPutId" resultType="java.lang.String">
        select
            receipt_remark
        from
            biz_receipt_output_info broi
        where
                id in (
                select
                    brah.out_info_id
                from
                    biz_receipt_apply_head brah
                where
                        id in (
                        select
                            broi2.pre_receipt_head_id
                        from
                            biz_receipt_output_item broi2 where broi2.id = #{id}))  limit 1
    </select>

    <update id="updateStatusById">
        update biz_receipt_output_head set receipt_status=#{status} where id=#{id}
    </update>

    <select id="getOutputItemDTOList" resultType="com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO">
        SELECT
            biz_receipt_output_item.*,
            biz_receipt_output_head.receipt_status,
            dic_material.mat_code,
            dic_material.mat_name,
            dic_unit.unit_code,
            dic_unit.unit_name,
            dic_unit.decimal_place
        FROM
            biz_receipt_output_head
                INNER JOIN biz_receipt_output_item ON biz_receipt_output_head.id = biz_receipt_output_item.head_id
                AND biz_receipt_output_head.is_delete = 0
                AND biz_receipt_output_item.is_delete = 0
                INNER JOIN dic_material ON dic_material.id = biz_receipt_output_item.mat_id
                INNER JOIN dic_unit ON dic_unit.id = dic_material.unit_id
        <where>
            <if test="po.matCode != null and po.matCode != '' ">
                AND dic_material.mat_code = #{po.matCode}
            </if>
            <if test="po.matName != null and po.matName != ''">
                AND dic_material.mat_name LIKE concat( '%',#{po.matName}, '%')
            </if>
            <if test="po.preReceiptCode != null and po.preReceiptCode != '' ">
                AND biz_receipt_output_head.receipt_code = #{po.preReceiptCode}
            </if>
            <if test="po.receiptType != null and po.receiptType != '' ">
                AND biz_receipt_output_head.receipt_type = #{po.receiptType}
            </if>
            <if test="po.receiptStatus != null and po.receiptStatus != '' ">
                AND biz_receipt_output_head.receipt_status = #{po.receiptStatus}
            </if>
            <if test="po.des != null and po.des != '' ">
                AND biz_receipt_output_head.des LIKE concat( '%',#{po.des}, '%')
            </if>
        </where>

        ORDER BY biz_receipt_output_head.create_time DESC

    </select>
</mapper>
