package com.inossem.wms.bizdomain.input.service.component.movetype;

import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import org.springframework.beans.factory.annotation.Autowired;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizdomain.task.service.component.TaskComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputBinDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;

/**
 * <p>
 * 采购入库冲销过账移动类型组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class PurchaseInputWriteOffMoveTypeComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private TaskComponent taskComponent;

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 采购入库冲销过账凭证
     *
     * @param headDTO 采购入库信息
     * @return ins凭证
     */
    public StockInsMoveTypeDTO generateInsDocToPost(BizReceiptInputHeadDTO headDTO) {
        if (UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 装载批次库存凭证、仓位库存凭证
        StockInsMoveTypeDTO stockInsMoveTypeDTO = new StockInsMoveTypeDTO();
        // 装载批次库存凭证集合
        List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
        // 装载仓位库存凭证集合
        List<StockInsDocBin> insDocBinList = new ArrayList<>();
        // 凭证编码
        String insDocCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
        // 凭证序号
        AtomicInteger insDocRid = new AtomicInteger(1);
        for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {
            if (UtilCollection.isEmpty(inputItemDTO.getBinList())) {
                /* ***** 先过账后作业模式 or 先作业后过账模式 ***** */
                // 批次库存凭证 - 扣减
                insDocBatchList.add(this.getInputInsBatch(headDTO, inputItemDTO, insDocCode, insDocRid.get()));
                // 获取已作业入库单
                List<BizReceiptTaskItemDTO> taskItemDTOList = taskComponent
                    .getTaskItemByPreReceiptItemIds(new ArrayList<>(Collections.singletonList(inputItemDTO.getId())));
                taskItemDTOList.forEach(taskItemDTO -> {
                    // 实际入库仓位凭证 - 扣减
                    insDocBinList.add(this.getInputInsBin(headDTO, inputItemDTO, taskItemDTO, null, insDocCode,
                        insDocRid.getAndIncrement()));
                });
                // 临时仓位801凭证 - 扣减
                insDocBinList.add(
                    this.getInputInsBin(headDTO, inputItemDTO, null, null, insDocCode, insDocRid.getAndIncrement()));
            } else {
                /* ***** 同时过账作业模式 ***** */
                inputItemDTO.getBinList().forEach(binDTO -> {
                    // 批次库存凭证 - 扣减
                    insDocBatchList.add(this.getInputInsBatch(headDTO, inputItemDTO, insDocCode, insDocRid.get()));
                    // 实际入库仓位凭证 - 扣减
                    insDocBinList.add(this.getInputInsBin(headDTO, inputItemDTO, null, binDTO, insDocCode,
                        insDocRid.getAndIncrement()));
                });
            }
        }
        // 设置批次库存凭证
        stockInsMoveTypeDTO.setInsDocBatchList(insDocBatchList);
        // 设置仓位库存凭证
        stockInsMoveTypeDTO.setInsDocBinList(insDocBinList);
        return stockInsMoveTypeDTO;
    }

    /**
     * 冲销批次库存凭证
     *
     * @param headDTO 采购入库单
     * @param inputItemDTO 采购入库单行项目
     * @param insDocCode 采购入库单号
     * @param insDocRid 采购入库单序号
     * @return 批次库存凭证
     */
    private StockInsDocBatch getInputInsBatch(BizReceiptInputHeadDTO headDTO, BizReceiptInputItemDTO inputItemDTO,
        String insDocCode, int insDocRid) {
        StockInsDocBatch insDocBatch = new StockInsDocBatch();
        // 凭证编码
        insDocBatch.setInsDocCode(insDocCode);
        // 凭证序号
        insDocBatch.setInsDocRid(UtilObject.getStringOrEmpty(insDocRid));
        // 物料
        insDocBatch.setMatId(inputItemDTO.getMatId());
        // 批次
        insDocBatch.setBatchId(inputItemDTO.getBatchId());
        // 工厂
        insDocBatch.setFtyId(inputItemDTO.getFtyId());
        // 库存地点
        insDocBatch.setLocationId(inputItemDTO.getLocationId());
        // 单位
        insDocBatch.setUnitId(inputItemDTO.getUnitId());
        insDocBatch.setDecimalPlace(inputItemDTO.getDecimalPlace());
        // 移动数量
        insDocBatch.setMoveQty(inputItemDTO.getQty());
        // 库存状态
        insDocBatch.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 前续单据
        insDocBatch.setPreReceiptCode(headDTO.getReceiptCode());
        insDocBatch.setPreReceiptHeadId(headDTO.getId());
        insDocBatch.setPreReceiptItemId(inputItemDTO.getId());
        insDocBatch.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据
        insDocBatch.setReferReceiptHeadId(inputItemDTO.getReferReceiptHeadId());
        insDocBatch.setReferReceiptItemId(inputItemDTO.getReferReceiptItemId());
        insDocBatch.setReferReceiptType(inputItemDTO.getReferReceiptType());
        // 冲销标识
        insDocBatch.setIsWriteOff(inputItemDTO.getIsWriteOff());
        // 冲销 - 扣减
        insDocBatch.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        // 创建人
        insDocBatch.setCreateUserId(inputItemDTO.getCreateUserId());
        return insDocBatch;
    }

    /**
     * 冲销仓位库存凭证
     *
     * @param headDTO 验收入库单
     * @param inputItemDTO 验收入库单行项目
     * @param taskItemDTO 验收入库作业行项目
     * @param binDTO 验收入库仓位
     * @param insDocCode 验收入库单号
     * @param insDocRid 验收入库单序号
     * @return 冲销仓位库存凭证
     */
    private StockInsDocBin getInputInsBin(BizReceiptInputHeadDTO headDTO, BizReceiptInputItemDTO inputItemDTO,
        BizReceiptTaskItemDTO taskItemDTO, BizReceiptInputBinDTO binDTO, String insDocCode, int insDocRid) {
        StockInsDocBin insDocBin = new StockInsDocBin();
        // 凭证编码
        insDocBin.setInsDocCode(insDocCode);
        // 凭证序号
        insDocBin.setInsDocRid(String.valueOf(insDocRid));
        // 物料
        insDocBin.setMatId(inputItemDTO.getMatId());
        // 批次
        insDocBin.setBatchId(inputItemDTO.getBatchId());
        // 工厂
        insDocBin.setFtyId(inputItemDTO.getFtyId());
        // 库存地点
        insDocBin.setLocationId(inputItemDTO.getLocationId());
        // 仓库
        insDocBin.setWhId(inputItemDTO.getWhId());
        // 单位
        insDocBin.setUnitId(inputItemDTO.getUnitId());
        insDocBin.setDecimalPlace(inputItemDTO.getDecimalPlace());
        // 库存状态
        insDocBin.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 前续单据
        insDocBin.setPreReceiptHeadId(headDTO.getId());
        insDocBin.setPreReceiptItemId(inputItemDTO.getId());
        insDocBin.setPreReceiptType(headDTO.getReceiptType());
        // 参考单据
        insDocBin.setReferReceiptHeadId(inputItemDTO.getReferReceiptHeadId());
        insDocBin.setReferReceiptItemId(inputItemDTO.getReferReceiptItemId());
        insDocBin.setReferReceiptType(inputItemDTO.getReferReceiptType());
        // 冲销标识
        insDocBin.setIsWriteOff(inputItemDTO.getIsWriteOff());
        // 冲销 - 扣减
        insDocBin.setDebitCredit(Const.CREDIT_H_SUBTRACT);
        // 创建人
        insDocBin.setCreateUserId(inputItemDTO.getCreateUserId());
        if (UtilObject.isNull(binDTO)) {
            if (UtilObject.isNull(taskItemDTO)) {
                String typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                String binCode = EnumDefaultStorageType.INPUT.getBinCode();
                // 入库临时存储类型
                insDocBin.setTypeId(dictionaryService.getStorageTypeIdCacheByCode(inputItemDTO.getWhCode(),
                    EnumDefaultStorageType.INPUT.getTypeCode()));
                // 入库临时仓位
                insDocBin.setBinId(dictionaryService.getBinIdCacheByCode(inputItemDTO.getWhCode(), typeCode, binCode));
                // 入库临时托盘
                insDocBin.setCellId(0L);
                // 入库临时区剩余数量
                insDocBin.setMoveQty(inputItemDTO.getQty().subtract(inputItemDTO.getTaskQty()));
            } else {
                // 已作业存储类型
                insDocBin.setTypeId(taskItemDTO.getTargetTypeId());
                // 已作业仓位
                insDocBin.setBinId(taskItemDTO.getTargetBinId());
                // 已作业托盘
                insDocBin.setCellId(taskItemDTO.getTargetCellId());
                // 已作业数量
                insDocBin.setMoveQty(taskItemDTO.getQty());
            }
        } else {
            // 入库存储类型
            insDocBin.setTypeId(binDTO.getTypeId());
            // 入库仓位
            insDocBin.setBinId(binDTO.getBinId());
            // 入库托盘
            insDocBin.setCellId(binDTO.getCellId());
            // 入库移动数量
            insDocBin.setMoveQty(binDTO.getQty());
        }
        return insDocBin;
    }

}