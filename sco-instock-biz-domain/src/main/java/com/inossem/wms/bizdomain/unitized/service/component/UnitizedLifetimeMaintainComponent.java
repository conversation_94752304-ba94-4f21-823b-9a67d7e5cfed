package com.inossem.wms.bizdomain.unitized.service.component;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.lifetime.service.datawrap.BizReceiptLifetimeHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumScrapFreezeCauseType;
import com.inossem.wms.common.enums.EnumSpecStock;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.lifetime.EnumInspectResult;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeHeadDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.dto.BizReceiptLifetimeItemDTO;
import com.inossem.wms.common.model.bizdomain.lifetime.po.BizReceiptLifetimeSearchPO;
import com.inossem.wms.common.model.bizdomain.lifetime.vo.BizReceiptLifetimePageVO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFactory;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilMybatisPlus;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 寿期维护 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-05-23
 */
@Service
public class UnitizedLifetimeMaintainComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected StockCommonService stockCommonService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected LabelDataService labelDataService;

    @Autowired
    protected UnitizedLifetimeCommonComponent lifetimeCommonComponent;

    @Autowired
    protected BizReceiptLifetimeHeadDataWrap bizReceiptLifetimeHeadDataWrap;

    @Autowired
    protected DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;

    /**
     * 查询检定结果下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumInspectResult.toList()":"检定结果下拉框")}
     */
    public void getinspectResultDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumInspectResult.toList()));
    }

    /**
     * 查询报废原因下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumScrapFreezeCauseType.toList()":"报废原因下拉框")}
     */
    public void getscrapCauseDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumScrapFreezeCauseType.toList()));
    }

    /**
     * 查询寿期维护列表-分页
     *
     * @in ctx 入参 {@link BizReceiptLifetimeSearchPO :"寿期分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptLifetimeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptLifetimePageVO> page = po.getPageObj(BizReceiptLifetimePageVO.class);
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        bizReceiptLifetimeHeadDataWrap.getPageVOListUnitized(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 寿期维护单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"寿期维护单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取寿期维护单详情
        BizReceiptLifetimeHeadDTO headDTO = UtilBean.newInstance(bizReceiptLifetimeHeadDataWrap.getById(headId), BizReceiptLifetimeHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置寿期
        headDTO.getItemList().forEach(p -> p.setShelfLifeMax(UtilObject.isNotNull(dictionaryService.getMatCacheById(p.getMatId()))
                ? dictionaryService.getMatCacheById(p.getMatId()).getShelfLifeMax() : 0));
        for (BizReceiptLifetimeItemDTO bizReceiptLifetimeItemDTO : headDTO.getItemList()) {
            // 计算到期日
          //  bizReceiptLifetimeItemDTO.setExpireDate(UtilDate.plusMonths(bizReceiptLifetimeItemDTO.getProductDate(), bizReceiptLifetimeItemDTO.getShelfLifeMax()));
            //7.11 寿期检定创建与结果维护详情页行项目，在库存地点后增加包装方式与存放方式（取对应的工厂物料主数据）
            QueryWrapper<DicMaterialFactory> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DicMaterialFactory::getFtyId,bizReceiptLifetimeItemDTO.getFtyId()).eq(DicMaterialFactory::getMatId,bizReceiptLifetimeItemDTO.getMatId());
            DicMaterialFactory dicMaterialFactory = dicMaterialFactoryDataWrap.getOne(queryWrapper);
            if (UtilObject.isNotEmpty(dicMaterialFactory)) {
                bizReceiptLifetimeItemDTO.setPrice(dicMaterialFactory.getMoveAvgPrice());
                bizReceiptLifetimeItemDTO.setMoney(dicMaterialFactory.getMoveAvgPrice().multiply(bizReceiptLifetimeItemDTO.getStockQty()));
                bizReceiptLifetimeItemDTO.setPackageType(dicMaterialFactory.getPackageType());
                bizReceiptLifetimeItemDTO.setDepositType(dicMaterialFactory.getDepositType());
            }
        }
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置寿期维护单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 寿期单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptLifetimeHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_MAINTAIN.getValue().equals(receiptStatus)) {
            // 待维护 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        return buttonVO;
    }

    /**
     * 提交寿期维护单
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "要提交的寿期维护单"}
     * @out ctx 出参 {"receiptCode" : "寿期维护单单号"}
     */
    public void submitLifetimeAppraisal(BizContext ctx) {
        // 入参上下文
        BizReceiptLifetimeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);

        BizReceiptLifetimeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交寿期维护单时赋值提交时间提交人
        headDTO.setSubmitTime(UtilDate.getNow());
        headDTO.setSubmitUserId(user.getId());
        // 保存寿期单
        lifetimeCommonComponent.saveLifetime(ctx);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "不符合项处置单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 入参上下文
        BizReceiptLifetimeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Integer receiptType = po.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
        Long ftyId=po.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新寿期维护单- 审批中
        lifetimeCommonComponent.updateStatusApproving(ctx);
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取寿期维护单信息
        BizReceiptLifetimeHeadDTO headDTO = UtilBean.newInstance(bizReceiptLifetimeHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId()), BizReceiptLifetimeHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        // 封装上下文
        BizContext ctxInconformity = new BizContext();
        ctxInconformity.setCurrentUser(wfReceiptCo.getInitiator());
        ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 根据解决方案处理单据
            this.handleReceiptBySolveReason(ctxInconformity);
            // 更新寿期维护单已完成
            lifetimeCommonComponent.updateStatusCompleted(ctxInconformity);
        } else {
            // 单据状态已驳回
            lifetimeCommonComponent.updateStatusRejected(ctxInconformity);
        }
    }

    /**
     * 根据解决方案处理单据
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "要提交的寿期维护单"}
     */
    public void handleReceiptBySolveReason(BizContext ctx) {
        // 入参上下文
        BizReceiptLifetimeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 延期行项目-不处理
        List<BizReceiptLifetimeItemDTO> delayItemList = new ArrayList<>();
        // 降级行项目-不处理
        List<BizReceiptLifetimeItemDTO> demotionItemList = new ArrayList<>();
        // 报废行项目-生成报废冻结单并触发SAP过账
        List<BizReceiptLifetimeItemDTO> scrapItemList = new ArrayList<>();
        po.getItemList().forEach(p -> {
            if(p.getInspectResult().equals(EnumInspectResult.DELAY.getValue())) {
                delayItemList.add(p);
            }else if (p.getInspectResult().equals(EnumInspectResult.DEMOTION.getValue())) {
                demotionItemList.add(p);
            }else if (p.getInspectResult().equals(EnumInspectResult.SCRAP.getValue())) {
                scrapItemList.add(p);
            }
        });
        /*if(UtilCollection.isNotEmpty(scrapItemList)) {
            // 生成报废冻结单
            this.genScrapFreeze(po, scrapItemList, ctx.getCurrentUser());
        }*/
        updateBatchDate(delayItemList);
    }

    private void updateBatchDate(List<BizReceiptLifetimeItemDTO> delayItemList) {
        if (CollectionUtils.isEmpty(delayItemList)) {
            return;
        }
        int size = delayItemList.size();
        Map<Long, Date> itemDTOMap = new HashMap<>(size);
        Set<Long> batchIdSet = new HashSet<>(size);
        List<Long> batchIdList = new ArrayList<>(size);
        for (BizReceiptLifetimeItemDTO itemDTO : delayItemList) {
            Long batchId = itemDTO.getBatchId();
            if (batchIdSet.add(batchId)) {
                batchIdList.add(batchId);
                Date delayDate = itemDTO.getDelayDate();
                if (delayDate == null) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                }
                itemDTOMap.put(batchId, delayDate);
            }
        }
        List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(batchIdList);
        batchInfoDTOList.forEach(p -> {
            Long id = p.getId();
            Date delayDate = itemDTOMap.get(id);
            batchInfoService.updateLifetimeDate(id, delayDate);
        });
    }

    /**
     * 生成报废冻结单
     *
     * @in ctx 入参 {@link BizReceiptLifetimeHeadDTO : "寿期维护单"}
     * @in ctx 入参 {@link List<BizReceiptLifetimeItemDTO> : "报废行项目单"}
     * @in ctx 入参 {@link CurrentUser : "当前登录人信息"}
     */
    public void genScrapFreeze(BizReceiptLifetimeHeadDTO po, List<BizReceiptLifetimeItemDTO> scrapItemList, CurrentUser cUser) {
        // 装载报废冻结单item
        List<BizReceiptTransportItemDTO> transportItemDTOList = new ArrayList<>();
        // 装载报废冻结单item-Q库存
        List<BizReceiptTransportItemDTO> transportItemByQDTOList = new ArrayList<>();
        for (BizReceiptLifetimeItemDTO itemDTO : scrapItemList) {
            BizReceiptTransportItemDTO transportItemDTO = UtilBean.newInstance(itemDTO, BizReceiptTransportItemDTO.class);
            transportItemDTO.setPreReceiptItemId(itemDTO.getId());
            transportItemDTO.setOutputFtyId(itemDTO.getFtyId());
            transportItemDTO.setOutputMatId(itemDTO.getMatId());
            transportItemDTO.setOutputUnitId(itemDTO.getUnitId());
            transportItemDTO.setOutputLocationId(itemDTO.getLocationId());
            transportItemDTO.setOutputWhId(itemDTO.getWhId());
            transportItemDTO.setOutputSpecStockCode(itemDTO.getSpecStockCode());
            transportItemDTO.setOutputSpecStockName(itemDTO.getSpecStockName());
            transportItemDTO.setTransferableQty(itemDTO.getQty());
            transportItemDTO.setInputFtyId(itemDTO.getFtyId());
            transportItemDTO.setInputMatId(itemDTO.getMatId());
            transportItemDTO.setInputUnitId(itemDTO.getUnitId());
            transportItemDTO.setInputLocationId(itemDTO.getLocationId());
            transportItemDTO.setInputWhId(itemDTO.getWhId());
            transportItemDTO.setInputSpecStockCode(itemDTO.getSpecStockCode());
            transportItemDTO.setInputSpecStockName(itemDTO.getSpecStockName());
            transportItemDTO.setInputSupplierCode(itemDTO.getSupplierName());
            // 查询特性code和特性值库存
            BizReceiptAssembleRuleSearchPO searchPO = new BizReceiptAssembleRuleSearchPO();
            searchPO.setBatchCode(itemDTO.getBatchCode());
            searchPO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
            // 装载配货信息
            BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValueBySdw(null, null, EnumReceiptType.STOCK_FREEZE_SCRAP.getValue(), searchPO);
            assembleRuleDTO.getAssembleDTOList().forEach(p -> p.setQty(p.getStockQty()));
            transportItemDTO.setAssembleDTOList(assembleRuleDTO.getAssembleDTOList());
            // 装载标签信息
            this.setLabelList(assembleRuleDTO, itemDTO.getWhId());
            if(itemDTO.getSpecStock().equals(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue())) {
                transportItemByQDTOList.add(transportItemDTO);
            }else {
                transportItemDTOList.add(transportItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(transportItemDTOList)) {
            // 装载报废冻结单head
            BizReceiptTransportHeadDTO transportHeadDTO = new BizReceiptTransportHeadDTO();
            transportHeadDTO.setReceiptType(EnumReceiptType.STOCK_FREEZE_SCRAP.getValue());
            transportHeadDTO.setPreReceiptType(po.getReceiptType());
            transportHeadDTO.setPreReceiptHeadId(po.getId());
            transportHeadDTO.setMoveTypeId(dictionaryService.getMoveTypeIdCacheByCode("344", Const.STRING_EMPTY));
            transportHeadDTO.setItemDTOList(transportItemDTOList);
            transportHeadDTO.setRemark(po.getRemark());
            // 设置入参上下文
            BizContext ctxScrapFreeze = new BizContext();
            ctxScrapFreeze.setContextData(Const.BIZ_CONTEXT_KEY_PO, transportHeadDTO);
            ctxScrapFreeze.setCurrentUser(cUser);
            // 推送MQ生成报废冻结单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_SCRAP_FREEZE_STOCK, ctxScrapFreeze);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
        if(UtilCollection.isNotEmpty(transportItemByQDTOList)) {
            // 装载报废冻结单head
            BizReceiptTransportHeadDTO transportByQHeadDTO = new BizReceiptTransportHeadDTO();
            transportByQHeadDTO.setReceiptType(EnumReceiptType.STOCK_FREEZE_SCRAP.getValue());
            transportByQHeadDTO.setPreReceiptType(po.getReceiptType());
            transportByQHeadDTO.setPreReceiptHeadId(po.getId());
            transportByQHeadDTO.setMoveTypeId(dictionaryService.getMoveTypeIdCacheByCode("344", EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue()));
            transportByQHeadDTO.setItemDTOList(transportItemByQDTOList);
            transportByQHeadDTO.setRemark(po.getRemark());
            // 设置入参上下文
            BizContext ctxScrapFreeze = new BizContext();
            ctxScrapFreeze.setContextData(Const.BIZ_CONTEXT_KEY_PO, transportByQHeadDTO);
            ctxScrapFreeze.setCurrentUser(cUser);
            // 推送MQ生成报废冻结单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_SCRAP_FREEZE_STOCK, ctxScrapFreeze);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签信息
     *
     * @param assembleRuleDTO 配货信息
     * @param whId 仓库id
     */
    public void setLabelList(BizReceiptAssembleRuleDTO assembleRuleDTO, Long whId) {
        // 取表名,字段名
        String tableName = StockBin.class.getAnnotation(TableName.class).value();
        String tableFieldNameBinId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
        String tableFieldNameBatchId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
        String tableFieldNameTypeId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
        String tableFieldNameCellId =
                tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
        // 包含仓位批次时
        if (null != assembleRuleDTO.getFeatureCode()
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
            List<StockBinDTO> stockBinDTOList = new ArrayList<>();
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                StockBinDTO stockBinDTO = new StockBinDTO();
                // 工厂
                stockBinDTO.setFtyId(assembleDTO.getFtyId());
                // 库存地点
                stockBinDTO.setLocationId(assembleDTO.getLocationId());
                // 仓库
                stockBinDTO.setWhId(whId);
                // 物料
                stockBinDTO.setMatId(assembleDTO.getMatId());
                stockBinDTO.setCellId(0L);
                // 批次
                Long batchInfoId = null;
                List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                for (int i = 0; i < codeList.size(); i++) {
                    if (codeList.get(i).equals(tableFieldNameBatchId)) {
                        // 批次
                        batchInfoId = Long.parseLong(valueList.get(i));
                        stockBinDTO.setBatchId(batchInfoId);
                    } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                        // 存储类型
                        stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                    } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                        // 存储单元
                        stockBinDTO.setCellId(Long.parseLong(valueList.get(i)));
                    } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                        // 仓位
                        stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                    }
                }
                // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                        && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue())) ) {
                    stockBinDTOList.add(stockBinDTO);
                }
            }
            // 批量查询标签列表
            if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                List<BizLabelData> labelDataVOList = labelDataService.getList(stockBinDTOList);
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            typeId = Long.parseLong(valueList.get(i));
                        } else if (codeList.get(i).equals(tableFieldNameCellId)) {
                            // 存储单元
                            cellId = 0L;
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            binId = Long.parseLong(valueList.get(i));
                        }
                    }
                    List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                    for (BizLabelData labelData : labelDataVOList) {
                        if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                && labelData.getMatId().equals(assembleDTO.getMatId())
                                && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                && labelData.getCellId().equals(cellId) && labelData.getBinId().equals(binId)) {
                            // 唯一键相同时,匹配
                            BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                            labelReceiptRelDTO.setLabelId(labelData.getId());
                            labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                            labelReceiptRelDTO.setQty(labelData.getQty());
                            labelDataList.add(labelReceiptRelDTO);
                        }
                    }
                    assembleDTO.setLabelDataList(labelDataList);
                }
            }
        }
    }

}
