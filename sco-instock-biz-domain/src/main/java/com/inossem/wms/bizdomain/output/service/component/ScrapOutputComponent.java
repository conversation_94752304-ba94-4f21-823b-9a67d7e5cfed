package com.inossem.wms.bizdomain.output.service.component;

//import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleRuleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsScrapOutputMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsScrapOutputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputBinDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssembleRule;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报废出库组件类
 *
 * @Author: jhr
 * @Date: 2021/5/6 14:53
 */

@Service
@Slf4j
public class ScrapOutputComponent {

    @Autowired
    private OutputComponent outputComponent;
    @Autowired
    protected BatchInfoService batchInfoService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    protected BizReceiptAssembleRuleDataWrap bizReceiptAssembleRuleDataWrap;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizReceiptOutputBinDataWrap bizReceiptOutputBinDataWrap;

    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;

    @Autowired
    private BatchImgService bizBatchImgService;

    @Autowired
    private InsScrapOutputMoveTypeComponent insScrapOutputMoveTypeComponent;

    @Autowired
    private InsScrapOutputWriteOffMoveTypeComponent insScrapOutputWriteOffMoveTypeComponent;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    protected ErpPostingService erpPostingService;
    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    private HXSapIntegerfaceService hxInterfaceService;
    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<
            BizReceiptOutputHeadDTO> resultVO =
                new BizResultVO<>(
                    new BizReceiptOutputHeadDTO().setReceiptType(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
                    new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 报废出库详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptOutputHeadDTO headDTO = outputComponent.getItemListById(headId);
        //ass 填充批次信息
        outputComponent.setBatchInfo(headDTO.getItemDTOList());
        BizReceiptOutputItemDTO itemDTO = headDTO.getItemDTOList().get(0);
        headDTO.setMoveTypeId(itemDTO.getMoveTypeId());
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(itemDTO.getMoveTypeId());
        if(dicMoveType != null){
            headDTO.setMoveTypeCode(dicMoveType.getMoveTypeCode());
            headDTO.setMoveTypeName(dicMoveType.getMoveTypeName());
            headDTO.setMoveTypeSpecStock(dicMoveType.getSpecStock());
        }
        // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
        outputComponent.setMaterialFactoryInfo(headDTO.getItemDTOList());
        ButtonVO button = outputComponent.setButton(headId).setButtonDelete(false);

        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, headDTO.getReceiptType());
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);

        // 设置配货信息(特性库存)
        for(BizReceiptOutputItemDTO item : headDTO.getItemDTOList()){
            item.setQty(item.getPreReceiptQty());

            List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
            BizReceiptAssembleDTO assembleDTO = new BizReceiptAssembleDTO();
            assembleDTO.setFtyId(item.getFtyId());
            assembleDTO.setLocationId(item.getLocationId());
            assembleDTO.setWhId(item.getWhId());
            assembleDTO.setMatId(item.getMatId());
            assembleDTO.setUnitId(item.getUnitId());
            assembleDTO.setBatchId(item.getBatchId());
            assembleDTO.setQty(item.getPreReceiptQty());
            assembleDTO.setSpecCode(specFeature.getFeatureCode());
            assembleDTO.setSpecValue(item.getBatchId().toString());
            assembleDTO.setSpecDisplayValue(specFeature.getFeatureDisplayCode());
            assembleDTOList.add(assembleDTO);
            item.setAssembleDTOList(assembleDTOList);
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }


    /**
     * 提交校验
     *
     * @param ctx 上下文
     */
    public void checkSubmit(BizContext ctx) {
        // 参数基本校验
        outputComponent.check(ctx);
        // 出库数量是否为0校验
        outputComponent.checkItemQtyIsZero(ctx);
        // 校验单据状态
        outputComponent.checkReceiptStatus(ctx);
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(EnumReceiptType.STOCK_OUTPUT_SALE.getValue());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.SALE_RECEIPT.getValue());
            bizCommonReceiptRelation.setPreReceiptHeadId(item.getPreReceiptHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 获取过账移动类型并校验
     *
     * @param ctx 上下文
     */
    public void generateInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = new StockInsMoveTypePostTaskDTO();
        StockInsMoveTypeDTO postingInsMoveTypeDTO;
        try {
            postingInsMoveTypeDTO = insScrapOutputMoveTypeComponent.generatePostingInsDoc(headDTO);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeDTO);
        // 过账凭证(处理批次库存、临时区仓位库存)
        stockInsMoveTypePostTaskDTO.setPostDTO(postingInsMoveTypeDTO);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        stockInsMoveTypePostTaskDTO.setTaskDTO(null);
        // 校验
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    /**
     * 获取过账移动类型并校验
     *
     * @param ctx 上下文
     */
    public void generateWriteOffInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        headDTO.getItemDTOList().forEach(x -> x.setIsWriteOff(1));
        StockInsMoveTypeDTO postingInsMoveTypeVo;
        StockInsMoveTypeDTO taskInsMoveTypeVo = null;
        try {
            postingInsMoveTypeVo = insScrapOutputMoveTypeComponent.generateWriteOffPostingInsDoc(headDTO);
            List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
            boolean isGenerateTaskInsDoc = false;
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if (UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    isGenerateTaskInsDoc = true;
                    break;
                }
            }
            // 非同时模式判断是否生成task凭证
            if (isGenerateTaskInsDoc) {
                taskInsMoveTypeVo = insScrapOutputMoveTypeComponent.generateTaskInsDoc(headDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeVo);
        bizCommonService.setInsDocCode(taskInsMoveTypeVo);
        StockInsMoveTypePostTaskDTO dto = new StockInsMoveTypePostTaskDTO();
        // 过账凭证(处理批次库存、临时区仓位库存)
        dto.setPostDTO(postingInsMoveTypeVo);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        dto.setTaskDTO(taskInsMoveTypeVo);
        outputComponent.checkAndComputeForModifyStock(dto);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, dto);
    }


    /**
     * 非先过账模式-获取冲销移动类型并校验
     *
     * @param ctx 上下文
     *
     */
    public void generateWriteOffInsMoveTypeAndCheckNonPostFirst(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = this.setWriteOffMoveTypeNonPostFirst(headDTO);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    private StockInsMoveTypePostTaskDTO setWriteOffMoveTypeNonPostFirst(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO postingInsMoveTypeVo;
        StockInsMoveTypeDTO taskInsMoveTypeVo = null;
        try {
            postingInsMoveTypeVo = insScrapOutputWriteOffMoveTypeComponent.generatePostingInsDocNonPostFirst(headDTO);
            List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
            boolean isGenerateTaskInsDoc = false;
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if(UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    isGenerateTaskInsDoc = true;
                    break;
                }
            }
            // 非同时模式判断是否生成task凭证
            if(isGenerateTaskInsDoc) {
                taskInsMoveTypeVo = insScrapOutputWriteOffMoveTypeComponent.generateTaskInsDoc(headDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        bizCommonService.setInsDocCode(postingInsMoveTypeVo);
        bizCommonService.setInsDocCode(taskInsMoveTypeVo);
        StockInsMoveTypePostTaskDTO dto = new StockInsMoveTypePostTaskDTO();
        dto.setPostDTO(postingInsMoveTypeVo);
        dto.setTaskDTO(taskInsMoveTypeVo);
        return dto;
    }

    /**
     * 校验删除
     *
     * @param ctx 上下文
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 判断出库单是否已同步SAP
        if (outputComponent.getSapPostSyncStatus(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
    }

    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue(), headId);
    }

    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
        Long matId = null;
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
                return;
            }
        }
        String specStock = Const.STRING_EMPTY;
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        DicMoveType dicMoveType = dictionaryService.getMoveCacheById(po.getMoveTypeId());
        BizReceiptAssembleRuleSearchPO rulePo = UtilBean.deepCopyNewInstance(po, BizReceiptAssembleRuleSearchPO.class);
        if(Objects.isNull(dicMoveType)){
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MatStockDTO());
            return;
        }
        rulePo.setSpecStock(dicMoveType.getSpecStock());
        rulePo.setStockStatus(stockStatus);
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw(po.getReceiptType(),rulePo);
        if (UtilCollection.isNotEmpty(assembleRuleDTO.getAssembleDTOList())) {
            // 特性库存转行项目
            for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(assembleDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setReceiptType(po.getReceiptType());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
            this.setBatchImg(itemDTOList);
        }
        MatStockDTO matStockDTO = UtilBean.newInstance(assembleRuleDTO, MatStockDTO.class);
        matStockDTO.setItemDTOList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, matStockDTO);
    }

    /**
     * sap过账
     *
     * @param ctx 上下文
     */
    public void  postToSap(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 过滤没有sap凭证的行项目
        List<BizReceiptOutputItemDTO> syncList = headDTO.getItemDTOList().stream()
                .filter(item -> !StringUtils.hasText(item.getMatDocCode())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(syncList)) {
            return;
        }
        headDTO.setItemDTOList(syncList);
        // 过滤后重新设置上下文-headDTO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        outputComponent.setInPostDate(syncList);

        HXPostingHeader header = this.scrapOutputPosting(headDTO, syncList, false);

        HXPostingReturn returnObj = hxInterfaceService.posting(header,false);

        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 更新过账时间和过账标识
            outputComponent.updatePostingDateAndIsPost(ctx);
            // 修改出库单SAP物料凭证字段
            // 调用SAP
            if (UtilConst.getInstance().isErpSyncMode()) {
                this.updateItemAfterSyncSap(syncList, returnObj);
            }else {
                this.updateItemAfterNoSyncSap(syncList, returnObj);
            }
            // 更新单据行项目状态已记账
            outputComponent.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            List<Long> itemIdList = syncList.stream().map(BizReceiptOutputItemDTO::getId).collect(Collectors.toList());
            log.debug("出库单{}行项目{}过账同步SAP成功", headDTO.getReceiptCode(), itemIdList.toString());
        } else {
            // 失败时，更新出库单及行项目为【未同步】状态
            log.warn("出库单{}过账同步SAP失败", headDTO.getReceiptCode());
            outputComponent.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    /**
     * 领料退库单过账或者冲销入参
     */
    private HXPostingHeader scrapOutputPosting(BizReceiptOutputHeadDTO headDTO, List<BizReceiptOutputItemDTO> itemList, boolean isWriteOff) {
        HXPostingHeader header = new HXPostingHeader();
        header.setReceiptId(headDTO.getId());
        header.setReceiptCode(headDTO.getReceiptCode());
        header.setReceiptType(headDTO.getReceiptType());
        if (isWriteOff) {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffDocDate()));
        } else {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getDocDate()));
        }

        List<HXPostingItem> items = itemList.stream().map(outItem -> {
            HXPostingItem item = new HXPostingItem();
            item.setReceiptRid(outItem.getRid());
            item.setFtyCode(outItem.getFtyCode());
            item.setLocationCode1(outItem.getLocationCode());
            item.setMatCode(outItem.getMatCode());
            item.setQty(UtilBigDecimal.getString(outItem.getQty()));
            item.setUnitCode(outItem.getUnitCode());

            if (isWriteOff) {
                item.setMoveType(EnumMoveType.TYPE_SCRAP_OUTPUT_OFF.getValue());
                item.setMatDocCode(outItem.getMatDocCode());
                item.setMatDocYear(outItem.getMatDocYear());
                item.setReceiptRid(String.valueOf(Integer.parseInt(outItem.getRid()) / 10));
            } else {
                item.setMoveType(EnumMoveType.TYPE_SCRAP_OUTPUT.getValue());
            }

            item.setCostCenter(headDTO.getCostCenterCode());
            return item;
        }).collect(Collectors.toList());

        header.setItems(items);

        return header;
    }

    /**
     * 过账成功，修改出库单SAP物料凭证字段,年,凭证行号,凭证时间
     *
     * @param itemDTOList 行项目列表
     * @param retObj      sap返回结果
     */
    public void updateItemAfterSyncSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn retObj) {
        List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(retObj.getMatDocCode());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptOutputBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 过账成功，修改出库单SAP物料凭证字段,年,凭证行号,凭证时间（不过调AP）
     *
     * @param itemDTOList 行项目列表
     * @param retObj      sap返回结果
     */
    public void updateItemAfterNoSyncSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn retObj) {
        int matDocRid = 1;
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(retObj.getMatDocCode());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setMatDocRid(matDocRidStr);
            itemDTO.setDocDate(new Date());
            matDocRid++;
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemDTOList 单据行项目
     * @param user 当前用户
     */
    private void setInPostDate(List<BizReceiptOutputItemDTO> itemDTOList, CurrentUser user) {
        if (UtilCollection.isEmpty(itemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemDTOList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (BizReceiptOutputItemDTO inspectItemDTO : itemDTOList) {
            if (EnumRealYn.FALSE.getIntValue().equals(inspectItemDTO.getIsWriteOff())) {
                inspectItemDTO.setDocDate(UtilDate.getNow());
                inspectItemDTO.setPostingDate(postingDate);
            } else {
                inspectItemDTO.setWriteOffDocDate(UtilDate.getNow());
                inspectItemDTO.setWriteOffPostingDate(postingDate);
            }
        }
    }

    /**
     * 设置批次图片信息
     *
     * @param itemDTOList 行项目
     */
    public void setBatchImg(List<BizReceiptOutputItemDTO> itemDTOList) {
        Set<Long> matIdSet = itemDTOList.stream().map(BizReceiptOutputItemDTO::getMatId).collect(Collectors.toSet());
        if (UtilCollection.isNotEmpty(matIdSet)) {
            Map<Long, List<BizBatchImgDTO>> batchImgMap = bizBatchImgService.getBatchImgListByMatIdList(matIdSet, 4);
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                List<BizBatchImgDTO> bizBatchImgDTOS = batchImgMap.get(itemDTO.getMatId());
                itemDTO.setBatchImgList(bizBatchImgDTOS);
            }
        }
    }

    /**
     * 获取配货信息(特性库存)【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getItemInfoByFeatureStock(BizContext ctx) {
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询特性库存
        BizReceiptAssembleRuleDTO assembleRuleDTO = new BizReceiptAssembleRuleDTO();
        String specStock = Const.STRING_EMPTY;
        // 根据单据类型获取特性
        List<StockBinDTO> stockBinDTOS = null;
        if (UtilCollection.isNotEmpty(po.getAssembleDTOList())) {
            stockBinDTOS = stockCommonService.fillSpecCodeAndValue(po.getAssembleDTOList());
        }
        // 根据单据类型获取特性
        BizReceiptAssembleRuleSearchPO rulePo = UtilBean.deepCopyNewInstance(po, BizReceiptAssembleRuleSearchPO.class);
        rulePo.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        assembleRuleDTO = stockCommonService.getStockByFeatureCodeBySdw( po.getReceiptType(), rulePo);
        // 若其他行项目无配货则直接返回
        List<BizReceiptOutputItemDTO> itemDTOList = po.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, assembleRuleDTO);
            return;
        }
        List<BizReceiptAssembleDTO> stockQtyList = assembleRuleDTO.getAssembleDTOList();
        // 已配货的物料idList
        List<Long> matIdList = itemDTOList.stream().map(BizReceiptOutputItemDTO::getMatId).collect(Collectors.toList());
        // 同一个物料重新计算数量
        if (matIdList.contains(po.getMatId())) {
            // 保存特性值与此特性值已操作数量
            Map<String, BigDecimal> totalMap = new HashMap<>();
            // 物料工厂库存地点 粗略分组,筛选出物料相同可能特性值不同的list
            Map<String, List<BizReceiptOutputItemDTO>> matMap =
                    itemDTOList.stream().collect(Collectors.groupingBy(a -> getItemInfoGroupKey(po)));
            List<BizReceiptOutputItemDTO> bizReceiptOutputItemDTOS = matMap.get(this.getItemInfoGroupKey(po));
            for (BizReceiptOutputItemDTO bizReceiptOutputItemDTO : bizReceiptOutputItemDTOS) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : bizReceiptOutputItemDTO.getAssembleDTOList()) {
                    BigDecimal qty = bizReceiptAssembleDTO.getQty();
                    if (totalMap.containsKey(bizReceiptAssembleDTO.getSpecValue())) {
                        qty = qty.add(totalMap.get(bizReceiptAssembleDTO.getSpecValue()));
                    }
                    totalMap.put(bizReceiptAssembleDTO.getSpecValue(), qty);
                }
            }
            // 重新赋值库存数量
            for (BizReceiptAssembleDTO bizReceiptAssembleDTO : stockQtyList) {
                // 已操作数量
                BigDecimal qty = totalMap.get(bizReceiptAssembleDTO.getSpecValue());
                if (Objects.nonNull(qty)) {
                    BigDecimal stockQty = bizReceiptAssembleDTO.getStockQty().subtract(qty);
                    stockQty = stockQty.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : stockQty;
                    bizReceiptAssembleDTO.setStockQty(stockQty);
                }
            }
        }
        assembleRuleDTO.setAssembleDTOList(stockQtyList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, assembleRuleDTO);
    }

    /**
     * 获取配货分组条件
     *
     * @param po 查询库存入参
     * @return 分组key
     */
    private String getItemInfoGroupKey(BizReceiptOutputSearchPO po) {
        return String.valueOf(po.getMatId()) + po.getFtyId() + po.getLocationId();
    }

    /**
     * 【先过账模式】设置批次id-用于生成接收方批次
     */
    public void setAssembleInputBatchId(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<Long> batchIdList = new ArrayList<>();
        Map<String, BizBatchInfoDTO> batchMap = new HashMap<>();
        // 批量查询批次信息
        for (BizReceiptOutputItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                Long batchId = outputComponent.getBatchId(assembleDTO);
                assembleDTO.setBatchId(batchId);
                batchIdList.add(batchId);
            }
        }
        // 根据批次id批量获取批次信息
        List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(batchIdList);
        // 拼装map
        Map<Long, BizBatchInfoDTO> batchInfoDTOMap = new HashMap<>();
        for (BizBatchInfoDTO batchInfoDTO : batchInfoDTOList) {
            batchInfoDTOMap.put(batchInfoDTO.getId(), batchInfoDTO);
        }
        // 拼装map
        if(UtilCollection.isEmpty(batchInfoDTOList)) {return;}
        for (BizReceiptOutputItemDTO itemDto : headDTO.getItemDTOList()) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                if(batchInfoDTOMap.containsKey(assembleDTO.getBatchId())){
                    assembleDTO.setBatchCode(batchInfoDTOMap.get(assembleDTO.getBatchId()).getBatchCode());
                }
            }
        }
    }


    /**
     * sap冲销
     *
     * @param ctx 上下文
     */
    public void writeOffToSap(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        // 设置冲销标识。
        itemDTOList.forEach(x -> x.setIsWriteOff(1));
        /* ******** 设置出库单账期 ******** */
        outputComponent.setInPostDate(itemDTOList);

        HXPostingHeader header = this.scrapOutputPosting(headDTO, itemDTOList, true);

        HXPostingReturn returnObj = hxInterfaceService.writeOff(header,true);

        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 更新冲销过账时间和冲销标识
            outputComponent.updateWriteOffPostingDateAndIsWriteOff(ctx);
            // 冲销成功，修改出库单SAP物料凭证字段
            // 调用SAP
            if (UtilConst.getInstance().isErpSyncMode()) {
                this.updateItemAfterWriteOffSap(itemDTOList, returnObj);
            }else {
                this.updateItemAfterWriteOffNoSap(itemDTOList, returnObj);
            }
        } else {
            log.warn("出库单{}冲销同步SAP失败，返回信息：{}", headDTO.getId(), returnObj.getReturnMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    /**
     * 单据状态状态变更-已冲销
     */
    public void updateStatusWriteOff(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (headDTO == null) {
            return;
        }
        List<Long> itemIdList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDto : headDTO.getItemDTOList()) {
            itemIdList.add(itemDto.getId());
        }
        // 行项目状态
        QueryWrapper<BizReceiptOutputItem> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .ne(BizReceiptOutputItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())
                .eq(BizReceiptOutputItem::getHeadId, headDTO.getId());

        List<BizReceiptOutputItem> itemList = bizReceiptOutputItemDataWrap.list(wrapper);

        if(org.springframework.util.CollectionUtils.isEmpty(itemList)){
            headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            bizReceiptOutputHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 冲销成功，修改出库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj      sap返回结果
     */
    public void updateItemAfterWriteOffSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn retObj) {
        List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(retObj.getMatDocCode());
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptOutputBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 冲销成功，修改出库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj      sap返回结果
     */
    public void updateItemAfterWriteOffNoSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn retObj) {
        int matDocRid = 1;
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(retObj.getMatDocCode());
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setWriteOffMatDocRid(matDocRidStr);
            itemDTO.setWriteOffDocDate(new Date());
            matDocRid++;
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
    }
}
