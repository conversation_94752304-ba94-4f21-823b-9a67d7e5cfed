package com.inossem.wms.bizdomain.purchase.controller;

import com.inossem.wms.bizdomain.purchase.service.biz.ContractChangeService;
import com.inossem.wms.common.annotation.In;
import com.inossem.wms.common.annotation.Out;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.purchase.dto.BizReceiptPurchaseApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplySearchPO;
import com.inossem.wms.common.model.bizdomain.purchase.po.BizReceiptPurchaseApplyUpdatePO;
import com.inossem.wms.common.model.bizdomain.purchase.vo.BizReceiptPurchaseApplyListVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.apply.BudgetClassMapVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@Api(tags = "合同变更")
public class ContractChangeController {


    @Autowired
    private ContractChangeService contractChangeService;

    /**
     * 合同变更-获取预算分类预算科目列表
     */
    @ApiOperation(value = "获取预算分类预算科目列表", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/budgetList")
    public BaseResult<MultiResultVO<BudgetClassMapVO>> getEnumBudgetList(BizContext ctx) {
        contractChangeService.getEnumBudgetList(ctx);
        MultiResultVO<BudgetClassMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 合同变更分页查询
     */
    @ApiOperation(value = "合同变更分页查询", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/results")
    @In(parameter = "BizReceiptPurchaseApplySearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyListVO>")
    public BaseResult<PageObjectVO<BizReceiptPurchaseApplyListVO>> getPurchaseApplyPageVo(
            @RequestBody BizReceiptPurchaseApplySearchPO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        contractChangeService.getPurchaseApplyPageVo(ctx);
        PageObjectVO<BizReceiptPurchaseApplyListVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 合同变更初始化
     */
    @ApiOperation(value = "合同变更初始化", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/init")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> init(BizContext ctx) {
        contractChangeService.init(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 合同变更详情
     */
    @ApiOperation(value = "合同变更详情", tags = {"合同变更管理"})
    @GetMapping("/contract-changes/{id}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> getPurchaseApplyDetail(
            @PathVariable("id") Long id, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        contractChangeService.getPurchaseApplyDetail(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 合同变更详情
     */
    @ApiOperation(value = "合同变更详情", tags = {"合同变更管理"})
    @GetMapping("/contract-changes/{id}/{taskId}")
    @In(parameter = "id", required = "id")
    @Out(parameter = "BizResultVO<BizReceiptPurchaseApplyHeadDTO>")
    public BaseResult<BizResultVO<BizReceiptPurchaseApplyHeadDTO>> getPurchaseApplyDetail(
            @PathVariable("id") Long id, @PathVariable("taskId") String taskId, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        contractChangeService.getPurchaseApplyDetail(ctx);
        BizResultVO<BizReceiptPurchaseApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 合同变更保存
     */
    @ApiOperation(value = "合同变更保存", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/save")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"receiptType", "applyUserId", "applyDeptId", "planArrivalDate"})
    @Out(parameter = "String")
    public BaseResult<String> save(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        contractChangeService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 合同变更提交
     */
    @ApiOperation(value = "合同变更提交", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/submit")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> submit(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        contractChangeService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 合同变更过账srm
     */
    @ApiOperation(value = "合同变更过账", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/post")
    @In(parameter = "BizReceiptPurchaseApplyHeadDTO", required = {"id", "receiptCode"})
    @Out(parameter = "String")
    public BaseResult<String> post(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        contractChangeService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS, code);
    }

    /**
     * 合同变更删除
     */
    @ApiOperation(value = "合同变更删除", tags = {"合同变更管理"})
    @DeleteMapping("/contract-changes/{id}")
    @In(parameter = "id", required = "id")
    public BaseResult<String> delete(@PathVariable("id") Long id, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, id);
        contractChangeService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 获取需求计划行项目列表
     */
    @ApiOperation(value = "获取需求计划行项目列表", tags = {"合同变更管理"})
    @PostMapping("/contract-changes/demand-plan-items")
    @In(parameter = "BizReceiptDemandPlanSearchPO", required = {"pageIndex", "pageSize"})
    @Out(parameter = "PageObjectVO<BizReceiptPurchaseApplyItemDTO>")
    public BaseResult<PageObjectVO<BizReceiptPurchaseApplyItemDTO>> getDemandPlanItems(
            @RequestBody BizReceiptDemandPlanSearchPO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        contractChangeService.getDemandPlanItems(ctx);
        PageObjectVO<BizReceiptPurchaseApplyItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 取消合同变更行项目
     */
    @ApiOperation(value = "取消合同变更行项目")
    @PostMapping("/contract-changes/cancelItems")
    public BaseResult<String> cancelItems(@RequestBody BizReceiptPurchaseApplyUpdatePO po, BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
        contractChangeService.cancel(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }


    /**
     * 文件上传功能
     *
     * @param fileInClient 文件
     * @return 上传后文件信息
     */
    @ApiOperation(value = "文件上传", tags = {"附件管理"})
    @PostMapping(path = "/contract-changes/file/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizCommonFile> upload(@RequestPart("file") MultipartFile fileInClient, CurrentUser user) {
        return BaseResult.success(contractChangeService.upload(fileInClient, user));
    }

    @ApiOperation(value = "撤销")
    @PostMapping(value = "/contract-changes/revoke")
    public BaseResult<?> revoke(@RequestBody BizReceiptPurchaseApplyHeadDTO po, BizContext ctx) {
        contractChangeService.revoke(ctx);
        return BaseResult.success();
    }



}
