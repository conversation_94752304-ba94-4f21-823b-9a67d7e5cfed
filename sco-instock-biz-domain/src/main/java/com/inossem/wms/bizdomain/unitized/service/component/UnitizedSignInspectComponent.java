package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.todo.service.datawrap.TemplateTaskDataWrap;
import com.inossem.wms.bizdomain.inspect.service.component.SignInspectComponent;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.inconformity.EnumDifferentType;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.todo.entity.TemplateTask;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectUserDTO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectHeadVO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFacotryWbs;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilGzip;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilPrint;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备质检会签 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-26
 */
@Slf4j
@Service
public class UnitizedSignInspectComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected UnitizedInspectCommonComponent unitizedInspectCommonComponent;

    @Autowired
    protected DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private ErpPurchaseReceiptItemDataWrap purchaseReceiptItemDataWrap;

    @Autowired
    protected SignInspectComponent signInspectComponent;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected TemplateTaskDataWrap templateTaskDataWrap;

    @Autowired
    protected UnitizedReportComponent unitizedReportComponent;

    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;

    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @out ctx 出参 {@link MultiResultVO <> ("dicCarTypeDataWrap.list()":"库存地点下拉框")}
     */
    public void getLocationList(BizContext ctx) {
        // 从上下文获取工厂id
        Long ftyId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 查询库存地点下拉
        QueryWrapper<DicStockLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicStockLocation::getFtyId, ftyId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicStockLocationDataWrap.list(queryWrapper)));
    }

    /**
     * 查询质检会签单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptInspectSearchPO :"质检会签单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInspectSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptInspectHeadVO> page = po.getPageObj(BizReceiptInspectHeadVO.class);
        CurrentUser user = ctx.getCurrentUser();
        bizReceiptInspectHeadDataWrap.getPageVOListUnitized(page, po,user);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 质检会签单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"质检会签单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取质检会签单详情
        BizReceiptInspectHeadDTO headDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(headId), BizReceiptInspectHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 属性填充
        SysUser purchaseUser = dictionaryService.getSysUserCacheByuserCode(headDTO.getItemList().get(0).getPurchaseUserCode());
        String purchaseUserName = headDTO.getItemList().get(0).getPurchaseUserCode();
        if (!(purchaseUser == null || purchaseUser.getUserName() == null || purchaseUser.getUserName() == "")){
            purchaseUserName = purchaseUser.getUserName();
        }

        headDTO.setPurchaseUserCode(headDTO.getItemList().get(0).getPurchaseUserCode())
                .setPurchaseUserName(purchaseUserName)
                .setReferReceiptCode(headDTO.getItemList().get(0).getReferReceiptCode())
                .setMatDocCode(headDTO.getItemList().get(0).getMatDocCode())
                .setContractCode(headDTO.getItemList().get(0).getContractCode());
        headDTO.setReceiptApplyCode(signInspectComponent.getReceiptApplyCode(headId, headDTO.getReceiptType()));
        headDTO.setSupplierName(headDTO.getItemList().get(0).getSupplierName());

        // 打印属性 退库单位
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setUserId(headDTO.getCreateUserId());
        List<DicDeptDTO> userDeptOffice = dicDeptDataWrap.getUserDeptOfficeTree(po);
        if (UtilCollection.isNotEmpty(userDeptOffice)) {
            headDTO.setApplyUserDept(userDeptOffice.get(0).getDeptName());
        }

        // 配置打印相关属性字段 实到数量
        headDTO.getItemList().forEach(itemDTO -> {
            // 合格数量+不合格数量-未到货数量） = 实到数量
            itemDTO.setActualQty(itemDTO.getQty().add(itemDTO.getUnqualifiedQty()).subtract(itemDTO.getUnarrivalQty()));
            if (itemDTO.getUnqualifiedQty().compareTo(BigDecimal.ZERO)!=0||itemDTO.getUnarrivalQty().compareTo(BigDecimal.ZERO)!=0){
                itemDTO.setIsDiff(1);
                itemDTO.setIsDiffI18n("是");
            }else {
                itemDTO.setIsDiff(0);
                itemDTO.setIsDiffI18n("否");
            }

            // 填充申请单号（退库&退转库打印时需要）
            if (UtilObject.isNotEmpty(headDTO.getReceiptApplyCode())) {
                itemDTO.setReceiptApplyCode(headDTO.getReceiptApplyCode());
            }

            // 从项目工厂物料主数据(dic_material_factory_wbs)中得到移动平均价，设置到行项目中，以便打印时使用
            ErpPurchaseReceiptItem purchaseReceiptItem = purchaseReceiptItemDataWrap.getById(itemDTO.getReferReceiptItemId());
            itemDTO.setMoveAvgPrice(BigDecimal.ZERO);
            itemDTO.setPrice(BigDecimal.ZERO);
            if (UtilObject.isNotEmpty(purchaseReceiptItem)) {
                itemDTO.setPrice(purchaseReceiptItem.getPrice());
                String materialFactoryWbsKey = itemDTO.getMatId() + "##" + itemDTO.getFtyId() + "##" + purchaseReceiptItem.getSpecStock() + "##" + purchaseReceiptItem.getSpecStockCode();
                DicMaterialFacotryWbs materialFacotryWbsByKey = dictionaryService.getMaterialFacotryWbsByKey(materialFactoryWbsKey);
                if (UtilObject.isNotNull(materialFacotryWbsByKey)) {
                    itemDTO.setMoveAvgPrice(materialFacotryWbsByKey.getMoveAvgPrice());
                } else {
                    log.warn("质检会签-单据{}，行项目{}，物料{}-{}，在设置移动平均价时，未能在工厂{}下获取到有效数据", headDTO.getReceiptCode(), itemDTO.getRid(), itemDTO.getMatId(), itemDTO.getMatName(), itemDTO.getFtyCode());
                }
            }

            // 填充抬头 公司名称信息 打印时需要 取行项目工厂对应的公司信息
            if (UtilString.isNullOrEmpty(headDTO.getCorpName())) {
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(itemDTO.getFtyId());
                if (factoryDTO == null) {
                    // 如果缓存没有获取到，默认一个公司名称（这种情况为异常数据，行项目上缺少了工厂有效的工厂id）
                    log.warn("质检会签单{} 行项目缺少有效的工厂数据，请检查", headDTO.getReceiptCode());
                    headDTO.setCorpName(Const.FACTORY_J046_DEFAULT_CORP_NAME);
                } else {
                    headDTO.setCorpName(factoryDTO.getCorpName());
                }
            }

            // 从工厂物料主数据(dic_material_factory)缓存数据中读取包装方式，覆盖行项目中的包装方式数据
            DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(), itemDTO.getFtyId());
            if (materialFactoryDTO != null) {
                itemDTO.setPackageType(materialFactoryDTO.getPackageType());
            } else {
                log.warn("质检会签单{} 行项目缺少有效的工厂物料数据，请检查，行项目rid={}, 物料id={}, 工厂Id={}", headDTO.getReceiptCode(), itemDTO.getRid(), itemDTO.getMatId(), itemDTO.getFtyId());
            }
        });

        // 质检会签填充固定资产物料描述属性
        List<Long> referReceiptItemIdList = headDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).map(BizReceiptInspectItemDTO::getReferReceiptItemId).collect(Collectors.toList());

        if (referReceiptItemIdList.size() > 0){
            QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, referReceiptItemIdList);
            List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);

            Map<Long, ErpPurchaseReceiptItem> erpPurchaseMap = erpPurchaseReceiptItemList.stream()
                    .collect(Collectors.toMap(ErpPurchaseReceiptItem::getId, obj -> obj));

            headDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(itemDTO -> {
                ErpPurchaseReceiptItem erpPurchaseList = erpPurchaseMap.get(itemDTO.getReferReceiptItemId());
                if (null != erpPurchaseList && "1".equals(erpPurchaseList.getSubjectType())){
                    itemDTO.setMatName(erpPurchaseList.getMatNameBack());
                }
            });
        }
        if (EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN.getValue().equals(headDTO.getReceiptType())) {
            fillReturnSign(headDTO); //填充退库质检会签参检人签名信息
        } else {
            fillSign(headDTO); //填充签名信息
        }
        // 计算总价
        if( headDTO.getSignWaybillDTOList()!=null){
            headDTO.getSignWaybillDTOList().forEach(p -> p.setTotalPrice((p.getArrivalQty().multiply(p.getPrice())).add(p.getRemainder())));
        }
        if (EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN.getValue().equals(headDTO.getReceiptType())) {
            // 暂不支持退库数量维护成小数（因为考虑单价可能会存在小数，若数量存在小数，则可能会因为丢失精度导致sap记录的主设备库存不准确，后续可能会放开）
            headDTO.getItemList().forEach(item -> item.setDecimalPlace(BigDecimal.ZERO.intValue()));
        }
        headDTO.setWareContractDept(Const.wareContractDept); //仓储承包商
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置质检会签单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }


    public void fillSign(BizReceiptInspectHeadDTO headDTO){
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        headDTO.setSign5(UtilPrint.SIGNATURE);
        headDTO.setSign6(UtilPrint.SIGNATURE);
        headDTO.setSign7(UtilPrint.SIGNATURE);
        List<BizReceiptInspectUserDTO> constUserDTOList = JSON.parseArray(Const.unitizedInspectUserJson,BizReceiptInspectUserDTO.class);
        List<BizReceiptInspectUserDTO> userDTOList = headDTO.getInspectUserList();
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                if(i>6){
                    continue;
                }
                BizReceiptInspectUserDTO userDTO = userDTOList.get(i);
                BizReceiptInspectUserDTO constUserDTO = constUserDTOList.get(i);
                String constInspectCompany=constUserDTO.getInspectCompany();
                BizReceiptInspectUserDTO inspectUserDTO = UtilBean.newInstance(userDTO, BizReceiptInspectUserDTO.class);
                inspectUserDTO.setInspectCompany(constInspectCompany);
                constUserDTOList.set(i,inspectUserDTO);
            }
        }
        for (int i = 0; i < constUserDTOList.size(); i++) {
            BizReceiptInspectUserDTO userDTO = constUserDTOList.get(i);
            String inspectUserName = userDTO.getInspectUserName();
            String inspectCompany = userDTO.getInspectCompany();
            Date signDate = userDTO.getCreateTime();
            if (StringUtils.isNoneBlank(inspectUserName) && inspectUserName.startsWith("data:image/svg")) {
                if (i == 0) {
                    headDTO.setSign1(inspectUserName);
                    headDTO.setSignCompany1(inspectCompany);
                    headDTO.setSignDate1(signDate);
                    continue;
                }
                if (i == 1) {
                    headDTO.setSign2(inspectUserName);
                    headDTO.setSignCompany2(inspectCompany);
                    headDTO.setSignDate2(signDate);
                    continue;
                }
                if (i == 2) {
                    headDTO.setSign3(inspectUserName);
                    headDTO.setSignCompany3(inspectCompany);
                    headDTO.setSignDate3(signDate);
                    continue;
                }
                if (i == 3) {
                    headDTO.setSign4(inspectUserName);
                    headDTO.setSignCompany4(inspectCompany);
                    headDTO.setSignDate4(signDate);
                    continue;
                }
                if (i == 4) {
                    headDTO.setSign5(inspectUserName);
                    headDTO.setSignCompany5(inspectCompany);
                    headDTO.setSignDate5(signDate);
                    continue;
                }
                if (i == 5) {
                    headDTO.setSign6(inspectUserName);
                    headDTO.setSignCompany6(inspectCompany);
                    headDTO.setSignDate6(signDate);
                    continue;
                }
                if (i == 6) {
                    headDTO.setSign7(inspectUserName);
                    headDTO.setSignCompany7(inspectCompany);
                    headDTO.setSignDate7(signDate);
                }
            }
        }
        headDTO.setInspectUserList(constUserDTOList);

    }

    public void fillReturnSign(BizReceiptInspectHeadDTO headDTO){
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        headDTO.setSign5(UtilPrint.SIGNATURE);
        headDTO.setSign6(UtilPrint.SIGNATURE);
        headDTO.setSign7(UtilPrint.SIGNATURE);
        headDTO.setSign8(UtilPrint.SIGNATURE);
        headDTO.setSign9(UtilPrint.SIGNATURE);
        headDTO.setSign10(UtilPrint.SIGNATURE);
        List<BizReceiptInspectUserDTO> userDTOList = headDTO.getInspectUserList();
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                BizReceiptInspectUserDTO userDTO = userDTOList.get(i);
                String inspectUserName = userDTO.getInspectUserName();
                String inspectCompany = userDTO.getInspectCompany();
                if (StringUtils.isNoneBlank(inspectUserName) && inspectUserName.startsWith("data:image/svg")) {
                    if (i == 0) {
                        headDTO.setSign1(inspectUserName);
                        headDTO.setSignCompany1(inspectCompany);
                        continue;
                    }
                    if (i == 1) {
                        headDTO.setSign2(inspectUserName);
                        headDTO.setSignCompany2(inspectCompany);
                        continue;
                    }
                    if (i == 2) {
                        headDTO.setSign3(inspectUserName);
                        headDTO.setSignCompany3(inspectCompany);
                        continue;
                    }
                    if (i == 3) {
                        headDTO.setSign4(inspectUserName);
                        headDTO.setSignCompany4(inspectCompany);
                        continue;
                    }
                    if (i == 4) {
                        headDTO.setSign5(inspectUserName);
                        headDTO.setSignCompany5(inspectCompany);
                        continue;
                    }
                    if (i == 5) {
                        headDTO.setSign6(inspectUserName);
                        headDTO.setSignCompany6(inspectCompany);
                        continue;
                    }
                    if (i == 6) {
                        headDTO.setSign7(inspectUserName);
                        headDTO.setSignCompany7(inspectCompany);
                        continue;
                    }
                    if (i == 7) {
                        headDTO.setSign8(inspectUserName);
                        headDTO.setSignCompany8(inspectCompany);
                        continue;
                    }
                    if (i == 8) {
                        headDTO.setSign9(inspectUserName);
                        headDTO.setSignCompany9(inspectCompany);
                        continue;
                    }
                    if (i == 9) {
                        headDTO.setSign10(inspectUserName);
                        headDTO.setSignCompany10(inspectCompany);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 按钮组
     *
     * @param headDTO 登记单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInspectHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
                // 压水堆-退转库-质检会签, 无打印
                && !EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN_TRANSFER.getValue().equals(headDTO.getReceiptType())) {
            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 质检会签单保存效验
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的质检会签单"}
     */
    public void checkQty(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 判断 待质检数量 = 合格数量+不合格数量+未到货数量
        List<String> errorRidList = new ArrayList<>();
        List<String> errorBidList = new ArrayList<>();
        headDTO.getSignWaybillDTOList().forEach(p -> {
            if(p.getQualifiedQty().add(p.getUnqualifiedQty()).add(p.getUnarrivalQty()).compareTo(BigDecimal.ZERO)!=0){
                if(p.getArrivalQty().compareTo(p.getQualifiedQty().add(p.getUnqualifiedQty()).add(p.getUnarrivalQty())) != 0) {
                    errorRidList.add(p.getSignInspectItemRid());
                    errorBidList.add(p.getBid());
                }
            }
        });
        if(UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ARRIVAL_QTR_UN_ENUOUGH, errorRidList.toString(), errorBidList.toString());
        }
    }

    /**
     * 提交质检会签单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的质检会签单"}
     * @out ctx 出参 {"receiptCode" : "分配质检单单号"}
     */
    public void submitLoseRegister(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存质检会签单
        unitizedInspectCommonComponent.saveInspect(ctx);
    }

    /**
     * 同步DTS
     */
    public void syncDTS(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        if(po!=null){
            String fileName=po.getReceiptCode()+".pdf";
            List<BizCommonReceiptAttachment> list= receiptAttachmentService.getBizReceiptAttachmentListByFileName(po.getId(),po.getReceiptType(),fileName);
            if(UtilCollection.isEmpty(list)) { //之前没有附件 存入模版附件定时任务表
                fillSign(po); //填充签名信息
                po.setWareContractDept(Const.wareContractDept); //仓储承包商
                TemplateTask templateTask=new TemplateTask();
                templateTask.setType(1);
                templateTask.setHeadId(po.getId());
                templateTask.setReceiptCode(po.getReceiptCode());
                templateTask.setReceiptType(po.getReceiptType());
                templateTask.setTemplateReceiptType(po.getReceiptType());
                String param = UtilGzip.compressStringToString(JSONArray.toJSONStringWithDateFormat(po, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat));
                templateTask.setPara(param);
                templateTask.setDealFlag(0);
                templateTask.setCreateUserId(user.getId());
                templateTask.setModifyUserId(user.getId());
                templateTask.setCreateTime(UtilDate.getNow());
                templateTask.setModifyTime(UtilDate.getNow());
                templateTaskDataWrap.save(templateTask);
            }else{ //存在附件 同步dts
                unitizedReportComponent.syncDTSSignInspect(ctx);
            }
        }
    }


    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取质检会签单信息
        BizReceiptInspectHeadDTO headDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId()), BizReceiptInspectHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 封装上下文
            BizContext ctxInspect = new BizContext();
            ctxInspect.setCurrentUser(wfReceiptCo.getInitiator());
            ctxInspect.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            // 更新单据已完成
            unitizedInspectCommonComponent.updateStatusCompleted(ctxInspect);
            // 生成验收入库单
            this.genInspectInputNew(ctxInspect);
        } else {
            // 单据状态已驳回
            unitizedInspectCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 生成验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    public void genInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        // 查询采购订单行项目
        QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, po.getItemList().stream().map(p -> p.getReferReceiptItemId()).collect(Collectors.toList()));
        List<ErpPurchaseReceiptItem> purchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);
        // 装载验收入库单head
        BizReceiptInputHeadDTO inspectInputHead = new BizReceiptInputHeadDTO();
        // 装载验收入库单item
        List<BizReceiptInputItemDTO> inspectInputItemList = new ArrayList<>(po.getItemList().size());
        /* ******** 入库单head设置 ******** */
        inspectInputHead.setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())
                .setWaybillDTOList(po.getSignWaybillDTOList().stream().filter(p -> p.getQualifiedQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()))
                .setUnit(po.getUnit());
        // 运单按行项目号分组
        Map<String, List<BizReceiptWaybillDTO>> waybillMap = po.getSignWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getSignInspectItemRid));
        /* ******** 入库单item设置 ******** */
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            BizReceiptInputItemDTO inspectInputItem = new BizReceiptInputItemDTO();
            inspectInputItem = UtilBean.newInstance(itemDTO, inspectInputItem.getClass());
            inspectInputItem.setId(null);
            inspectInputItem.setHeadId(null);
            inspectInputItem.setQty(inspectInputItem.getQty().subtract((waybillMap.get(inspectInputItem.getRid()).stream().map(BizReceiptWaybillDTO::getUnqualifiedQty).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .add(waybillMap.get(inspectInputItem.getRid()).stream().map(BizReceiptWaybillDTO::getUnarrivalQty).reduce(BigDecimal.ZERO, BigDecimal::add)))
                    .multiply(waybillMap.get(inspectInputItem.getRid()).get(0).getPrice())));
            inspectInputItem.setPreReceiptHeadId(po.getId());
            inspectInputItem.setPreReceiptItemId(itemDTO.getId());
            inspectInputItem.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
            inspectInputItem.setPreMatDocCode(itemDTO.getMatDocCode());
            inspectInputItem.setPreMatDocRid(itemDTO.getMatDocRid());
            inspectInputItem.setPreMatDocYear(itemDTO.getMatDocYear());
            inspectInputItem.setMatDocCode(null);
            inspectInputItem.setMatDocRid(null);
            inspectInputItem.setMatDocYear(null);
            inspectInputItem.setPostingDate(null);
            inspectInputItem.setDocDate(null);
            inspectInputItem.setIsPost(null);
            /* ******** 设置批次信息 ******** */
            for (ErpPurchaseReceiptItem purchaseReceiptItem : purchaseReceiptItemList) {
                if(itemDTO.getReferReceiptItemId().equals(purchaseReceiptItem.getId())) {
                    for(BizReceiptWaybillDTO waybillDTO : inspectInputHead.getWaybillDTOList()) {
                        if(itemDTO.getId().equals(waybillDTO.getSignInspectItemId())) {
                            BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                            batchInfoDTO = UtilBean.newInstance(purchaseReceiptItem, batchInfoDTO.getClass());
                            batchInfoDTO.setId(null);
                            batchInfoDTO.setPurchaseReceiptHeadId(inspectInputItem.getReferReceiptHeadId());
                            batchInfoDTO.setPurchaseReceiptItemId(inspectInputItem.getReferReceiptItemId());
                            batchInfoDTO.setPurchaseReceiptRid(inspectInputItem.getReferReceiptRid());
                            batchInfoDTO.setPurchaseReceiptCode(inspectInputItem.getReferReceiptCode());
                            batchInfoDTO.setProductionDate(waybillDTO.getProductDate());
                            batchInfoDTO.setIsSafe(po.getIsSafe());
                            batchInfoDTO.setIsMainParts(waybillDTO.getIsMainParts());
                            batchInfoDTO.setFunctionalLocationCode(waybillDTO.getFunctionalLocationCode());
                            batchInfoDTO.setPrice(waybillDTO.getPrice());
                            batchInfoDTO.setRemainder(waybillDTO.getRemainder());
                            batchInfoDTO.setUnitizedFtyId(itemDTO.getFtyId());
                            batchInfoDTO.setUnitizedLocationId(itemDTO.getLocationId());
//                            batchInfoDTO.setExtend1(waybillDTO.getExtend1());
                            batchInfoDTO.setExtend2(waybillDTO.getExtend2());
//                            batchInfoDTO.setExtend3(waybillDTO.getExtend3());
//                            batchInfoDTO.setExtend4(waybillDTO.getExtend4());
//                            batchInfoDTO.setExtend5(waybillDTO.getExtend5());
//                            batchInfoDTO.setExtend6(waybillDTO.getExtend6());
//                            batchInfoDTO.setExtend7(waybillDTO.getExtend7());
//                            batchInfoDTO.setExtend8(waybillDTO.getExtend8());
//                            batchInfoDTO.setExtend9(waybillDTO.getExtend9());
//                            batchInfoDTO.setExtend10(waybillDTO.getExtend10());
//                            batchInfoDTO.setExtend11(waybillDTO.getExtend11());
//                            batchInfoDTO.setExtend12(waybillDTO.getExtend12());
//                            batchInfoDTO.setExtend13(waybillDTO.getExtend13());
//                            batchInfoDTO.setExtend14(waybillDTO.getExtend14());
//                            batchInfoDTO.setExtend15(waybillDTO.getExtend15());
//                            batchInfoDTO.setExtend16(waybillDTO.getExtend16());
//                            batchInfoDTO.setExtend17(waybillDTO.getExtend17());
//                            batchInfoDTO.setExtend18(waybillDTO.getExtend18());
//                            batchInfoDTO.setExtend19(waybillDTO.getExtend19());
                            batchInfoDTO.setExtend20(waybillDTO.getExtend20());
//                            batchInfoDTO.setExtend21(waybillDTO.getExtend21());
//                            batchInfoDTO.setExtend22(waybillDTO.getExtend22());
//                            batchInfoDTO.setExtend23(waybillDTO.getExtend23());
                            batchInfoDTO.setExtend24(waybillDTO.getExtend24());
                            batchInfoDTO.setExtend25(waybillDTO.getExtend25());
                            batchInfoDTO.setExtend26(waybillDTO.getExtend26());
                            batchInfoDTO.setExtend27(waybillDTO.getExtend27());
                            batchInfoDTO.setExtend28(waybillDTO.getExtend28());
                            batchInfoDTO.setExtend29(waybillDTO.getExtend29());
//                            batchInfoDTO.setExtend30(waybillDTO.getExtend30());
                            batchInfoDTO.setExtend31(waybillDTO.getExtend31());
//                            batchInfoDTO.setExtend32(waybillDTO.getExtend32());
//                            batchInfoDTO.setExtend33(waybillDTO.getExtend33());
                            batchInfoDTO.setExtend34(waybillDTO.getExtend34());
                            batchInfoDTO.setExtend35(waybillDTO.getExtend35());
                            batchInfoDTO.setExtend36(waybillDTO.getExtend36());
                            batchInfoDTO.setExtend37(waybillDTO.getExtend37());
                            batchInfoDTO.setExtend38(waybillDTO.getExtend38());
//                            batchInfoDTO.setExtend39(waybillDTO.getExtend39());
                            batchInfoDTO.setExtend40(waybillDTO.getExtend40());
                            batchInfoDTO.setExtend41(waybillDTO.getExtend41());
                            batchInfoDTO.setExtend42(waybillDTO.getExtend42());
                            batchInfoDTO.setExtend43(waybillDTO.getExtend43());
                            batchInfoDTO.setExtend44(waybillDTO.getExtend44());
//                            batchInfoDTO.setExtend45(waybillDTO.getExtend45());
                            batchInfoDTO.setExtend46(waybillDTO.getExtend46());
                            batchInfoDTO.setExtend47(waybillDTO.getExtend47());
                            batchInfoDTO.setExtend48(waybillDTO.getExtend48());
                            batchInfoDTO.setExtend49(waybillDTO.getExtend49());
                            batchInfoDTO.setExtend50(waybillDTO.getExtend50());
//                            batchInfoDTO.setExtend51(waybillDTO.getExtend51());
//                            batchInfoDTO.setExtend52(waybillDTO.getExtend52());
//                            batchInfoDTO.setExtend53(waybillDTO.getExtend53());
//                            batchInfoDTO.setExtend54(waybillDTO.getExtend54());
//                            batchInfoDTO.setExtend55(waybillDTO.getExtend55());
//                            batchInfoDTO.setExtend56(waybillDTO.getExtend56());
//                            batchInfoDTO.setExtend57(waybillDTO.getExtend57());
//                            batchInfoDTO.setExtend58(waybillDTO.getExtend58());
//                            batchInfoDTO.setExtend59(waybillDTO.getExtend59());
                            batchInfoDTO.setExtend60(waybillDTO.getExtend60());
                            batchInfoDTO.setExtend61(waybillDTO.getExtend61());
                            batchInfoDTO.setExtend62(waybillDTO.getExtend62());
                            batchInfoDTO.setExtend63(waybillDTO.getExtend63());
                            batchInfoDTO.setExtend64(waybillDTO.getExtend64());
                            batchInfoDTO.setExtend65(waybillDTO.getExtend65());
                            batchInfoDTO.setExtend66(waybillDTO.getExtend66());
                            batchInfoDTO.setExtend67(waybillDTO.getExtend67());
                            batchInfoDTO.setExtend68(waybillDTO.getExtend68());
                            batchInfoDTO.setExtend69(waybillDTO.getExtend69());
                            batchInfoDTO.setExtend70(waybillDTO.getExtend70());
                            batchInfoDTO.setExtend71(waybillDTO.getExtend71());
                            batchInfoDTO.setExtend72(waybillDTO.getExtend72());
                            batchInfoDTO.setExtend73(waybillDTO.getExtend73());
                            batchInfoDTO.setExtend74(waybillDTO.getExtend74());
                            batchInfoDTO.setCaseCode(waybillDTO.getCaseCode());
                            batchInfoDTO.setCaseWeight(waybillDTO.getCaseWeight());
                            batchInfoDTO.setPackageForm(waybillDTO.getPackageForm());
                            batchInfoDTO.setArrivalQty(waybillDTO.getArrivalQty());
                            waybillDTO.setBizBatchInfoDTO(batchInfoDTO);
                        }
                    }
                }
            }
            inspectInputItemList.add(inspectInputItem);
        }
        if(UtilCollection.isNotEmpty(inspectInputItemList)) {
            inspectInputHead.setItemList(inspectInputItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, inspectInputHead);
            ctxInput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成验收入库单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_INSPECT_INPUT_STOCK, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成不符合项处置单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    public void genInconformityMaintain(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        // 装载质量差异不符合项处置单head
        BizReceiptInconformityHeadDTO qualityDiffInconformityMaintainHead = new BizReceiptInconformityHeadDTO();
        // 装载质量差异不符合项处置单item
        List<BizReceiptInconformityItemDTO> qualityDiffInconformityMaintainItemList = new ArrayList<>(po.getItemList().size());
        // 装载数量差异不符合项处置单head
        BizReceiptInconformityHeadDTO numberDiffInconformityMaintainHead = new BizReceiptInconformityHeadDTO();
        // 装载数量差异不符合项处置单item
        List<BizReceiptInconformityItemDTO> numberDiffInconformityMaintainItemList = new ArrayList<>(po.getItemList().size());
        /* ******** 质量差异不符合项处置单head设置 ******** */
        qualityDiffInconformityMaintainHead.setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue()).setDifferentType(EnumDifferentType.QUALITY_DIFF.getValue())
                .setDepositPoint(po.getDepositPoint())
                .setQuailtyWaybillDTOList(po.getSignWaybillDTOList().stream().filter(p -> p.getUnqualifiedQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()));
        qualityDiffInconformityMaintainHead.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        /* ******** 数量差异不符合项处置单head设置 ******** */
        numberDiffInconformityMaintainHead.setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue()).setDifferentType(EnumDifferentType.NUMBER_DIFF.getValue())
                .setDepositPoint(po.getDepositPoint())
                .setNumberWaybillDTOList(po.getSignWaybillDTOList().stream().filter(p -> p.getUnarrivalQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList()));
        numberDiffInconformityMaintainHead.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        // 运单按行项目号分组
        Map<String, List<BizReceiptWaybillDTO>> waybillMap = po.getSignWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getSignInspectItemRid));
        /* ******** 不符合项处置单item设置 ******** */
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            // 有不合格数量生成不符合项处置单
            BigDecimal unqualifiedQty = waybillMap.get(itemDTO.getRid()).stream().map(BizReceiptWaybillDTO::getUnqualifiedQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(unqualifiedQty.compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = new BizReceiptInconformityItemDTO();
                inconformityItemDTO = UtilBean.newInstance(itemDTO, inconformityItemDTO.getClass());
                inconformityItemDTO.setQty(waybillMap.get(itemDTO.getRid()).stream().collect(Collectors.toList())
                        .stream().map(BizReceiptWaybillDTO::getUnqualifiedQty).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(waybillMap.get(itemDTO.getRid()).get(0).getPrice()));
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
                qualityDiffInconformityMaintainItemList.add(inconformityItemDTO);
            }
            // 有未到货数量生成不符合项处置单
            BigDecimal unarrivalQty = waybillMap.get(itemDTO.getRid()).stream().map(BizReceiptWaybillDTO::getUnarrivalQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(unarrivalQty.compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = new BizReceiptInconformityItemDTO();
                inconformityItemDTO = UtilBean.newInstance(itemDTO, inconformityItemDTO.getClass());
                inconformityItemDTO.setQty(waybillMap.get(itemDTO.getRid()).stream().collect(Collectors.toList())
                        .stream().map(BizReceiptWaybillDTO::getUnarrivalQty).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(waybillMap.get(itemDTO.getRid()).get(0).getPrice()));
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
                numberDiffInconformityMaintainItemList.add(inconformityItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(qualityDiffInconformityMaintainItemList)) {
            qualityDiffInconformityMaintainHead.setItemList(qualityDiffInconformityMaintainItemList);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, qualityDiffInconformityMaintainHead);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项处置单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_QUALITY_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
        if(UtilCollection.isNotEmpty(numberDiffInconformityMaintainItemList)) {
            numberDiffInconformityMaintainHead.setItemList(numberDiffInconformityMaintainItemList);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, numberDiffInconformityMaintainHead);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项处置单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_NUMBER_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成不符合项处置单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    public void genInconformityMaintainNew(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        // 装载质量差异不符合项处置单head
        BizReceiptInconformityHeadDTO qualityDiffInconformityMaintainHead = new BizReceiptInconformityHeadDTO();
        // 装载质量差异不符合项处置单item
        List<BizReceiptInconformityItemDTO> qualityDiffInconformityMaintainItemList = new ArrayList<>(po.getItemList().size());
        // 装载数量差异不符合项处置单head
        BizReceiptInconformityHeadDTO numberDiffInconformityMaintainHead = new BizReceiptInconformityHeadDTO();
        // 装载数量差异不符合项处置单item
        List<BizReceiptInconformityItemDTO> numberDiffInconformityMaintainItemList = new ArrayList<>(po.getItemList().size());
        List<BizReceiptWaybillDTO> waybillDTOList = po.getSignWaybillDTOList();
        int size = waybillDTOList.size();
        List<BizReceiptWaybillDTO> waybillListUnqualified = new ArrayList<>(size);
        List<BizReceiptWaybillDTO> waybillListUnarrival = new ArrayList<>(size);
        Set<String> usedRidSet = new HashSet<>();
        for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
            BigDecimal unqualifiedQty = waybillDTO.getUnqualifiedQty();
            BigDecimal unarrivalQty = waybillDTO.getUnarrivalQty();
            BigDecimal remainder = waybillDTO.getRemainder();
            String rid = waybillDTO.getInspectInputItemRid();
            if (unqualifiedQty.compareTo(BigDecimal.ZERO) > 0) {
                if (remainder.compareTo(BigDecimal.ZERO) > 0 && !usedRidSet.add(rid)) {
                    waybillDTO.setRemainder(BigDecimal.ZERO);
                }
                waybillListUnqualified.add(waybillDTO);
            }
            if (unarrivalQty.compareTo(BigDecimal.ZERO) > 0) {
                if (remainder.compareTo(BigDecimal.ZERO) > 0 && !usedRidSet.add(rid)) {
                    waybillDTO.setRemainder(BigDecimal.ZERO);
                }
                waybillListUnarrival.add(waybillDTO);
            }
        }
        /* ******** 质量差异不符合项处置单head设置 ******** */
        qualityDiffInconformityMaintainHead.setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue()).setDifferentType(EnumDifferentType.QUALITY_DIFF.getValue())
                .setDepositPoint(po.getDepositPoint())
                .setQuailtyWaybillDTOList(waybillListUnqualified);
        qualityDiffInconformityMaintainHead.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        /* ******** 数量差异不符合项处置单head设置 ******** */
        numberDiffInconformityMaintainHead.setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue()).setDifferentType(EnumDifferentType.NUMBER_DIFF.getValue())
                .setDepositPoint(po.getDepositPoint())
                .setNumberWaybillDTOList(waybillListUnarrival);
        numberDiffInconformityMaintainHead.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        // 运单按行项目号分组
        Map<String, List<BizReceiptWaybillDTO>> waybillMap = po.getSignWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getSignInspectItemRid));
        /* ******** 不符合项处置单item设置 ******** */
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            // 有不合格数量生成不符合项处置单
            BigDecimal unqualifiedQty = waybillMap.get(itemDTO.getRid()).stream().map(BizReceiptWaybillDTO::getUnqualifiedQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(unqualifiedQty.compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = new BizReceiptInconformityItemDTO();
                inconformityItemDTO = UtilBean.newInstance(itemDTO, inconformityItemDTO.getClass());
                inconformityItemDTO.setQty(waybillMap.get(itemDTO.getRid()).stream().collect(Collectors.toList())
                        .stream().map(BizReceiptWaybillDTO::getUnqualifiedQty).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(waybillMap.get(itemDTO.getRid()).get(0).getPrice()));
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
                qualityDiffInconformityMaintainItemList.add(inconformityItemDTO);
            }
            // 有未到货数量生成不符合项处置单
            BigDecimal unarrivalQty = waybillMap.get(itemDTO.getRid()).stream().map(BizReceiptWaybillDTO::getUnarrivalQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(unarrivalQty.compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = new BizReceiptInconformityItemDTO();
                inconformityItemDTO = UtilBean.newInstance(itemDTO, inconformityItemDTO.getClass());
                inconformityItemDTO.setQty(waybillMap.get(itemDTO.getRid()).stream().collect(Collectors.toList())
                        .stream().map(BizReceiptWaybillDTO::getUnarrivalQty).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(waybillMap.get(itemDTO.getRid()).get(0).getPrice()));
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
                numberDiffInconformityMaintainItemList.add(inconformityItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(qualityDiffInconformityMaintainItemList)) {
            qualityDiffInconformityMaintainHead.setItemList(qualityDiffInconformityMaintainItemList);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, qualityDiffInconformityMaintainHead);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项处置单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_QUALITY_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
        if(UtilCollection.isNotEmpty(numberDiffInconformityMaintainItemList)) {
            numberDiffInconformityMaintainHead.setItemList(numberDiffInconformityMaintainItemList);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, numberDiffInconformityMaintainHead);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项处置单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_NUMBER_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成成套设备数量差异通知
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    public void genInconformityNumberNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        // 装载数量差异不符合项处置单head
        BizReceiptInconformityHeadDTO numberDiffInconformityMaintainHead = new BizReceiptInconformityHeadDTO();
        // 装载数量差异不符合项处置单item
        List<BizReceiptInconformityItemDTO> numberDiffInconformityMaintainItemList = new ArrayList<>(po.getItemList().size());
        List<BizReceiptWaybillDTO> waybillDTOList = po.getSignWaybillDTOList();
        int size = waybillDTOList.size();
        List<BizReceiptWaybillDTO> waybillListUnarrival = new ArrayList<>(size);
        for (BizReceiptWaybillDTO waybillDTO : waybillDTOList) {
            if(po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue())){
                // 多供数量判断
                BigDecimal moreQty = waybillDTO.getMoreQty();
                if (moreQty.compareTo(BigDecimal.ZERO) > 0) {
                    waybillListUnarrival.add(waybillDTO);
                }

            } else {
                BigDecimal unarrivalQty = waybillDTO.getUnarrivalQty();
                if (unarrivalQty.compareTo(BigDecimal.ZERO) > 0) {
                    waybillListUnarrival.add(waybillDTO);
                }
            }
        }
        if (UtilCollection.isEmpty(waybillListUnarrival)) {
            return;
        }
        /* ******** 数量差异不符合项处置单head设置 ******** */
        numberDiffInconformityMaintainHead.setReceiptType(EnumReceiptType.UNITIZED_INCONFORMITY_NUMBER_NOTICE.getValue()).setDifferentType(po.getDifferentType())
                .setDepositPoint(po.getDepositPoint())
                .setNumberNoticeWaybillDTOList(waybillListUnarrival);
        numberDiffInconformityMaintainHead.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        // 运单按行项目号分组
        Map<Long, List<BizReceiptWaybillDTO>> waybillMap = po.getSignWaybillDTOList().stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getSignInspectItemId));

        /* ******** 不符合项处置单item设置 ******** */
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            Long preReceiptItemId=itemDTO.getId();
            if(waybillMap.get(preReceiptItemId) ==null){
                continue;
            }
            // 有未到货数量生成不符合项处置单
            BigDecimal unarrivalQty = waybillMap.get(preReceiptItemId).stream().map(BizReceiptWaybillDTO::getUnarrivalQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal moreQty = waybillMap.get(preReceiptItemId).stream().map(BizReceiptWaybillDTO::getMoreQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(unarrivalQty.compareTo(BigDecimal.ZERO) > 0 || moreQty.compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = new BizReceiptInconformityItemDTO();
                inconformityItemDTO = UtilBean.newInstance(itemDTO, inconformityItemDTO.getClass());
                BigDecimal qty = new BigDecimal(0);

                List<BizReceiptWaybillDTO> waybillDTOListMain = waybillMap.get(preReceiptItemId);
                if (!CollectionUtils.isEmpty(waybillDTOListMain)) {
                    if(po.getDifferentType().equals(EnumDifferentType.NUMBER_DIFF_MORE.getValue())){
                        // 多供
                        for (BizReceiptWaybillDTO waybill : waybillDTOListMain) {
                            qty=qty.add(waybill.getMoreQty().multiply(waybill.getPrice()));
                        }

                    } else {
                        for (BizReceiptWaybillDTO waybill : waybillDTOListMain) {
                            qty=qty.add(waybill.getUnarrivalQty().multiply(waybill.getPrice()));
                        }
                    }
                }
                inconformityItemDTO.setQty(qty);
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
                numberDiffInconformityMaintainItemList.add(inconformityItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(numberDiffInconformityMaintainItemList)) {
            numberDiffInconformityMaintainHead.setItemList(numberDiffInconformityMaintainItemList);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, numberDiffInconformityMaintainHead);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成不符合项通知单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_NUMBER_INCONFORMITY_NOTICE_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    public void genInspectInputNew(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        // 查询采购订单行项目
        QueryWrapper<ErpPurchaseReceiptItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ErpPurchaseReceiptItem::getId, po.getItemList().stream().map(p -> p.getReferReceiptItemId()).collect(Collectors.toList()));
        List<ErpPurchaseReceiptItem> purchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(queryWrapper);
        // 装载验收入库单head
        BizReceiptInputHeadDTO inspectInputHead = new BizReceiptInputHeadDTO();
        // 装载验收入库单item
        List<BizReceiptInputItemDTO> inspectInputItemList = new ArrayList<>(po.getItemList().size());
        /* ******** 入库单head设置 ******** */
        List<BizReceiptWaybillDTO> signWaybillDTOList = po.getSignWaybillDTOList();
        //合格数量
        List<BizReceiptWaybillDTO> qualifiedDTOList = signWaybillDTOList.stream().filter(p -> p.getQualifiedQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(qualifiedDTOList)) {
            return;
        }
        inspectInputHead.setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue()); //成套设备验收入库
        inspectInputHead.setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe());
        inspectInputHead.setUnit(po.getUnit());
        // 运单按行项目号分组
        List<Long> idList = new ArrayList<>(qualifiedDTOList.size());
        Map<Long, List<BizReceiptWaybillDTO>> waybillMapMain = qualifiedDTOList.stream().collect(Collectors.groupingBy(BizReceiptWaybillDTO::getSignInspectItemId));
        /* ******** 入库单item设置 ******** */
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            BizReceiptInputItemDTO inspectInputItem = new BizReceiptInputItemDTO();
            inspectInputItem = UtilBean.newInstance(itemDTO, inspectInputItem.getClass());
            inspectInputItem.setId(null);
            inspectInputItem.setHeadId(null);
            if (CollectionUtils.isEmpty(waybillMapMain)) {
                continue;
            }
            Long preReceiptItemId = itemDTO.getId();
            BigDecimal qty = new BigDecimal(0);
            List<BizReceiptWaybillDTO> waybillDTOListMain = waybillMapMain.get(preReceiptItemId);
            if (!CollectionUtils.isEmpty(waybillDTOListMain)) {
                for (BizReceiptWaybillDTO waybill : waybillDTOListMain) {
                    qty=qty.add(waybill.getQualifiedQty().multiply(waybill.getPrice()));
                }
            }
            inspectInputItem.setQty(qty);
            inspectInputItem.setPreReceiptHeadId(po.getId());
            inspectInputItem.setPreReceiptItemId(preReceiptItemId);
            inspectInputItem.setPreReceiptType(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue());
            inspectInputItem.setPreMatDocCode(itemDTO.getMatDocCode());
            inspectInputItem.setPreMatDocRid(itemDTO.getMatDocRid());
            inspectInputItem.setPreMatDocYear(itemDTO.getMatDocYear());
            inspectInputItem.setMatDocCode(null);
            inspectInputItem.setMatDocRid(null);
            inspectInputItem.setMatDocYear(null);
            inspectInputItem.setPostingDate(null);
            inspectInputItem.setDocDate(null);
            inspectInputItem.setIsPost(null);
            /* ******** 设置批次信息 ******** */
            for (ErpPurchaseReceiptItem purchaseReceiptItem : purchaseReceiptItemList) {
                if(itemDTO.getReferReceiptItemId().equals(purchaseReceiptItem.getId())) {
                    for(BizReceiptWaybillDTO waybillDTO : qualifiedDTOList) {
                        if(itemDTO.getId().equals(waybillDTO.getSignInspectItemId())) {
                            BigDecimal remainder = waybillDTO.getRemainder();
                            if (remainder.compareTo(BigDecimal.ZERO) > 0) {
                                idList.add(waybillDTO.getId());
                            }
                            BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                            batchInfoDTO = UtilBean.newInstance(purchaseReceiptItem, batchInfoDTO.getClass());
                            batchInfoDTO.setId(null);
                            batchInfoDTO.setPurchaseReceiptHeadId(inspectInputItem.getReferReceiptHeadId());
                            batchInfoDTO.setPurchaseReceiptItemId(inspectInputItem.getReferReceiptItemId());
                            batchInfoDTO.setPurchaseReceiptRid(inspectInputItem.getReferReceiptRid());
                            batchInfoDTO.setPurchaseReceiptCode(inspectInputItem.getReferReceiptCode());
                            batchInfoDTO.setProductionDate(waybillDTO.getProductDate());
                            batchInfoDTO.setIsSafe(po.getIsSafe());
                            batchInfoDTO.setIsMainParts(waybillDTO.getIsMainParts());
                            batchInfoDTO.setFunctionalLocationCode(waybillDTO.getFunctionalLocationCode());
                            batchInfoDTO.setPrice(waybillDTO.getPrice());
                            batchInfoDTO.setRemainder(remainder);
                            batchInfoDTO.setUnitizedFtyId(itemDTO.getFtyId());
                            batchInfoDTO.setUnitizedLocationId(itemDTO.getLocationId());
//                            batchInfoDTO.setExtend1(waybillDTO.getExtend1());
                            batchInfoDTO.setExtend2(waybillDTO.getExtend2());
//                            batchInfoDTO.setExtend3(waybillDTO.getExtend3());
//                            batchInfoDTO.setExtend4(waybillDTO.getExtend4());
//                            batchInfoDTO.setExtend5(waybillDTO.getExtend5());
//                            batchInfoDTO.setExtend6(waybillDTO.getExtend6());
//                            batchInfoDTO.setExtend7(waybillDTO.getExtend7());
//                            batchInfoDTO.setExtend8(waybillDTO.getExtend8());
//                            batchInfoDTO.setExtend9(waybillDTO.getExtend9());
//                            batchInfoDTO.setExtend10(waybillDTO.getExtend10());
//                            batchInfoDTO.setExtend11(waybillDTO.getExtend11());
//                            batchInfoDTO.setExtend12(waybillDTO.getExtend12());
//                            batchInfoDTO.setExtend13(waybillDTO.getExtend13());
//                            batchInfoDTO.setExtend14(waybillDTO.getExtend14());
//                            batchInfoDTO.setExtend15(waybillDTO.getExtend15());
//                            batchInfoDTO.setExtend16(waybillDTO.getExtend16());
//                            batchInfoDTO.setExtend17(waybillDTO.getExtend17());
//                            batchInfoDTO.setExtend18(waybillDTO.getExtend18());
//                            batchInfoDTO.setExtend19(waybillDTO.getExtend19());
                            batchInfoDTO.setExtend20(waybillDTO.getExtend20());
//                            batchInfoDTO.setExtend21(waybillDTO.getExtend21());
//                            batchInfoDTO.setExtend22(waybillDTO.getExtend22());
//                            batchInfoDTO.setExtend23(waybillDTO.getExtend23());
                            batchInfoDTO.setExtend24(waybillDTO.getExtend24());
                            batchInfoDTO.setExtend25(waybillDTO.getExtend25());
                            batchInfoDTO.setExtend26(waybillDTO.getExtend26());
                            batchInfoDTO.setExtend27(waybillDTO.getExtend27());
                            batchInfoDTO.setExtend28(waybillDTO.getExtend28());
                            batchInfoDTO.setExtend29(waybillDTO.getExtend29());
//                            batchInfoDTO.setExtend30(waybillDTO.getExtend30());
                            batchInfoDTO.setExtend31(waybillDTO.getExtend31());
//                            batchInfoDTO.setExtend32(waybillDTO.getExtend32());
//                            batchInfoDTO.setExtend33(waybillDTO.getExtend33());
                            batchInfoDTO.setExtend34(waybillDTO.getExtend34());
                            batchInfoDTO.setExtend35(waybillDTO.getExtend35());
                            batchInfoDTO.setExtend36(waybillDTO.getExtend36());
                            batchInfoDTO.setExtend37(waybillDTO.getExtend37());
                            batchInfoDTO.setExtend38(waybillDTO.getExtend38());
//                            batchInfoDTO.setExtend39(waybillDTO.getExtend39());
                            batchInfoDTO.setExtend40(waybillDTO.getExtend40());
                            batchInfoDTO.setExtend41(waybillDTO.getExtend41());
                            batchInfoDTO.setExtend42(waybillDTO.getExtend42());
                            batchInfoDTO.setExtend43(waybillDTO.getExtend43());
                            batchInfoDTO.setExtend44(waybillDTO.getExtend44());
//                            batchInfoDTO.setExtend45(waybillDTO.getExtend45());
                            batchInfoDTO.setExtend46(waybillDTO.getExtend46());
                            batchInfoDTO.setExtend47(waybillDTO.getExtend47());
                            batchInfoDTO.setExtend48(waybillDTO.getExtend48());
                            batchInfoDTO.setExtend49(waybillDTO.getExtend49());
                            batchInfoDTO.setExtend50(waybillDTO.getExtend50());
//                            batchInfoDTO.setExtend51(waybillDTO.getExtend51());
//                            batchInfoDTO.setExtend52(waybillDTO.getExtend52());
//                            batchInfoDTO.setExtend53(waybillDTO.getExtend53());
//                            batchInfoDTO.setExtend54(waybillDTO.getExtend54());
//                            batchInfoDTO.setExtend55(waybillDTO.getExtend55());
//                            batchInfoDTO.setExtend56(waybillDTO.getExtend56());
//                            batchInfoDTO.setExtend57(waybillDTO.getExtend57());
//                            batchInfoDTO.setExtend58(waybillDTO.getExtend58());
//                            batchInfoDTO.setExtend59(waybillDTO.getExtend59());
                            batchInfoDTO.setExtend60(waybillDTO.getExtend60());
                            batchInfoDTO.setExtend61(waybillDTO.getExtend61());
                            batchInfoDTO.setExtend62(waybillDTO.getExtend62());
                            batchInfoDTO.setExtend63(waybillDTO.getExtend63());
                            batchInfoDTO.setExtend64(waybillDTO.getExtend64());
                            batchInfoDTO.setExtend65(waybillDTO.getExtend65());
                            batchInfoDTO.setExtend66(waybillDTO.getExtend66());
                            batchInfoDTO.setExtend67(waybillDTO.getExtend67());
                            batchInfoDTO.setExtend68(waybillDTO.getExtend68());
                            batchInfoDTO.setExtend69(waybillDTO.getExtend69());
                            batchInfoDTO.setExtend70(waybillDTO.getExtend70());
                            batchInfoDTO.setExtend71(waybillDTO.getExtend71());
                            batchInfoDTO.setExtend72(waybillDTO.getExtend72());
                            batchInfoDTO.setExtend73(waybillDTO.getExtend73());
                            batchInfoDTO.setExtend74(waybillDTO.getExtend74());
                            batchInfoDTO.setCaseCode(waybillDTO.getCaseCode());
                            batchInfoDTO.setCaseWeight(waybillDTO.getCaseWeight());
                            batchInfoDTO.setPackageForm(waybillDTO.getPackageForm());
                            batchInfoDTO.setArrivalQty(waybillDTO.getArrivalQty());
                            waybillDTO.setBizBatchInfoDTO(batchInfoDTO);
                        }
                    }
                }
            }
            inspectInputItemList.add(inspectInputItem);
        }
        if (!CollectionUtils.isEmpty(idList)) {
            bizReceiptWaybillDataWrap.updateUseRemainder(EnumRealYn.TRUE.getIntValue(), idList);
        }
        if(UtilCollection.isNotEmpty(inspectInputItemList)) {
            // MQ发生消息太大, 置空无用图片信息
            qualifiedDTOList.stream().forEach(o->o.getBizBatchImgDTOList().stream().forEach(b->b.setImgBase64(null)));
            inspectInputHead.setWaybillDTOList(qualifiedDTOList);
            inspectInputHead.setItemList(inspectInputItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, inspectInputHead);
            ctxInput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成验收入库单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_INSPECT_INPUT_STOCK, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }
}
