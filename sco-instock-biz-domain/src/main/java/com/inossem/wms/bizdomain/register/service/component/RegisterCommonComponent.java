package com.inossem.wms.bizdomain.register.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeCaseRelDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.register.EnumFileType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeCaseRel;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.delivery.po.BizReceiptDeliveryNoticeSearchPO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.print.label.LabelReceiptRegisterBox;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 登记业务公共部分 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-11
 */
@Service
@Slf4j
public class RegisterCommonComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected StockCommonService stockCommonService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected ApprovalService approvalService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected LabelDataService labelDataService;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    protected BizReceiptRegisterHeadDataWrap bizReceiptRegisterHeadDataWrap;

    @Autowired
    protected BizReceiptRegisterItemDataWrap bizReceiptRegisterItemDataWrap;

    @Autowired
    protected BizReceiptDeliveryNoticeCaseRelDataWrap noticeCaseRelDataWrap;

    @Autowired
    protected SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap bizReceiptDeliveryNoticeHeadDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap deliveryNoticeItemDataWrap;

    @Autowired
    private BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    /**
     * 登记单保存校验
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void checkSaveRegister(BizContext ctx) {
        // 获取上下文
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            List<String> errorRidList = new ArrayList<>(headDTO.getItemList().size());
            headDTO.getItemList().forEach(itemDTO -> {
                if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    errorRidList.add(itemDTO.getRid());
                }
            });
            if (UtilCollection.isNotEmpty(errorRidList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorRidList.toString());
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 保存登记单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     * @out ctx 出参 {"receiptCode" : "登记单单号"},{@link BizReceiptRegisterHeadDTO :
     *      "已保存的登记单}
     */
    public void saveApply(BizContext ctx) {
        // 入参上下文 - 要保存的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());

        if (UtilNumber.isEmpty(headDTO.getId())) {
            headDTO.setCreateUserId(user.getId());
        }
        headDTO.setModifyUserId(user.getId());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            // 更新登记单
            bizReceiptRegisterHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptRegisterItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptRegisterItem::getHeadId,
                    headDTO.getId());
            bizReceiptRegisterItemDataWrap.physicalDelete(wrapperItem);
            // 删除箱件
            UpdateWrapper<BizReceiptDeliveryNoticeCaseRel> caseRelUpdateWrapper = new UpdateWrapper<>();
            caseRelUpdateWrapper.lambda().eq(BizReceiptDeliveryNoticeCaseRel::getHeadId, headDTO.getId());
            noticeCaseRelDataWrap.remove(caseRelUpdateWrapper);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            // if(headDTO.getReceiptType().equals(EnumReceiptType.LOSE_REGISTER.getValue()))
            // {
            // receiptCode =
            // bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_LOSE_REGISTER.getValue());
            // }else if
            // (headDTO.getReceiptType().equals(EnumReceiptType.DAMAGE_REGISTER.getValue()))
            // {
            // receiptCode =
            // bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TOOL_BAD_REGISTER.getValue());
            // }else
            // if(headDTO.getReceiptType().equals(EnumReceiptType.ARRIVAL_REGISTER.getValue()))
            // {
            // if(dictionaryService.getCorpCacheById(user.getCorpId()).getCorpCode().equals(Const.HL_59C0)){
            // // HL-5ST-RCBD0-DHJSYYZZZZ
            // receiptCode = "HL-5ST-RCBD0-DHJS" +
            // bizCommonService.getNextSequenceYear(EnumSequenceCode.SEQUENCE_ARRIVE_REGISTER.getValue());
            //
            // } else {
            // receiptCode =
            // bizCommonService.getNextSequenceRegisterValue(EnumSequenceCode.SEQUENCE_ARRIVE_REGISTER.getValue(),headDTO.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
            // }
            // }
            receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_REGISTER.getValue());
            headDTO.setReceiptCode(receiptCode);
            // if (UtilString.isNullOrEmpty(headDTO.getPurchaseCode())) {
            //     BizReceiptDeliveryNoticeHead bizReceiptDeliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getById(headDTO.getItemList().get(0).getPreReceiptHeadId());
            //     headDTO.setPurchaseCode(bizReceiptDeliveryNoticeHead.getPurchaseCode());
            //     headDTO.setDeliveryNoticeDescribe(bizReceiptDeliveryNoticeHead.getDeliveryNoticeDescribe());
            //     headDTO.setContractId(bizReceiptDeliveryNoticeHead.getContractId());
            // }
            bizReceiptRegisterHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptRegisterItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            // 到货登记104冲销特殊处理字段
            itemDTO.setWriteOffQty(itemDTO.getQty());
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
        }
        bizReceiptRegisterItemDataWrap.saveBatchDto(headDTO.getItemList());
        if (headDTO.getCaseNowList() != null) {
            for (BizReceiptDeliveryNoticeCaseRelDTO obj : headDTO.getCaseNowList()) {
                obj.setId(null);
                obj.setHeadId(headDTO.getId());
                obj.setModifyTime(new Date());
                obj.setModifyUserId(ctx.getCurrentUser().getId());
                obj.setCreateUserId(ctx.getCurrentUser().getId());
                obj.setCreateTime(new Date());
            }
            noticeCaseRelDataWrap.saveBatchDto(headDTO.getCaseNowList());
        }
        /* ********************** item处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(headDTO);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的登记单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 审批校验
        this.approveCheck(ctx);

        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();

        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        Long ftyId = headDTO.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        // 用户所属部门
        variables.put("userDept", userDept);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx,
                headDTO.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 更新登记单 - 审批中
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 校验审批人
     *
     * @param ctx BizContext
     */
    private void approveCheck(BizContext ctx) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (org.springframework.util.CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }

        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList;
        List<String> level3UserList;

        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            // 查询用户所属部门所属科室的2级审批人
            String deptCode = deptOfficePO.getDeptCode();
            String officeCode = deptOfficePO.getDeptOfficeCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, officeCode,
                    EnumApprovalLevel.LEVEL_2);
            level1UserList.addAll(userList);
        }

        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }

        // 查询维修部-维修支持科-二级审批人
        level2UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.MTD, EnumOffice.MTD07,
                EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "2", EnumDept.MTD.getName(),
                    EnumOffice.MTD07.getName(), EnumApprovalLevel.LEVEL_2.getValue());
        }

        // 维修部-维修支持科-1级审批人
        level3UserList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.MTD, EnumOffice.MTD07,
                EnumApprovalLevel.LEVEL_1);
        if (UtilCollection.isEmpty(level3UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE, "3", EnumDept.MTD.getName(),
                    EnumOffice.MTD07.getName(), EnumApprovalLevel.LEVEL_1.getValue());
        }

    }

    /**
     * sap过账
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "到货登记单"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO :
     *      "补全凭证【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间】"}
     */
    public void postToSap(BizContext ctx) {
        // 入参上下文 - 到货登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptRegisterItemDTO> itemDTOList = headDTO.getItemList();
        /* ******** 设置到货登记单账期 ******** */
        itemDTOList.forEach(p -> p.setPostingDate(headDTO.getPostingDate()));
        this.setInPostDate(itemDTOList, user);
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.posting(JSONArray.toJSONStringWithDateFormat(itemDTOList, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptRegisterItemDTO itemDTO : itemDTOList) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(itemDTO.getReceiptCode())
                                        && item.getReceiptRid().equals(itemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        itemDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                        itemDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                        itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        itemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        itemDTO.setDmbtr(currentReturnObject.getDmbtr());
                    }
                    // 更新到货登记单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、sap过账标识】
                    bizReceiptRegisterItemDataWrap.updateBatchDtoById(itemDTOList);
                }
                // 更新到货登记单状态 - 已记账
                this.updateStatus(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("到货登记单{}SAP过账失败", headDTO.getReceiptCode());
                // 更新到货登记单head、item状态-未同步
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * sap冲销
     *
     * @param ctx ctx
     */
    public void writeOffToSap(BizContext ctx) {
        // 入参上下文 - 到货登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        List<BizReceiptRegisterItemDTO> itemListNotSync = headDTO.getItemList().stream()
                .filter(e -> !StringUtils.hasText(e.getWriteOffMatDocCode()) && StringUtils.hasText(e.getMatDocCode()))
                .collect(Collectors.toList());
        /* ******** 设置冲销账期 ******** */
        this.setInPostDate(itemListNotSync, user);
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.posting(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd",
                    SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptRegisterItemDTO itemDTO : itemListNotSync) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(itemDTO.getReceiptCode())
                                        && item.getReceiptRid().equals(itemDTO.getRid()))
                                .findFirst().orElse(null);
                        if (null == currentReturnObject) {
                            continue;
                        }
                        itemDTO.setWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                        itemDTO.setWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                        itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
                        itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
                        itemDTO.setDmbtr(currentReturnObject.getDmbtr());
                    }
                    // 更新到货登记单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、sap冲销标识】
                    bizReceiptRegisterItemDataWrap.updateBatchDtoById(itemListNotSync);
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("验收单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param itemDTOList 到货登记单行项目
     * @param user        当前用户
     */
    public void setInPostDate(List<BizReceiptRegisterItemDTO> itemDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            Date postingDate = itemDTOList.get(0).getPostingDate();
            Date writeOffPostingDate = itemDTOList.get(0).getWriteOffPostingDate();
            if (UtilObject.isNull(postingDate)) {
                postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            if (UtilObject.isNull(writeOffPostingDate)) {
                writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            // 判断过账日期是否在帐期内
            postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
            writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
            for (BizReceiptRegisterItemDTO itemDTO : itemDTOList) {
                if (EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsWriteOff())) {
                    itemDTO.setDocDate(UtilDate.getNow());
                    itemDTO.setPostingDate(postingDate);
                } else {
                    itemDTO.setWriteOffDocDate(UtilDate.getNow());
                    itemDTO.setWriteOffPostingDate(writeOffPostingDate);
                }
            }
        }
    }

    /**
     * InStock过账
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void postToInStock(BizContext ctx) {
        log.info("登记instock过账 ctx：{}", JSONObject.toJSONString(ctx));
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        try {
            // 修改库存
            stockCommonService.modifyStock(insMoveTypeDTO);
            // 更新单据行项目状态已记账
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            // 添加单据过账日志
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", user.getId());
        } catch (WmsException e) {
            // 失败时更新登记单及行项目为未同步
            log.warn("登记单{}过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * InStock冲销
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void writeOffToInStock(BizContext ctx) {
        log.info("登记instock冲销 ctx：{}", JSONObject.toJSONString(ctx));
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        try {
            // 修改库存
            stockCommonService.modifyStock(insMoveTypeDTO);
            // 更新单据行项目状态已冲销
            this.updateStatus(null, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            // 添加单据过账日志
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", user.getId());
        } catch (WmsException e) {
            // 失败时新登记单及行项目冲销中
            log.warn("登记单{}冲销失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            this.updateStatus(null, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WRITING_OFF.getValue());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新单据状态为已冲销
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void updateStatusWriteOff(BizContext ctx) {
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 所有行项目均已冲销更新单据已冲销
        QueryWrapper<BizReceiptRegisterItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(BizReceiptRegisterItem::getHeadId, headDTO.getId());
        Set<Integer> itemStatusSet = bizReceiptRegisterItemDataWrap.list(itemQueryWrapper).stream()
                .map(p -> p.getItemStatus()).collect(Collectors.toSet());
        if (itemStatusSet.size() == 1
                && itemStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue())) {
            this.updateStatus(headDTO, null, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
        }
    }

    /**
     * 更新上游单据的行项目状态和单据状态
     * 逻辑：
     * 遗失登记单提交审批通过后，
     * 更新前序借用申请单对应的借用归还单行项目数量为0，修改行项目状态为已完成，如果该单据所有行项目状态此时均为已完成，整单修改为已完成
     * 
     * @param ctx
     */
    public void updateUpstreamReceiptStatusByLoseSubmit(BizContext ctx) {
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 根据遗失登记单的行项目中记录的前端单据信息，找到借用申请单，再根据借用申请单，到入库单中查找工器具归还单（归还单的前序单据也是借用申请单）
        Long borrowApplyHeadId = headDTO.getItemList().get(0).getPreReceiptHeadId();
        QueryWrapper<BizReceiptInputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptInputItem::getReferReceiptHeadId, borrowApplyHeadId)
                .eq(BizReceiptInputItem::getReferReceiptType, EnumReceiptType.BORROW_APPLY.getValue());
        List<BizReceiptInputItem> borrowInputItems = bizReceiptInputItemDataWrap.list(queryWrapper);

        Set<Long> borrowApplyItemIds = headDTO.getItemList().stream()
                .map(BizReceiptRegisterItemDTO::getPreReceiptItemId).collect(Collectors.toSet());
        List<BizReceiptInputItem> needUpdateItemList = new ArrayList<>();

        // 将归还单的对应行项目数量设置为0，行项目状态修改为已完成
        for (BizReceiptInputItem borrowInputItem : borrowInputItems) {
            if (borrowApplyItemIds.contains(borrowInputItem.getReferReceiptItemId())) {
                borrowInputItem.setQty(BigDecimal.ZERO);
                borrowInputItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                needUpdateItemList.add(borrowInputItem);
            }
        }
        if (UtilCollection.isNotEmpty(needUpdateItemList)) {
            // 长期借用不会自动生成归还单，先做空列表判断
            bizReceiptInputItemDataWrap.updateBatchByIdOptimize(needUpdateItemList);
            if (borrowInputItems.stream()
                    .allMatch(e -> EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(e.getItemStatus()))) {
                // 当归还单的所有行项目状态被修改后，都是已完成状态，则更新单据为已完成
                UpdateWrapper<BizReceiptInputHead> headUpdateWrapper = new UpdateWrapper<>();
                headUpdateWrapper.lambda()
                        .set(BizReceiptInputHead::getReceiptStatus,
                                EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                        .eq(BizReceiptInputHead::getId, needUpdateItemList.get(0).getHeadId());
                bizReceiptInputHeadDataWrap.update(headUpdateWrapper);
            }
        }

    }

    /**
     * 更新上游单据的行项目状态和单据状态
     * 逻辑：
     * 遗失登记单冲销后，
     * 更新前序借用申请单对应的借用归还单行项目数量为1，修改行项目状态为草稿，并将该单据状态修改为草稿
     * 
     * @param ctx
     */
    public void updateUpstreamReceiptStatusByLoseWriteOff(BizContext ctx) {
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 根据遗失登记单的行项目中记录的前端单据信息，找到借用申请单，再根据借用申请单，到入库单中查找工器具归还单（归还单的前序单据也是借用申请单）
        Long borrowApplyHeadId = headDTO.getItemList().get(0).getPreReceiptHeadId();
        QueryWrapper<BizReceiptInputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptInputItem::getReferReceiptHeadId, borrowApplyHeadId)
                .eq(BizReceiptInputItem::getReferReceiptType, EnumReceiptType.BORROW_APPLY.getValue());
        List<BizReceiptInputItem> borrowInputItems = bizReceiptInputItemDataWrap.list(queryWrapper);

        Set<Long> borrowApplyItemIds = headDTO.getItemList().stream()
                .map(BizReceiptRegisterItemDTO::getPreReceiptItemId).collect(Collectors.toSet());
        List<BizReceiptInputItem> needUpdateItemList = new ArrayList<>();

        // 将归还单的对应行项目状态修改为草稿
        for (BizReceiptInputItem borrowInputItem : borrowInputItems) {
            if (borrowApplyItemIds.contains(borrowInputItem.getReferReceiptItemId())) {
                borrowInputItem.setQty(BigDecimal.ONE);
                borrowInputItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                needUpdateItemList.add(borrowInputItem);
            }
        }

        if (UtilCollection.isNotEmpty(needUpdateItemList)) {
            bizReceiptInputItemDataWrap.updateBatchByIdOptimize(needUpdateItemList);
            // 更新归还单单据抬头为草稿
            UpdateWrapper<BizReceiptInputHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda()
                    .set(BizReceiptInputHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .eq(BizReceiptInputHead::getId, needUpdateItemList.get(0).getHeadId());
            bizReceiptInputHeadDataWrap.update(headUpdateWrapper);
        }

    }

    /**
     * 更新单据状态
     *
     * @param headDTO       登记单抬头
     * @param itemDTOList   登记单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptRegisterHeadDTO headDTO, List<BizReceiptRegisterItemDTO> itemDTOList,
            Integer receiptStatus) {
        if (UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptRegisterHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptRegisterHead::getId, headDTO.getId())
                    .set(BizReceiptRegisterHead::getReceiptStatus, receiptStatus);
            bizReceiptRegisterHeadDataWrap.update(headUpdateWrapper);
        }
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptRegisterItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda()
                    .in(BizReceiptRegisterItem::getId,
                            itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptRegisterItem::getItemStatus, receiptStatus);
            bizReceiptRegisterItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 刪除登记单
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "登记单单号"}
     */
    public void deleteInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置登记单
        BizReceiptRegisterHeadDTO headDTO = UtilBean.newInstance(bizReceiptRegisterHeadDataWrap.getById(headId),
                BizReceiptRegisterHeadDTO.class);
        // 逻辑删除抬头表
        bizReceiptRegisterHeadDataWrap.removeById(headId);
        // 逻辑删除行项目表
        QueryWrapper<BizReceiptRegisterItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptRegisterItem::getHeadId, headId);
        bizReceiptRegisterItemDataWrap.remove(itemWrapper);
        // 登记单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 登记单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
    }

    /**
     * 作废登记单
     *
     * @in ctx 入参 {@link Long : "id"��"抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "登记单单号"}
     */
    public void cancelInfo(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置登记单
        BizReceiptRegisterHeadDTO headDTO = UtilBean.newInstance(bizReceiptRegisterHeadDataWrap.getById(headId),
                BizReceiptRegisterHeadDTO.class);
        if (headDTO.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_CANCEL.getValue())
                || headDTO.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            throw new WmsException("当前单据状态不允许关闭");
        }
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);

        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CANCEL.getValue());
        // 登记单放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 登记单单号放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        // 操作类型放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_CANCEL);
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                    .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的登记单
     */
    public void saveReceiptTree(BizReceiptRegisterHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptRegisterItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保持附件的登记单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存登记单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                headDTO.getReceiptType(), user.getId());
        // 更新抬头表附件状态
        UpdateWrapper<BizReceiptRegisterHead> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(BizReceiptRegisterHead::getId, headDTO.getId())
                .set(BizReceiptRegisterHead::getFileType,
                        UtilCollection.isNotEmpty(headDTO.getFileList()) ? EnumFileType.UPLOADED.getValue()
                                : EnumFileType.UN_UPLOAD.getValue());
        bizReceiptRegisterHeadDataWrap.update(updateWrapper);
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的登记单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", user.getId());
    }

    /**
     * 保存冲销日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存保存冲销日志的登记单"}
     */
    public void saveBizReceiptWriteOffOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存冲销日志的登记单
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", user.getId());
    }

    /**
     * 删除登记单单据流
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除登记单单据流
        receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getId());
    }

    /**
     * 删除登记单附件
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "登记单"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除单据附件
        receiptAttachmentService.deleteBizReceiptAttachment(po.getId(), po.getReceiptType());
    }

    /**
     * 到货登记-打印待检标识-PDA
     * 
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }

        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptRegisterHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }

    /**
     * 填充打印数据
     * 
     * @param ctx
     */
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptRegisterHeadDTO headDTO = (BizReceiptRegisterHeadDTO) po.getHeadDTO();
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 新建领料申请打印实体对象
        List<BizReceiptRegisterItemDTO> itemDTOList = headDTO.getItemList();
        // 将行项目按照 箱件编号进行分组 并求出每组的数量
        Map<String, Long> caseCodeCount = itemDTOList.stream()
                .collect(Collectors.groupingBy(BizReceiptRegisterItemDTO::getCaseCode, Collectors.counting()));
        // 将行项目按照 箱件编号 进行排序-降序
        itemDTOList.sort(Comparator.comparing(BizReceiptRegisterItemDTO::getCaseCode).reversed());
        // 根据上述步骤将行项目的箱件数量填充进去
        itemDTOList = itemDTOList.stream().map(bizReceiptRegisterItemDTO -> {
            caseCodeCount.forEach((k, v) -> {
                if (k.equals(bizReceiptRegisterItemDTO.getCaseCode())) {
                    bizReceiptRegisterItemDTO.setCaseCount(v);
                    AtomicInteger atomicInteger = new AtomicInteger(v.intValue());
                    int i = atomicInteger.incrementAndGet();
                    bizReceiptRegisterItemDTO.setRid(String.valueOf(i--));
                }
            });
            return bizReceiptRegisterItemDTO;
        }).collect(Collectors.toList());

        List<LabelReceiptRegisterBox> receiptRegisterBoxes = new ArrayList<>();

        itemDTOList.forEach(itemDTO -> {

            // 标签打印数据
            this.setPrintLabelData(receiptRegisterBoxes, itemDTO, headDTO, "");

        });

        // 填充打印信息
        printInfo.setLabelBoxList(receiptRegisterBoxes);
        if (UtilCollection.isNotEmpty(receiptRegisterBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, receiptRegisterBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message = ProducerMessageContent
                    .messageContent(TagConst.PRINT_RECEIPT_REGISTER_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 标签打印的数据
     * 
     * @param receiptRegisterBoxes 装载打印的数据
     * @param itemDTO              要打印数据的行项目
     * @param labelCode            RFID 编码
     */
    private void setPrintLabelData(List<LabelReceiptRegisterBox> receiptRegisterBoxes,
            BizReceiptRegisterItemDTO itemDTO, BizReceiptRegisterHeadDTO headDTO, String labelCode) {
        LabelReceiptRegisterBox labelReceiptRegisterBox = UtilBean.newInstance(itemDTO, LabelReceiptRegisterBox.class);
        labelReceiptRegisterBox.setReceiptCode(itemDTO.getReferReceiptCode());
        List<Long> idList = new ArrayList<>();
        idList.add(itemDTO.getModifyUserId());

        List<SysUser> userList = (List<SysUser>) dictionaryService.getSysUserCacheByIds(idList);
        if (UtilCollection.isNotEmpty(userList)) {
            labelReceiptRegisterBox.setReceiver(userList.get(0).getUserName());
        }
        labelReceiptRegisterBox.setDeliveryNoticeDescribe(headDTO.getDeliveryNoticeDescribe());
        labelReceiptRegisterBox.setNeedDeptCode(itemDTO.getApplyUserDeptName());
        labelReceiptRegisterBox.setLabelIsRFID(EnumRealYn.FALSE.getIntValue());
        receiptRegisterBoxes.add(labelReceiptRegisterBox);
    }

    /**
     * 通过head id获取item
     * 
     * @param headId head id
     * @return
     */
    private BizReceiptRegisterHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptRegisterHead bizReceiptRegisterHead = bizReceiptRegisterHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptRegisterHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptRegisterHeadDTO headDTO = UtilBean.newInstance(bizReceiptRegisterHead,
                BizReceiptRegisterHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptRegisterHead bizReceiptApplyHead = bizReceiptRegisterHeadDataWrap.getById(headId);

        // 判断单据是否开启审批
        boolean wfByReceiptType = false;
        if (EnumReceiptType.LOSE_REGISTER.getValue().equals(bizReceiptApplyHead.getReceiptType())) {
            // 遗失登记
            wfByReceiptType = true;
        } else if (EnumReceiptType.DAMAGE_REGISTER.getValue().equals(bizReceiptApplyHead.getReceiptType())) {
            // 损坏登记
            wfByReceiptType = true;
        }

        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService
                            .getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(
                                Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询 油品送货通知
     * 
     * @param context
     */
    public void getDeliveryNotice(BizContext context) {

        BizReceiptDeliveryNoticeSearchPO po = context.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //po.setSendType(EnumSendType.OIL_PROCUREMENT.getValue());
        // 查询油品采购和在岸采购      
        po.setSendTypeList(Arrays.asList(EnumSendType.OIL_PROCUREMENT.getValue(),EnumSendType.ONSHORE_PROCUREMENT.getValue()));

        List<Integer> receiptStatusList = new ArrayList<>();
        receiptStatusList.add(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        po.setReceiptStatusList(receiptStatusList);

        List<BizReceiptDeliveryNoticeHead> deliveryNoticeHeadList = bizReceiptDeliveryNoticeHeadDataWrap.getBaseMapper()
                .selectDeliveryNotice(po);

        List<BizReceiptDeliveryNoticeHeadDTO> deliveryNoticeHeadDTOList = UtilCollection.toList(deliveryNoticeHeadList,
                BizReceiptDeliveryNoticeHeadDTO.class);

        dataFillService.fillAttr(deliveryNoticeHeadDTOList);

        List<BizReceiptRegisterHeadDTO> headDTOList = new ArrayList<>();

        for (BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO : deliveryNoticeHeadDTOList) {

            BizReceiptRegisterHeadDTO registerHeadDTO = UtilBean.newInstance(deliveryNoticeHeadDTO,
                    BizReceiptRegisterHeadDTO.class);

            registerHeadDTO.setId(null);
            registerHeadDTO.setReceiptCode(null);
            registerHeadDTO.setReceiptType(EnumReceiptType.ARRIVAL_REGISTER.getValue());
            registerHeadDTO.setReceiptStatus(null);
            registerHeadDTO.setCreateTime(null);
            registerHeadDTO.setModifyTime(null);
            registerHeadDTO.setCreateUserId(null);
            registerHeadDTO.setModifyUserId(null);
            registerHeadDTO.setCreateUserCode(null);
            registerHeadDTO.setCreateUserName(null);
            registerHeadDTO.setModifyUserCode(null);
            registerHeadDTO.setModifyUserName(null);
            registerHeadDTO.setDeliverNoticeCode(deliveryNoticeHeadDTO.getReceiptCode());
            registerHeadDTO.setLogList(null);

            List<BizReceiptRegisterItemDTO> itemDTOList = new ArrayList<>();
            for (BizReceiptDeliveryNoticeItemDTO deliveryNoticeItemDTO : deliveryNoticeHeadDTO.getItemList()) {
                BizReceiptRegisterItemDTO itemDTO = UtilBean.newInstance(deliveryNoticeItemDTO,
                        BizReceiptRegisterItemDTO.class);

                itemDTO.setPreReceiptHeadId(deliveryNoticeHeadDTO.getId());
                itemDTO.setPreReceiptItemId(deliveryNoticeItemDTO.getId());
                itemDTO.setPreReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());
                itemDTO.setPreReceiptQty(deliveryNoticeItemDTO.getQty());

                itemDTO.setQty(deliveryNoticeItemDTO.getQty().subtract(deliveryNoticeItemDTO.getRegisterQty()));

                itemDTO.setId(null);
                itemDTO.setHeadId(null);
                itemDTO.setItemStatus(null);
                itemDTO.setCreateTime(null);
                itemDTO.setModifyTime(null);
                itemDTO.setCreateUserId(null);
                itemDTO.setModifyUserId(null);
                itemDTO.setCreateUserCode(null);
                itemDTO.setCreateUserName(null);
                itemDTO.setModifyUserCode(null);
                itemDTO.setModifyUserName(null);

                itemDTOList.add(itemDTO);
                
            }
            registerHeadDTO.setItemList(itemDTOList);
            headDTOList.add(registerHeadDTO);

        }
        context.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTOList);

    }

    /**
     * 回写送货通知 已登记数量
     * 
     * @param context
     */
    public void writeBackRegisterQty(BizContext context) {

        BizReceiptRegisterHeadDTO po = context.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if (po == null || UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Map<Long, BigDecimal> deliveryNoticeItemMap = new HashMap<>();

        for (BizReceiptRegisterItemDTO item : po.getItemList()) {
            if (EnumReceiptType.DELIVERY_NOTICE.getValue().equals(item.getPreReceiptType())) {
                Long deliveryNoticeItemId = item.getPreReceiptItemId();
                BigDecimal registerQty = item.getQty();

                deliveryNoticeItemMap.merge(deliveryNoticeItemId, registerQty, BigDecimal::add);
            }
        }

        if (!deliveryNoticeItemMap.isEmpty()) {
            // 获取所有需要更新的送货通知行项目ID
            List<Long> itemIds = new ArrayList<>(deliveryNoticeItemMap.keySet());

            // 批量查询送货通知行项目
            List<BizReceiptDeliveryNoticeItem> items = deliveryNoticeItemDataWrap.listByIds(itemIds);

            // 更新已登记数量
            for (BizReceiptDeliveryNoticeItem item : items) {
                BigDecimal registerQty = deliveryNoticeItemMap.get(item.getId());
                if (registerQty != null) {
                    item.setRegisterQty(item.getRegisterQty().add(registerQty));
                }
            }

            // 批量更新送货通知行项目
            if (!items.isEmpty()) {
                deliveryNoticeItemDataWrap.updateBatchById(items);
            }
        }
    }


    /**
     * 到货登记提交校验采购订单是否存在
     */
    public void checkPurchaseOrder(BizContext ctx) {
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        if (headDTO == null || UtilCollection.isEmpty(headDTO.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 过滤出前序单据为送货通知的行项目
        List<BizReceiptRegisterItemDTO> deliveryNoticeItems = headDTO.getItemList().stream()
            .filter(item -> EnumReceiptType.DELIVERY_NOTICE.getValue().equals(item.getPreReceiptType()))
            .collect(Collectors.toList());

        if (UtilCollection.isEmpty(deliveryNoticeItems)) {
            return;
        }

        // 校验采购订单信息
        for (BizReceiptRegisterItemDTO item : deliveryNoticeItems) {
            if (UtilString.isNullOrEmpty(item.getPurchaseCode()) 
                || UtilString.isNullOrEmpty(item.getPurchaseRid())) {
                throw new WmsException(EnumReturnMsg.RETURN_ERROR_PURCHASE_ORDER_NOT_EXISTS, 
                    String.format("行项目[%s]", item.getRid()));
            }
        }
    }

    /**
     * 单据撤销
     * 当下游质检分配单为草稿时（未做任何拆单操作，行项目均为草稿）
     * 到货登记允许撤销，并删除下游的质检分配单；同时到货登记状态变为草稿，允许重新编辑提交；
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 下游质检分配单
        List<BizReceiptInspectItem> bizReceiptInspectItems = bizReceiptInspectItemDataWrap.list(new QueryWrapper<BizReceiptInspectItem>().lambda().eq(BizReceiptInspectItem::getPreReceiptHeadId, headDTO.getId()));
        if (UtilCollection.isNotEmpty(bizReceiptInspectItems) && bizReceiptInspectItems.stream().allMatch(c -> c.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()))) {
            // 删除下游质检分配单
            bizReceiptInspectHeadDataWrap.physicalDeleteById(bizReceiptInspectItems.get(0).getHeadId());
            bizReceiptInspectItemDataWrap.multiPhysicalDeleteByIdList(bizReceiptInspectItems.stream().map(BizReceiptInspectItem::getId).collect(Collectors.toList()));
            // 删除单据流
            receiptRelationService.deleteRelationByHeadId(EnumReceiptType.DISTRIBUTE_INSPECTION_PURCHASE.getValue(), bizReceiptInspectItems.get(0).getHeadId());
            // 更新登记单状态为草稿
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            // 保存操作日志
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE, "", user.getId());
        } else {
            throw new WmsException("下游单据不支持撤销操作");
        }
    }

}
