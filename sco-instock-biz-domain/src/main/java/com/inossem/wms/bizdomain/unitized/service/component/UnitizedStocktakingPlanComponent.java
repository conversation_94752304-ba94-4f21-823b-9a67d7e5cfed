package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizStocktakingDocBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.electronicscale.service.biz.LogElectronicScaleRecordService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageBinDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelStockdocReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizStockdocLabelDataDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.*;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.component.moveType.UnitizedStocktakingMoveTypeComponent;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.stocktaking.*;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizStocktakingDocBatchInfo;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.*;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.*;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.*;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingHeadPageVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.StocktakingCreateExportVO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.StocktakingPlanExportVO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskItem;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizStockdocLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizStockdocLabelData;
import com.inossem.wms.common.model.label.entity.BizStockdocLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFacotryWbs;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsDocBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.key.StockBinKey;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 库存盘点代码代码块
 * </p>
 */

@Service
@Slf4j
public class UnitizedStocktakingPlanComponent {

    /**
     * 数据填充实现类
     */
    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ErpPostingService erpPostingService;

    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected EditCacheService editCacheService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected UnitizedStocktakingMoveTypeComponent stocktakingMoveTypeComponent;

    @Autowired
    protected DicWhStorageBinDataWrap dicWhStorageBinDataWrap;

    @Autowired
    protected BizReceiptStocktakingHeadDataWrap bizReceiptStocktakingHeadDataWrap;

    @Autowired
    protected BizReceiptStocktakingItemDataWrap bizReceiptStocktakingItemDataWrap;

    @Autowired
    protected BizReceiptStocktakingBinDataWrap bizReceiptStocktakingBinDataWrap;

    @Autowired
    protected SysUserDataWrap sysUserDataWrap;

    @Autowired
    private BizReceiptStocktakingUserDataWrap bizReceiptStocktakingUserDataWrap;

    @Autowired
    private BizReceiptStocktakingSignUserDataWrap bizReceiptStocktakingSignUserDataWrap;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private LogElectronicScaleRecordService logElectronicScaleRecordService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private BizReceiptStocktakingRelatedReceiptDataWrap bizReceiptStocktakingRelatedReceiptDataWrap;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private BizReceiptTaskItemDataWrap bizReceiptTaskItemDataWrap;

    @Autowired
    private BizStockdocLabelDataDataWrap labelDataDataWrap;

    @Autowired
    private LabelStockdocReceiptRelService labelReceiptRelService;

    @Autowired
    private BizReceiptStocktakingDocHeadDataWrap bizReceiptStocktakingDocHeadDataWrap;

    @Autowired
    private BizStocktakingDocBatchInfoDataWrap bizStocktakingDocBatchInfoDataWrap;

    @Autowired
    private BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;

    private static final String DEFAULT_BIN_CODE = "00";

    private static final BigDecimal DEFAULT_SPOT_CHECK_RATIO = BigDecimal.valueOf(100);

    /**
     * 查询盘点模式下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("EnumStocktakingType.toList()":"盘点模式下拉框")}
     */
    public void getStocktakingTypeDown(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumStocktakingType.toPlanList()));
    }

    /**
     * 查询物资类型下拉
     *
     * @out ctx 出参 {@link MultiResultVO <> ("list":"物资类型下拉框内容")}
     */
    public void getExtend28Down(BizContext ctx) {
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(EnumMatType.toList()));
    }

    /**
     * 页面初始化: 1、设置盘点单【单据类型、创建时间、创建人】 2、设置按钮权限【提交】 3、设置扩展功能【无】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"库存盘点","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        /* ********* 页面初始化返回空的行项目、扩展项对象、按钮组【保存和提交】 ******** */
        BizResultVO<BizReceiptStocktakingPlanHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptStocktakingPlanHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_PLAN_STOCK_TAKE.getValue())
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                .setStocktakingMode(EnumStocktakingModeType.STOCKTAKING_MODE_MING.getValue())
                .setStocktakingType(EnumStocktakingType.PLAN_STOCKTAKING.getValue())
                .setSpotCheckRatio(DEFAULT_SPOT_CHECK_RATIO)
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
            new ExtendVO(), new ButtonVO().setButtonSubmit(true));
        /* ********* 页面初始化数据放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取盘点人列表
     *
     * @in ctx 入参 {@link BizReceiptStocktakingSearchUserListPO : "查询用户列表入参"}
     * @out ctx 出参 {@link MultiResultVO <> ("sysUserDTOList":"用户列表")}
     */
    public void setUserList(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptStocktakingSearchUserListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<SysUser> page = new Page<>(po.getPageIndex(), po.getPageSize());
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getUserName()), SysUser::getUserName, po.getUserName());
        sysUserDataWrap.page(page, queryWrapper);
        // 放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 获取盘点人反显name
     *
     * @in ctx 入参 {@link BizReceiptStocktakingSearchUserListPO : "查询用户列表入参"}
     * @out ctx 出参 {@link String ("userName":"用户name描述")}
     */
    public void setUserNameList(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptStocktakingSearchUserListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 定义出参
        List<String> userNameList = new ArrayList<>(po.getUserCodeList().size());
        if(UtilCollection.isNotEmpty(po.getUserCodeList())) {
            // 查询处理
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SysUser::getUserCode, po.getUserCodeList());
            List<SysUser> userList = sysUserDataWrap.list(queryWrapper);
            po.getUserCodeList().forEach(p -> {
                userList.forEach(q -> {
                    if(p.equals(q.getUserCode())) {
                        userNameList.add(q.getUserName());
                    }
                });
            });
        }
        // 放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, String.join(",", userNameList));
    }

    /**
     * 查询盘点单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptStocktakingHeadSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setUserId(cUser.getId());

        if (po == null) {
            po = new BizReceiptStocktakingHeadSearchPO();
        }
        if (UtilNumber.isNull(po.getIsElectronicScale())) {
            po.setIsElectronicScale(EnumRealYn.FALSE.getIntValue());
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        /* ********* 分页查询处理 ******** */
        IPage<BizReceiptStocktakingHeadPageVO> page = po.getPageObj(BizReceiptStocktakingHeadPageVO.class);
        bizReceiptStocktakingHeadDataWrap.getBizReceiptStocktakingPlanHeadPageVOList(page, po);
        /* ********* 分页结果信息放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 查询盘点单详情
     *
     * @in ctx 入参 {@link Long :"抬头表id"}
     * @out ctx 出参 {@link BizResultVO<> ("head":"验收入库单详情","extend":"扩展功能配置","button":"按钮组")}
     */
    public void setInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点单 ******** */
        BizReceiptStocktakingHead bizReceiptStockTakingHead = bizReceiptStocktakingHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingPlanHeadDTO bizReceiptStockTakingHeadDTO = UtilBean.newInstance(bizReceiptStockTakingHead, BizReceiptStocktakingPlanHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStockTakingHeadDTO);
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStockTakingHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        /* ********* 库存盘点单详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStockTakingHeadDTO, extendVO, buttonVO));
    }

    /**
     * 设置按钮组
     *
     * @param bizReceiptStockTakingHeadDTO 库存盘点单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptStocktakingPlanHeadDTO bizReceiptStockTakingHeadDTO) {
        /* ********* 设置单据抬头状态 ******** */
        Integer receiptStatus = bizReceiptStockTakingHeadDTO.getReceiptStatus();
        /* ********* 设置盘点类型 ******** */
        Integer isReplay = bizReceiptStockTakingHeadDTO.getIsReplay();
        if (null == receiptStatus) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            /* ********* 草稿 -【保存、提交、删除】 ******** */
            buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue().equals(receiptStatus)) {
            /* ********* 待计数 -【删除】 ******** */
            buttonVO.setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_COUNTED.getValue().equals(receiptStatus)) {
            /* ********* 已计数 -【完成、整单复盘、查看差异】 ******** */
            buttonVO.setButtonFinish(true).setButtonReInventoryAll(true).setButtonShowDiffType(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            /* ********* 已完成 -【查看差异】 ******** */
            buttonVO.setButtonShowDiffType(true);
            /* ********* 复盘单 -【过账】 ******** */
            if (EnumStocktakingReplayType.STOCKTAKING_REPLAY_YES.getValue().equals(isReplay)) {
                buttonVO.setButtonPost(true);
            }
        }
        return buttonVO;
    }

    /**
     * 【首盘可过账模式】查询盘点单详情
     *
     * @in ctx 入参 {@link Long :"抬头表id"}
     * @out ctx 出参 {@link BizResultVO<> ("head":"验收入库单详情","extend":"扩展功能配置","button":"按钮组")}
     */
    public void setInfoFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点单 ******** */
        BizReceiptStocktakingHead bizReceiptStockTakingHead = bizReceiptStocktakingHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingPlanHeadDTO bizReceiptStockTakingHeadDTO = UtilBean.newInstance(bizReceiptStockTakingHead, BizReceiptStocktakingPlanHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        // 28565 仅查询抬头单详情, 行项目信息由单独的接口分页获取
        dataFillService.fillAttrBField(bizReceiptStockTakingHeadDTO, BizReceiptStocktakingPlanHeadDTO::getItemList);

        bizReceiptStockTakingHeadDTO.setSign1(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign2(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign3(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign4(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign5(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign6(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign7(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign8(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign9(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign10(UtilPrint.SIGNATURE);
        List<BizReceiptStocktakingSignUserDTO> userDTOList = bizReceiptStockTakingHeadDTO.getStocktakingSignUserList();
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                BizReceiptStocktakingSignUserDTO userDTO = userDTOList.get(i);
                String signName = userDTO.getSignName();
                if (StringUtils.isNoneBlank(signName) && signName.startsWith("data:image/svg")) {
                    if (i == 0) {
                        bizReceiptStockTakingHeadDTO.setSign1(signName);
                        continue;
                    }
                    if (i == 1) {
                        bizReceiptStockTakingHeadDTO.setSign2(signName);
                        continue;
                    }
                    if (i == 2) {
                        bizReceiptStockTakingHeadDTO.setSign3(signName);
                        continue;
                    }
                    if (i == 3) {
                        bizReceiptStockTakingHeadDTO.setSign4(signName);
                        continue;
                    }
                    if (i == 4) {
                        bizReceiptStockTakingHeadDTO.setSign5(signName);
                        continue;
                    }
                    if (i == 5) {
                        bizReceiptStockTakingHeadDTO.setSign6(signName);
                        continue;
                    }
                    if (i == 6) {
                        bizReceiptStockTakingHeadDTO.setSign7(signName);
                        continue;
                    }
                    if (i == 7) {
                        bizReceiptStockTakingHeadDTO.setSign8(signName);
                        continue;
                    }
                    if (i == 8) {
                        bizReceiptStockTakingHeadDTO.setSign9(signName);
                        continue;
                    }
                    if (i == 9) {
                        bizReceiptStockTakingHeadDTO.setSign10(signName);
                        break;
                    }
                }
            }
        }

        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButtonFirstCanBePosted(bizReceiptStockTakingHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
        bizReceiptStockTakingHeadDTO.setRelationList(receiptRelationService.getReceiptTree(bizReceiptStockTakingHeadDTO.getReceiptType(), bizReceiptStockTakingHeadDTO.getId(), null));
        /* ********* 库存盘点单详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStockTakingHeadDTO, extendVO, buttonVO));
    }


    /**
     * 【首盘可过账模式】查询盘点单详情
     *
     * @in ctx 入参 {@link Long :"抬头表id"}
     * @out ctx 出参 {@link BizResultVO<> ("head":"验收入库单详情","extend":"扩展功能配置","button":"按钮组")}
     */
    public void getItemInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        BizReceiptStocktakingHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ********* 查询盘点bin表分页 ******** */
        IPage<BizReceiptStocktakingBin> page = new Page<>(po.getPageIndex(), po.getPageSize());
        WmsLambdaQueryWrapper<BizReceiptStocktakingBin> binQueryWrapper = new WmsLambdaQueryWrapper<>();
        binQueryWrapper.eq(BizReceiptStocktakingBin::getHeadId, po.getHeadId());
        bizReceiptStocktakingBinDataWrap.page(page, binQueryWrapper);
        /* ********* 泛型转换 ******** */
        List<BizReceiptStocktakingBinDTO> binDTOList = UtilCollection.toList(page.getRecords(), BizReceiptStocktakingBinDTO.class);
        /* ********* 查询盘点item表 ******** */
        List<Long> itemIdList = binDTOList.stream().map(BizReceiptStocktakingBinDTO::getItemId).distinct().collect(Collectors.toList());
        List<BizReceiptStocktakingItem> itemList = bizReceiptStocktakingItemDataWrap.listByIds(itemIdList);
        /* ********* 泛型转换 ******** */
        List<BizReceiptStocktakingItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptStocktakingItemDTO.class);
        /* ********* 组装itemDTO ******** */
        for (BizReceiptStocktakingItemDTO itemDTO : itemDTOList) {
            itemDTO.setBinList(new ArrayList<>());
            for (BizReceiptStocktakingBinDTO binDTO : binDTOList) {
                if (itemDTO.getId().equals(binDTO.getItemId())) {
                    itemDTO.getBinList().add(binDTO);
                }
            }
        }
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(binDTOList);
        dataFillService.fillAttrBField(itemDTOList, BizReceiptStocktakingItemDTO::getBinList);
        itemDTOList.forEach(p -> p.getBinList().forEach(q -> q.setToolCode(q.getBatchCode())));
        /* ********* 库存盘点单详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(itemDTOList, page.getTotal()));
    }

    public void getStocktakingUserSign(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点单 ******** */
        BizReceiptStocktakingHead bizReceiptStockTakingHead = bizReceiptStocktakingHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingPlanHeadDTO bizReceiptStockTakingHeadDTO = UtilBean.newInstance(bizReceiptStockTakingHead, BizReceiptStocktakingPlanHeadDTO.class);

        QueryWrapper<BizReceiptStocktakingItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingItem::getHeadId, headId);
        List<BizReceiptStocktakingItem> itemList = bizReceiptStocktakingItemDataWrap.list(queryWrapper);
        /* ********* 数据泛型转换为DTO ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemDTOList =
                UtilCollection.toList(itemList, BizReceiptStocktakingPlanItemDTO.class);
        /* ********* 填充关联属性 ******** */
        dataFillService.fillRlatAttrDataList(itemDTOList);
        /* ********* 设置盘点类型 盘点方式 *PDA判断使用* ******** */
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(headId);
        itemDTOList.forEach(item ->
                item.setIsReplay(head.getIsReplay())
                        .setStocktakingMode(head.getStocktakingMode()));
        bizReceiptStockTakingHeadDTO.setItemList(itemDTOList);
        QueryWrapper<BizReceiptStocktakingSignUser> signQueryWrapper = new QueryWrapper<>();
        signQueryWrapper.lambda().eq(BizReceiptStocktakingSignUser::getHeadId, bizReceiptStockTakingHeadDTO.getId());
        List<BizReceiptStocktakingSignUser> entityList = bizReceiptStocktakingSignUserDataWrap.list(signQueryWrapper);
        List<BizReceiptStocktakingSignUserDTO> signDTOList =
                UtilCollection.toList(entityList, BizReceiptStocktakingSignUserDTO.class);
        bizReceiptStockTakingHeadDTO.setStocktakingSignUserList(signDTOList);
        bizReceiptStockTakingHeadDTO.setSign1(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign2(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign3(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign4(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign5(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign6(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign7(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign8(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign9(UtilPrint.SIGNATURE);
        bizReceiptStockTakingHeadDTO.setSign10(UtilPrint.SIGNATURE);
        List<BizReceiptStocktakingSignUserDTO> userDTOList = bizReceiptStockTakingHeadDTO.getStocktakingSignUserList();
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                BizReceiptStocktakingSignUserDTO userDTO = userDTOList.get(i);
                String signName = userDTO.getSignName();
                if (StringUtils.isNoneBlank(signName) && signName.startsWith("data:image/svg")) {
                    if (i == 0) {
                        bizReceiptStockTakingHeadDTO.setSign1(signName);
                        continue;
                    }
                    if (i == 1) {
                        bizReceiptStockTakingHeadDTO.setSign2(signName);
                        continue;
                    }
                    if (i == 2) {
                        bizReceiptStockTakingHeadDTO.setSign3(signName);
                        continue;
                    }
                    if (i == 3) {
                        bizReceiptStockTakingHeadDTO.setSign4(signName);
                        continue;
                    }
                    if (i == 4) {
                        bizReceiptStockTakingHeadDTO.setSign5(signName);
                        continue;
                    }
                    if (i == 5) {
                        bizReceiptStockTakingHeadDTO.setSign6(signName);
                        continue;
                    }
                    if (i == 6) {
                        bizReceiptStockTakingHeadDTO.setSign7(signName);
                        continue;
                    }
                    if (i == 7) {
                        bizReceiptStockTakingHeadDTO.setSign8(signName);
                        continue;
                    }
                    if (i == 8) {
                        bizReceiptStockTakingHeadDTO.setSign9(signName);
                        continue;
                    }
                    if (i == 9) {
                        bizReceiptStockTakingHeadDTO.setSign10(signName);
                        break;
                    }
                }
            }
        }
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButtonFirstCanBePosted(bizReceiptStockTakingHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        /* ********* 库存盘点单详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStockTakingHeadDTO, extendVO, buttonVO));
    }

    /**
     * 【首盘可过账模式】设置按钮组
     *
     * @param bizReceiptStockTakingHeadDTO 库存盘点单
     * @return 按钮组对象
     */
    private ButtonVO setButtonFirstCanBePosted(BizReceiptStocktakingPlanHeadDTO bizReceiptStockTakingHeadDTO) {
        /* ********* 设置单据抬头状态 ******** */
        Integer receiptStatus = bizReceiptStockTakingHeadDTO.getReceiptStatus();
        if (null == receiptStatus) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            /* ********* 草稿 -【保存、提交、删除】 ******** */
            buttonVO.setButtonSave(true).setButtonSubmit(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue().equals(receiptStatus)) {
            /* ********* 待计数 ,待认领-【删除】 ******** */
           // buttonVO.setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            /* ********* 已提交  ******** */

        }else if (EnumReceiptStatus.RECEIPT_STATUS_COUNTED.getValue().equals(receiptStatus)) {
            /* ********* 已计数 -【完成、复盘、查看差异】 ******** */
            buttonVO.setButtonFinish(true).setButtonReInventorySelect(true).setButtonShowDiffType(true)
            .setButtonSave(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            /* ********* 已完成 打印 ******** */
            buttonVO.setButtonPrint(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            /* ********* 已过账 -【查看差异】 ******** */
            buttonVO.setButtonShowDiffType(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            /* ********* 过账失败后成为  未同步 -【查看差异，过账】 ******** */
            buttonVO.setButtonShowDiffType(true).setButtonPost(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            /* ********* 审批驳回 已驳回 -【完成、复盘、查看差异】 ******** */
            buttonVO.setButtonFinish(true).setButtonReInventorySelect(true).setButtonShowDiffType(true)
                    .setButtonSave(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            /* ********* 审批中 不可操作 ******** */

        }
        return buttonVO;
    }

    /**
     * 盘点人签名按钮展示
     * @param ctx
     * @return
     */
    public void setButtonUserSign(BizContext ctx) {
        BizResultVO vo=ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptStocktakingPlanHeadDTO bizReceiptStockTakingHeadDTO = (BizReceiptStocktakingPlanHeadDTO)vo.getHead();
        Integer receiptStatus = bizReceiptStockTakingHeadDTO.getReceiptStatus();
        if (null == receiptStatus) {
            return ;
        }
        ButtonVO buttonVO = new ButtonVO();
         if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            /* ********* 已提交  ******** */
             buttonVO.setButtonSave(true).setButtonSubmit(true);
         }

        vo.setButton(buttonVO);
    }

    /**
     * 设置批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO<> ("BizReceiptStocktakingPlanHeadDTO":"库存盘点单详情")}
     * @out ctx 出参 {@link BizResultVO<> ("BizReceiptStocktakingPlanHeadDTO":"库存盘点单单详情及批次图片")}
     */
    public void setBatchImg(BizContext ctx) {
        /* ********* 从上下文获取库存盘点单详情 ******** */
        BizResultVO<BizReceiptStocktakingPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead()) || UtilCollection.isEmpty(resultVO.getHead().getItemList())) {
            return;
        }
        /* ********* 设置批次id集合 ******** */
        Set<Long> collect = new HashSet<>();
        resultVO.getHead().getItemList().forEach(itemDTO -> {
            if (UtilCollection.isNotEmpty(itemDTO.getBinList())) {
                collect.addAll(itemDTO.getBinList().stream().map(BizReceiptStocktakingPlanBinDTO::getBatchId)
                    .collect(Collectors.toSet()));
            }
        });
        /* ********* 获取批次图片 ******** */
        Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(collect, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        /* ********* 赋值批次图片 ******** */
        resultVO.getHead().getItemList().forEach(itemDTO -> {
            if (UtilCollection.isNotEmpty(itemDTO.getBinList())) {
                itemDTO.getBinList().forEach(binDTO -> {
                    if (UtilNumber.isNotEmpty(binDTO.getBatchId())
                        && !CollectionUtils.isEmpty(imgMap.get(binDTO.getBatchId()))) {
                        binDTO.setBizBatchImgDTOList(imgMap.get(binDTO.getBatchId()));
                    }
                });
            }
        });
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO<> ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO<> ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        /* ********* 从上下文获取扩展功能对象 ******** */
        BizResultVO<BizReceiptStocktakingPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 设置附件开启/关闭 ******** */
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 查询仓位库存列表
     *
     * @in ctx 入参 {@link BizReceiptStocktakingSearchStockBinPO :"查询条件对象"}
     * @out ctx 出参 {@link MultiResultVO<> ("itemList":"仓位库存列表")}
     */
    public void setStockBinList(BizContext ctx) {
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingSearchStockBinPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 查询仓位库存 ******** */
        List<BizReceiptStocktakingPlanBinDTO> dtoList = bizReceiptStocktakingBinDataWrap.selectStockBinPlanList(po);
        /* ********* 过滤默认存储类型 ******** */
        List<BizReceiptStocktakingPlanBinDTO> bizReceiptStocktakingBinDTOList = new ArrayList<>();
        dtoList.forEach(p -> {
            if (!UtilConst.getInstance().getDefaultStorageTypeCodeSet().contains(p.getTypeCode())) {
                bizReceiptStocktakingBinDTOList.add(p);
            }
        });
        /* ********* 仓位库存按仓库id 存储类型id 仓位id分组 ******** */
        Map<String, List<BizReceiptStocktakingPlanBinDTO>> stockBinMap = bizReceiptStocktakingBinDTOList.stream().collect(Collectors.groupingBy(e -> getGroupKey(e)));
        /* ********* 设置盘点单行项目集合 ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemList = new ArrayList();
        stockBinMap.values().stream().forEach(p -> {
            /* ********* 设置盘点单行项目对象 ******** */
            BizReceiptStocktakingPlanItemDTO bizReceiptStockTakingItemDTO = UtilBean.newInstance(p.get(0), BizReceiptStocktakingPlanItemDTO.class);
            /* ********* 如果仓位下物料批次id为空 证明是空仓位 ******** */
            if (UtilNumber.isNotEmpty(p.get(0).getMatId()) && UtilNumber.isNotEmpty(p.get(0).getBatchId())) {
                /* ********* 设置仓位库存分组 首盘-按物料分组 复盘-按物料批次分组 ******** */
                Map<Long, List<BizReceiptStocktakingPlanBinDTO>> binMap = new HashMap<>();
                if (po.getIsReplay().equals(EnumStocktakingReplayType.STOCKTAKING_REPLAY_NO.getValue())) {
                    binMap = p.stream().collect(Collectors.groupingBy(BizReceiptStocktakingPlanBinDTO::getMatId));
                } else {
                    binMap = p.stream().collect(Collectors.groupingBy(BizReceiptStocktakingPlanBinDTO::getBatchId));
                }
                /* ********* 设置盘点单物料明细集合 ******** */
                List<BizReceiptStocktakingPlanBinDTO> binList = new ArrayList();
                binMap.values().forEach(q -> {
                    /* ********* 设置盘点单物料明细对象 ******** */
                    BizReceiptStocktakingPlanBinDTO bizReceiptStocktakingBinDTO = q.get(0);
                    if (EnumRealYn.FALSE.getIntValue().equals(bizReceiptStocktakingBinDTO.getFreezeOutput())
                        && EnumRealYn.FALSE.getIntValue().equals(bizReceiptStocktakingBinDTO.getFreezeInput())) {
                        /* ********* 相同物料数量汇总 ******** */
                        BigDecimal stockQty = q.stream().map(BizReceiptStocktakingPlanBinDTO::getStockQty)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        bizReceiptStocktakingBinDTO.setStockQty(stockQty);
                        /* ********* 计算物料库存金额 ******** */
                        // bizReceiptStocktakingBinDTO.setStockPrice(stockQty.multiply(q.get(0).getMoveAvgPrice()));
                        binList.add(bizReceiptStocktakingBinDTO);
                    }
                });
                if (UtilCollection.isNotEmpty(binList)) {
                    bizReceiptStockTakingItemDTO.setBinList(binList);
                }
            }
            itemList.add(bizReceiptStockTakingItemDTO);
        });
        /* ********* 盘点单行项目及物料明细放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(itemList));
    }

    /**
     * 查询交易盘点表创建数据结构
     * 仓位库存列表 按照 仓库 存储类型 仓位 物料分组的物料信息
     *
     * @in ctx 入参 {@link BizReceiptStocktakingSearchStockBinPO :"查询条件对象"}
     * @out ctx 出参 {@link MultiResultVO<> ("itemList":"仓位库存列表")}
     */
    public void queryStockBinListFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingSearchStockBinPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 入参校验
        if (UtilNumber.isEmpty(po.getStocktakingDocHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptStocktakingDocHead docHead = bizReceiptStocktakingDocHeadDataWrap.getById(po.getStocktakingDocHeadId());
        if (UtilObject.isNull(docHead)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilObject.isEmpty(po.getTradeDateStart()) || UtilObject.isEmpty(po.getTradeDateEnd())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Map<StocktakingItemKey, BizReceiptStocktakingPlanItemDTO> itemDTOMap = new HashMap<>();

        // 处理查询条件 批次编码取批次id, 仓位编码取仓位id
        List<Long> paramBatchIdList = new ArrayList<>();
        List<Long> paramBinIdList = new ArrayList<>();
        Collection<Long> paramMatIdList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(po.getBatchCodeList())) {
            List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getDTOListByCodeList(po.getBatchCodeList());
            paramBatchIdList = batchInfoDTOList.stream().map(BizBatchInfoDTO::getId).collect(Collectors.toList());
        }
        if (UtilCollection.isNotEmpty(po.getBinCodeList())) {
            List<DicWhStorageBin> storageBinList = dicWhStorageBinDataWrap.list(new QueryWrapper<DicWhStorageBin>().lambda()
                    .in(DicWhStorageBin::getBinCode, po.getBinCodeList()));
            paramBinIdList = storageBinList.stream().map(DicWhStorageBin::getId).collect(Collectors.toList());
        }
        if (UtilCollection.isNotEmpty(po.getMatCodeList())) {
            paramMatIdList = dictionaryService.getMatIdListByMatCodeList(po.getMatCodeList());
        }

        Date beginDate = UtilLocalDateTime.getStartTime(po.getTradeDateStart());
        Date endDate = UtilLocalDateTime.getEndTime(po.getTradeDateEnd());
        WmsQueryWrapper<StockInsDocBin> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(po.getWhId()), StockInsDocBin::getWhId, StockInsDocBin.class, po.getWhId())
                .in(UtilCollection.isNotEmpty(po.getWhIdList()), StockInsDocBin::getWhId, StockInsDocBin.class, po.getWhIdList())
                .in(UtilCollection.isNotEmpty(po.getLocationIdList()), StockInsDocBin::getLocationId, StockInsDocBin.class, po.getLocationIdList())
                .in(UtilCollection.isNotEmpty(po.getTypeIdList()), StockInsDocBin::getTypeId, StockInsDocBin.class, po.getTypeIdList())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), StockInsDocBin::getMatId, StockInsDocBin.class, dictionaryService.getMatIdByMatCode(po.getMatCode()))
                .in(UtilCollection.isNotEmpty(paramMatIdList), StockInsDocBin::getMatId, StockInsDocBin.class, paramMatIdList)
                .in(UtilCollection.isNotEmpty(paramBatchIdList), StockInsDocBin::getBatchId, StockInsDocBin.class, paramBatchIdList)
                .in(UtilCollection.isNotEmpty(paramBinIdList), StockInsDocBin::getBinId, StockInsDocBin.class, paramBinIdList)
                .between(true, StockInsDocBin::getCreateTime, StockInsDocBin.class, beginDate, endDate)
                .lt(true, StockInsDocBin::getCreateTime, StockInsDocBin.class, docHead.getPostTime()); // 只查凭证表日期之前的库存凭证
        List<StockInsDocBinDTO> stockInsDocBinList = stockCommonService.getStockInsDocBinListByWrapper(wrapper, true);

        // 对于出库业务，由于计算的是出库行项目上的凭证时间，因此需要根据出库单查找到对应凭证，并将此类INS凭证合并到stockInsDocBinList的列表中去重
        List<StockInsDocBinDTO> outputRelatedStockInsDocBinList = stockCommonService.getRelatedStockBinDocByOutputDocDate(wrapper, true);
        Set<Long> ids = stockInsDocBinList.stream().map(StockInsDocBinDTO::getId).collect(Collectors.toSet());
        List<StockInsDocBinDTO> collect = outputRelatedStockInsDocBinList.stream().filter(e -> !ids.contains(e.getId())).collect(Collectors.toList());
        stockInsDocBinList.addAll(collect);

        // 为避免上下架撤销导致的计数不准确问题，需要过滤掉已删除的作业单产生的凭证
        Set<Long> taskReceiptItemIds = stockInsDocBinList.stream().filter(doc ->
                EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue().equals(doc.getPreReceiptType())
                        || EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue().equals(doc.getPreReceiptType())
        ).map(StockInsDocBinDTO::getPreReceiptItemId).collect(Collectors.toSet());
        List<BizReceiptTaskItem> validTaskItems = taskReceiptItemIds.size() > 0 ? bizReceiptTaskItemDataWrap.listByIds(taskReceiptItemIds) : null;
        if (UtilCollection.isNotEmpty(validTaskItems)) {
            Set<Long> validTaskItemIds = validTaskItems.stream().map(BizReceiptTaskItem::getId).collect(Collectors.toSet());
            Set<Long> romoveIds = stockInsDocBinList.stream().filter(doc ->
                    (EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue().equals(doc.getPreReceiptType())
                            || EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue().equals(doc.getPreReceiptType()))
                            &&
                            !validTaskItemIds.contains(doc.getPreReceiptItemId())
            ).map(StockInsDocBinDTO::getId).collect(Collectors.toSet());
            stockInsDocBinList = stockInsDocBinList.stream().filter(doc -> !romoveIds.contains(doc.getId())).collect(Collectors.toList());
        }

        /* ******* 对仓位凭证按照 入、出、转储入、转储出、仓位整理入、仓位重整理出，进行分组 ******* */

        // 入库数量
        List<StockInsDocBinDTO> inputStockBinDocList = stockInsDocBinList.stream().filter(doc ->
            (
                /*
                 * 满足以下条件计入 => 入库数量
                 * 1. 前序单据是上架作业单，借贷标识为S
                 * 2. 参考单据为 成套验收入[106]、验收入库[214]、其他入库[213]、领料退[312]、退转库[321]、废旧物资入库[219]、暂存入[815]、成套暂存入[8151]、退旧入[332]、闲置物资入[9104]
                 * 或 冲销标识为1时，参考单据类型为 成套领料出[108]、领料出[414]、其他出[416]、采购退冲销[413]、报废出[415]、暂存出[817]、成套暂存出[8171]、退旧出[333]、闲置物资出[9102]
                 */
                doc.getPreReceiptType().equals(EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue())
                &&
                doc.getDebitCredit().equals(Const.DEBIT_S_ADD)
                &&
                (
                    (
                        // 正向入库业务
                        doc.getIsWriteOff().equals(EnumRealYn.FALSE.getIntValue())
                        &&
                        (
                            doc.getReferReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_INPUT_OTHER.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_WASTE_MATERIALS_INPUT.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_INPUT.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue())
                            || doc.getReferReceiptType().equals(EnumReceiptType.LEISURE_INPUT.getValue())
                        )
                    )
                    ||
                    (
                        // 出库逆向冲销业务
                        doc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())
                        &&
                        (
                            doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_OUTPUT.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_OUTPUT.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_RETURN_OLD_OUTPUT.getValue())
                            || doc.getPreReceiptType().equals(EnumReceiptType.LEISURE_OUTPUT.getValue())
                        )
                    )
                )
            )
        ).collect(Collectors.toList());

        // 出库数量来源处理
        List<StockInsDocBinDTO> outputStockBinDocList = stockInsDocBinList.stream().filter(doc ->
            /*
             * 满足以下条件计入 => 出库数量
             * 1. 前序单据是下架作业单，借贷标识为H
             * 2. 参考单据为 成套领料出[108]、领料出[414]、其他出[416]、采购退冲销[413]、报废出[415]、暂存出[422]、废旧出[817]、暂存出[817]、成套暂存出[8171]、退旧出[333]、闲置物资出[9102]
             * 或 冲销标识为1时，参考单据类型为 成套验收入[106]、验收入库[214]、其他入库[213]、领料退[312]、退转库[321]、暂存入[815]、成套暂存入[8151]、退旧入[332]、闲置物资入[9104]
             */
            doc.getPreReceiptType().equals(EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue())
            &&
            doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT)
            &&
            (
                (
                    // 出库正向业务
                    doc.getIsWriteOff().equals(EnumRealYn.FALSE.getIntValue())
                    &&
                    (
                        doc.getReferReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_OUTPUT.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_OUTPUT.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.STOCK_RETURN_OLD_OUTPUT.getValue())
                        || doc.getReferReceiptType().equals(EnumReceiptType.LEISURE_OUTPUT.getValue())
                    )
                )
                ||
                (
                    //入库逆向业务
                    doc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())
                    &&
                    (
                        doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_INPUT_OTHER.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_RETURN_OLD_INPUT.getValue())
                        || doc.getPreReceiptType().equals(EnumReceiptType.LEISURE_INPUT.getValue())
                    )
                )
            )
        ).collect(Collectors.toList());

        // 出库凭证特殊处理， 过滤掉尚未完成出库过账的出库凭证，只计算出库单已完成过账的
        List<Long> alreadyPostOutputStockBinHeadIds = stockInsDocBinList.stream().filter(doc ->
            doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_OTHER.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_SCRAP.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_OUTPUT_WASTE_MAT.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_TEMP_MAT_OUTPUT.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_OUTPUT.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.STOCK_RETURN_OLD_OUTPUT.getValue())
                || doc.getPreReceiptType().equals(EnumReceiptType.LEISURE_OUTPUT.getValue())
        ).map(StockInsDocBinDTO::getPreReceiptHeadId).collect(Collectors.toList());
        outputStockBinDocList = outputStockBinDocList.stream().filter(doc -> alreadyPostOutputStockBinHeadIds.contains(doc.getReferReceiptHeadId())).collect(Collectors.toList());


        // 转入数量来源处理
        List<StockInsDocBinDTO> transportInStockBinDocList = stockInsDocBinList.stream().filter(doc ->
            /*
             * 满足以下条件计入 => 转入数量
             * 1. 前序单据为上架作业单，借贷标识S
             * 2. 参考单据类型 转储单[511]
             */
            doc.getPreReceiptType().equals(EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue())
            &&
            doc.getDebitCredit().equals(Const.DEBIT_S_ADD)
            &&
            doc.getReferReceiptType().equals(EnumReceiptType.STOCK_TRANSPORT.getValue())
        ).collect(Collectors.toList());

        // 转出数量来源处理
        List<StockInsDocBinDTO> transportOutStockBinDocList = stockInsDocBinList.stream().filter(doc ->
                /*
                 * 满足以下条件计入 => 转出数量
                 * 1. 前序单据为下架作业单，借贷标识H
                 * 2. 参考单据类型 转储单[511]
                 */
                doc.getPreReceiptType().equals(EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue())
                &&
                doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT)
                &&
                doc.getReferReceiptType().equals(EnumReceiptType.STOCK_TRANSPORT.getValue())
        ).collect(Collectors.toList());

        // 仓位整理转入数量来源处理
        List<StockInsDocBinDTO> arrangeInStockBinDocList = stockInsDocBinList.stream().filter(doc ->
                /*
                 * 满足以下条件计入 => 仓位整理转入数量
                 * 1. 前序单据为 仓位整理单[630]、成套仓位整理[130]，借贷标识S
                 * 2. 前序单据为 报废冻结[410]、过期冻结[428]、成套报废冻结[4101]、成套报废解冻[4102]，借贷标识S
                 */
                (doc.getPreReceiptType().equals(EnumReceiptType.STOCK_BIN_ARRANGE.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_BIN_ARRANGE.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.STOCK_FREEZE_SCRAP.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.STOCK_FREEZE_EXPIRE.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_UNFREEZE_SCRAP.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
        ).collect(Collectors.toList());

        // 仓位整理转入数量来源处理
        List<StockInsDocBinDTO> arrangeOutStockBinDocList = stockInsDocBinList.stream().filter(doc ->
                /*
                 * 满足以下条件计入 => 仓位整理转出数量
                 * 1. 前序单据为 仓位整理单[630]、成套仓位整理[130]，借贷标识H
                 * 2. 前序单据为 报废冻结[410]、过期冻结[428]、成套报废冻结[4101]、成套报废解冻[4102]，借贷标识H
                 */
                (doc.getPreReceiptType().equals(EnumReceiptType.STOCK_BIN_ARRANGE.getValue())
                        && doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_BIN_ARRANGE.getValue())
                        && doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.STOCK_FREEZE_SCRAP.getValue())
                        && doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.STOCK_FREEZE_EXPIRE.getValue())
                        && doc.getDebitCredit().equals(Const.DEBIT_S_ADD))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_FREEZE_SCRAP.getValue())
                        && doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT))
                ||
                (doc.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_UNFREEZE_SCRAP.getValue())
                        && doc.getDebitCredit().equals(Const.CREDIT_H_SUBTRACT))
        ).collect(Collectors.toList());

        /* ****** 根据凭证分组结果，整理组装盘点表创建所需行项目结构 ****** */

        // 遍历入库凭证，组装盘点表行项目及行项目明细
        for (StockInsDocBinDTO stockBinDoc : inputStockBinDocList) {
            StocktakingItemKey itemKey = new StocktakingItemKey()
                    .setWhId(stockBinDoc.getWhId())
                    .setTypeId(stockBinDoc.getTypeId())
                    .setBinId(stockBinDoc.getBinId())
                    .setBatchId(stockBinDoc.getBatchId());

            if (!itemDTOMap.containsKey(itemKey)) {
                // 不存在同仓位同物料的情况下，插入入库数量
                BizReceiptStocktakingPlanItemDTO itemDTO = new BizReceiptStocktakingPlanItemDTO();
                itemDTO.setWhId(stockBinDoc.getWhId());
                itemDTO.setTypeId(stockBinDoc.getTypeId());
                itemDTO.setBinId(stockBinDoc.getBinId());

                BizReceiptStocktakingPlanBinDTO stocktakingBinDTO = new BizReceiptStocktakingPlanBinDTO();
                stocktakingBinDTO.setMatId(stockBinDoc.getMatId());
                stocktakingBinDTO.setBatchId(stockBinDoc.getBatchId());
                stocktakingBinDTO.setFtyId(stockBinDoc.getFtyId());
                stocktakingBinDTO.setLocationId(stockBinDoc.getLocationId());
                stocktakingBinDTO.setWhId(stockBinDoc.getWhId());
                stocktakingBinDTO.setTypeId(stockBinDoc.getTypeId());
                stocktakingBinDTO.setBinId(stockBinDoc.getBinId());
                stocktakingBinDTO.setUnitId(stockBinDoc.getUnitId());
                stocktakingBinDTO.setInputQty(stockBinDoc.getActualQty());

                StocktakingInputReferReceipt inputReferReceipt = new StocktakingInputReferReceipt();
                // Note：设置关联的单据类型，正向入库业务仓位库存凭证来源是作业单，使用referReceiptType字段取参考单据
                // 逆向出库业务现有冲销逻辑不产生作业单直接过账，因此需取前序单据类型preReceiptType
                inputReferReceipt.setReceiptType(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptType() : stockBinDoc.getReferReceiptType());
                inputReferReceipt.setReceiptHeadId(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptHeadId() : stockBinDoc.getReferReceiptHeadId());
                // 数量取标准计量单位的实际移动数量，不使用订单数量
                inputReferReceipt.setQty(stockBinDoc.getActualQty());
                stocktakingBinDTO.setInputReferReceiptList(Stream.of(inputReferReceipt).collect(Collectors.toList()));
                itemDTO.setBinList(Stream.of(stocktakingBinDTO).collect(Collectors.toList()));
                itemDTOMap.put(itemKey, itemDTO);
            } else {
                // 已存在同仓位同物料的情况下，追加入库数量
                BizReceiptStocktakingPlanItemDTO itemDTO = itemDTOMap.get(itemKey);
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StocktakingBinKey binKey = UtilBean.newInstance(stocktakingBinDTO, StocktakingBinKey.class);
                    StocktakingBinKey binKeyFromDoc = UtilBean.newInstance(stockBinDoc, StocktakingBinKey.class);
                    if (binKey.equals(binKeyFromDoc)) {
                        // 设置数量
                        if (stocktakingBinDTO.getInputQty() == null) {
                            stocktakingBinDTO.setInputQty(BigDecimal.ZERO);
                        }
                        stocktakingBinDTO.setInputQty(stocktakingBinDTO.getInputQty().add(stockBinDoc.getActualQty()));
                        // 设置关联单据
                        StocktakingInputReferReceipt inputReferReceipt = new StocktakingInputReferReceipt();
                        inputReferReceipt.setReceiptType(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptType() : stockBinDoc.getReferReceiptType());
                        inputReferReceipt.setReceiptHeadId(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptHeadId() : stockBinDoc.getReferReceiptHeadId());
                        inputReferReceipt.setQty(BigDecimal.ZERO);
                        // 已经存在关联单据，对数量累加
                        if (UtilCollection.isNotEmpty(stocktakingBinDTO.getInputReferReceiptList())
                                && stocktakingBinDTO.getInputReferReceiptList().contains(inputReferReceipt)) {
                            inputReferReceipt.setQty(inputReferReceipt.getQty().add(stockBinDoc.getActualQty()));
                        } else {
                            if (stocktakingBinDTO.getInputReferReceiptList() == null) {
                                stocktakingBinDTO.setInputReferReceiptList(new ArrayList<>());
                            }
                            inputReferReceipt.setQty(stockBinDoc.getActualQty());
                            stocktakingBinDTO.getInputReferReceiptList().add(inputReferReceipt);
                        }
                        itemDTOMap.put(itemKey, itemDTO);
                    }
                }
            }
        }

        // 遍历出库凭证，组装盘点表行项目及行项目明细
        for (StockInsDocBinDTO stockBinDoc : outputStockBinDocList) {
            StocktakingItemKey itemKey = new StocktakingItemKey()
                    .setWhId(stockBinDoc.getWhId())
                    .setTypeId(stockBinDoc.getTypeId())
                    .setBinId(stockBinDoc.getBinId())
                    .setBatchId(stockBinDoc.getBatchId());

            if (!itemDTOMap.containsKey(itemKey)) {
                // 不存在同仓位同物料的情况下，插入出库数量
                BizReceiptStocktakingPlanItemDTO itemDTO = new BizReceiptStocktakingPlanItemDTO();
                itemDTO.setWhId(stockBinDoc.getWhId());
                itemDTO.setTypeId(stockBinDoc.getTypeId());
                itemDTO.setBinId(stockBinDoc.getBinId());

                BizReceiptStocktakingPlanBinDTO stocktakingBinDTO = new BizReceiptStocktakingPlanBinDTO();
                stocktakingBinDTO.setMatId(stockBinDoc.getMatId());
                stocktakingBinDTO.setBatchId(stockBinDoc.getBatchId());
                stocktakingBinDTO.setFtyId(stockBinDoc.getFtyId());
                stocktakingBinDTO.setLocationId(stockBinDoc.getLocationId());
                stocktakingBinDTO.setWhId(stockBinDoc.getWhId());
                stocktakingBinDTO.setTypeId(stockBinDoc.getTypeId());
                stocktakingBinDTO.setBinId(stockBinDoc.getBinId());
                stocktakingBinDTO.setUnitId(stockBinDoc.getUnitId());
                stocktakingBinDTO.setOutputQty(stockBinDoc.getActualQty());

                StocktakingOutputReferReceipt outputReferReceipt = new StocktakingOutputReferReceipt();
                // Note：设置关联的单据类型，与入库类似，正向出库业务仓位库存凭证来源是作业单，使用referReceiptType字段取参考单据
                // 逆向入库业务现有冲销逻辑不产生作业单直接过账，因此需取前序单据类型preReceiptType
                outputReferReceipt.setReceiptType(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptType() : stockBinDoc.getReferReceiptType());
                outputReferReceipt.setReceiptHeadId(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptHeadId() : stockBinDoc.getReferReceiptHeadId());
                // 数量取标准计量单位的实际移动数量，不使用订单数量
                outputReferReceipt.setQty(stockBinDoc.getActualQty());
                stocktakingBinDTO.setOutputReferReceiptList(Stream.of(outputReferReceipt).collect(Collectors.toList()));
                itemDTO.setBinList(Stream.of(stocktakingBinDTO).collect(Collectors.toList()));
                itemDTOMap.put(itemKey, itemDTO);
            } else {
                // 已存在同仓位同物料的情况下，追加出库数量
                BizReceiptStocktakingPlanItemDTO itemDTO = itemDTOMap.get(itemKey);
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StocktakingBinKey binKey = UtilBean.newInstance(stocktakingBinDTO, StocktakingBinKey.class);
                    StocktakingBinKey binKeyFromDoc = UtilBean.newInstance(stockBinDoc, StocktakingBinKey.class);
                    if (binKey.equals(binKeyFromDoc)) {
                        // 设置数量
                        if (stocktakingBinDTO.getOutputQty() == null) {
                            stocktakingBinDTO.setOutputQty(BigDecimal.ZERO);
                        }
                        stocktakingBinDTO.setOutputQty(stocktakingBinDTO.getOutputQty().add(stockBinDoc.getActualQty()));
                        // 设置关联单据
                        StocktakingOutputReferReceipt outputReferReceipt = new StocktakingOutputReferReceipt();
                        outputReferReceipt.setReceiptType(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptType() : stockBinDoc.getReferReceiptType());
                        outputReferReceipt.setReceiptHeadId(stockBinDoc.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue()) ? stockBinDoc.getPreReceiptHeadId() : stockBinDoc.getReferReceiptHeadId());
                        outputReferReceipt.setQty(BigDecimal.ZERO);
                        // 已经存在关联单据，对数量累加
                        if (UtilCollection.isNotEmpty(stocktakingBinDTO.getOutputReferReceiptList())
                                &&stocktakingBinDTO.getOutputReferReceiptList().contains(outputReferReceipt)) {
                            outputReferReceipt.setQty(outputReferReceipt.getQty().add(stockBinDoc.getActualQty()));
                        } else {
                            if (stocktakingBinDTO.getOutputReferReceiptList() == null) {
                                stocktakingBinDTO.setOutputReferReceiptList(new ArrayList<>());
                            }
                            outputReferReceipt.setQty(stockBinDoc.getActualQty());
                            stocktakingBinDTO.getOutputReferReceiptList().add(outputReferReceipt);
                        }
                        itemDTOMap.put(itemKey, itemDTO);
                    }
                }
            }
        }

        // 遍历转储入凭证，组装盘点表行项目及行项目明细
        for (StockInsDocBinDTO stockBinDoc : transportInStockBinDocList) {
            StocktakingItemKey itemKey = new StocktakingItemKey()
                    .setWhId(stockBinDoc.getWhId())
                    .setTypeId(stockBinDoc.getTypeId())
                    .setBinId(stockBinDoc.getBinId())
                    .setBatchId(stockBinDoc.getBatchId());

            if (!itemDTOMap.containsKey(itemKey)) {
                // 不存在同仓位同物料的情况下，插入转入数量
                BizReceiptStocktakingPlanItemDTO itemDTO = new BizReceiptStocktakingPlanItemDTO();
                itemDTO.setWhId(stockBinDoc.getWhId());
                itemDTO.setTypeId(stockBinDoc.getTypeId());
                itemDTO.setBinId(stockBinDoc.getBinId());

                BizReceiptStocktakingPlanBinDTO stocktakingBinDTO = new BizReceiptStocktakingPlanBinDTO();
                stocktakingBinDTO.setMatId(stockBinDoc.getMatId());
                stocktakingBinDTO.setBatchId(stockBinDoc.getBatchId());
                stocktakingBinDTO.setFtyId(stockBinDoc.getFtyId());
                stocktakingBinDTO.setLocationId(stockBinDoc.getLocationId());
                stocktakingBinDTO.setWhId(stockBinDoc.getWhId());
                stocktakingBinDTO.setTypeId(stockBinDoc.getTypeId());
                stocktakingBinDTO.setBinId(stockBinDoc.getBinId());
                stocktakingBinDTO.setUnitId(stockBinDoc.getUnitId());
                stocktakingBinDTO.setTransportInputQty(stockBinDoc.getActualQty());

                StocktakingTransportReferReceipt transportInReferReceipt = new StocktakingTransportReferReceipt();
                transportInReferReceipt.setReceiptType(stockBinDoc.getReferReceiptType());
                transportInReferReceipt.setReceiptHeadId(stockBinDoc.getReferReceiptHeadId());
                // 数量取标准计量单位的实际移动数量，不使用订单数量
                transportInReferReceipt.setQty(stockBinDoc.getActualQty());
                stocktakingBinDTO.setTransportReferReceiptList(Stream.of(transportInReferReceipt).collect(Collectors.toList()));
                itemDTO.setBinList(Stream.of(stocktakingBinDTO).collect(Collectors.toList()));
                itemDTOMap.put(itemKey, itemDTO);
            } else {
                // 已存在同仓位同物料的情况下，累加转入数量
                BizReceiptStocktakingPlanItemDTO itemDTO = itemDTOMap.get(itemKey);
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StocktakingBinKey binKey = UtilBean.newInstance(stocktakingBinDTO, StocktakingBinKey.class);
                    StocktakingBinKey binKeyFromDoc = UtilBean.newInstance(stockBinDoc, StocktakingBinKey.class);
                    if (binKey.equals(binKeyFromDoc)) {
                        // 累加数量
                        if (stocktakingBinDTO.getTransportInputQty() == null) {
                            stocktakingBinDTO.setTransportInputQty(BigDecimal.ZERO);
                        }
                        stocktakingBinDTO.setTransportInputQty(stocktakingBinDTO.getTransportInputQty().add(stockBinDoc.getActualQty()));
                        // 设置关联单据
                        StocktakingTransportReferReceipt transportReferReceipt = new StocktakingTransportReferReceipt();
                        transportReferReceipt.setReceiptType(stockBinDoc.getReferReceiptType());
                        transportReferReceipt.setReceiptHeadId(stockBinDoc.getReferReceiptHeadId());
                        transportReferReceipt.setQty(BigDecimal.ZERO);
                        // 已经存在关联单据，对数量累加
                        if (UtilCollection.isNotEmpty(stocktakingBinDTO.getTransportReferReceiptList())
                                && stocktakingBinDTO.getTransportReferReceiptList().contains(transportReferReceipt)) {
                            transportReferReceipt.setQty(transportReferReceipt.getQty().add(stockBinDoc.getActualQty()));
                        } else {
                            if (stocktakingBinDTO.getTransportReferReceiptList() == null) {
                                stocktakingBinDTO.setTransportReferReceiptList(new ArrayList<>());
                            }
                            transportReferReceipt.setQty(stockBinDoc.getActualQty());
                            stocktakingBinDTO.getTransportReferReceiptList().add(transportReferReceipt);
                        }
                        itemDTOMap.put(itemKey, itemDTO);
                    }
                }
            }
        }

        // 遍历转储出凭证，组装盘点表行项目及行项目明细
        for (StockInsDocBinDTO stockBinDoc : transportOutStockBinDocList) {
            StocktakingItemKey itemKey = new StocktakingItemKey()
                    .setWhId(stockBinDoc.getWhId())
                    .setTypeId(stockBinDoc.getTypeId())
                    .setBinId(stockBinDoc.getBinId())
                    .setBatchId(stockBinDoc.getBatchId());

            if (!itemDTOMap.containsKey(itemKey)) {
                // 不存在同仓位同物料的情况下，插入转出数量
                BizReceiptStocktakingPlanItemDTO itemDTO = new BizReceiptStocktakingPlanItemDTO();
                itemDTO.setWhId(stockBinDoc.getWhId());
                itemDTO.setTypeId(stockBinDoc.getTypeId());
                itemDTO.setBinId(stockBinDoc.getBinId());

                BizReceiptStocktakingPlanBinDTO stocktakingBinDTO = new BizReceiptStocktakingPlanBinDTO();
                stocktakingBinDTO.setMatId(stockBinDoc.getMatId());
                stocktakingBinDTO.setBatchId(stockBinDoc.getBatchId());
                stocktakingBinDTO.setFtyId(stockBinDoc.getFtyId());
                stocktakingBinDTO.setLocationId(stockBinDoc.getLocationId());
                stocktakingBinDTO.setWhId(stockBinDoc.getWhId());
                stocktakingBinDTO.setTypeId(stockBinDoc.getTypeId());
                stocktakingBinDTO.setBinId(stockBinDoc.getBinId());
                stocktakingBinDTO.setUnitId(stockBinDoc.getUnitId());
                stocktakingBinDTO.setTransportOutputQty(stockBinDoc.getActualQty());

                StocktakingTransportReferReceipt transportOutReferReceipt = new StocktakingTransportReferReceipt();
                transportOutReferReceipt.setReceiptType(stockBinDoc.getReferReceiptType());
                transportOutReferReceipt.setReceiptHeadId(stockBinDoc.getReferReceiptHeadId());
                // 数量取标准计量单位的实际移动数量，不使用订单数量
                transportOutReferReceipt.setQty(stockBinDoc.getActualQty());
                stocktakingBinDTO.setTransportReferReceiptList(Stream.of(transportOutReferReceipt).collect(Collectors.toList()));
                itemDTO.setBinList(Stream.of(stocktakingBinDTO).collect(Collectors.toList()));
                itemDTOMap.put(itemKey, itemDTO);
            } else {
                // 已存在同仓位同物料的情况下，累加转出数量
                BizReceiptStocktakingPlanItemDTO itemDTO = itemDTOMap.get(itemKey);
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StocktakingBinKey binKey = UtilBean.newInstance(stocktakingBinDTO, StocktakingBinKey.class);
                    StocktakingBinKey binKeyFromDoc = UtilBean.newInstance(stockBinDoc, StocktakingBinKey.class);
                    if (binKey.equals(binKeyFromDoc)) {
                        // 累加数量
                        if (stocktakingBinDTO.getTransportOutputQty() == null) {
                            stocktakingBinDTO.setTransportOutputQty(BigDecimal.ZERO);
                        }
                        stocktakingBinDTO.setTransportOutputQty(stocktakingBinDTO.getTransportOutputQty().add(stockBinDoc.getActualQty()));
                        // 设置关联单据
                        StocktakingTransportReferReceipt transportReferReceipt = new StocktakingTransportReferReceipt();
                        transportReferReceipt.setReceiptType(stockBinDoc.getReferReceiptType());
                        transportReferReceipt.setReceiptHeadId(stockBinDoc.getReferReceiptHeadId());
                        transportReferReceipt.setQty(BigDecimal.ZERO);
                        // 已经存在关联单据，对数量累加
                        if (UtilCollection.isNotEmpty(stocktakingBinDTO.getTransportReferReceiptList())
                                && stocktakingBinDTO.getTransportReferReceiptList().contains(transportReferReceipt)) {
                            transportReferReceipt.setQty(transportReferReceipt.getQty().add(stockBinDoc.getActualQty()));
                        } else {
                            if (stocktakingBinDTO.getTransportReferReceiptList() == null) {
                                stocktakingBinDTO.setTransportReferReceiptList(new ArrayList<>());
                            }
                            transportReferReceipt.setQty(stockBinDoc.getActualQty());
                            stocktakingBinDTO.getTransportReferReceiptList().add(transportReferReceipt);
                        }
                        itemDTOMap.put(itemKey, itemDTO);
                    }
                }
            }
        }

        // 遍历仓位整理转入凭证，组装盘点表行项目及行项目明细
        for (StockInsDocBinDTO stockBinDoc : arrangeInStockBinDocList) {
            StocktakingItemKey itemKey = new StocktakingItemKey()
                    .setWhId(stockBinDoc.getWhId())
                    .setTypeId(stockBinDoc.getTypeId())
                    .setBinId(stockBinDoc.getBinId())
                    .setBatchId(stockBinDoc.getBatchId());

            if (!itemDTOMap.containsKey(itemKey)) {
                // 不存在同仓位同物料的情况下，插入仓位整理转入数量
                BizReceiptStocktakingPlanItemDTO itemDTO = new BizReceiptStocktakingPlanItemDTO();
                itemDTO.setWhId(stockBinDoc.getWhId());
                itemDTO.setTypeId(stockBinDoc.getTypeId());
                itemDTO.setBinId(stockBinDoc.getBinId());

                BizReceiptStocktakingPlanBinDTO stocktakingBinDTO = new BizReceiptStocktakingPlanBinDTO();
                stocktakingBinDTO.setMatId(stockBinDoc.getMatId());
                stocktakingBinDTO.setBatchId(stockBinDoc.getBatchId());
                stocktakingBinDTO.setFtyId(stockBinDoc.getFtyId());
                stocktakingBinDTO.setLocationId(stockBinDoc.getLocationId());
                stocktakingBinDTO.setWhId(stockBinDoc.getWhId());
                stocktakingBinDTO.setTypeId(stockBinDoc.getTypeId());
                stocktakingBinDTO.setBinId(stockBinDoc.getBinId());
                stocktakingBinDTO.setUnitId(stockBinDoc.getUnitId());
                stocktakingBinDTO.setBinInputQty(stockBinDoc.getActualQty());

                StocktakingArrangeReferReceipt arrangeInReferReceipt = new StocktakingArrangeReferReceipt();
                arrangeInReferReceipt.setReceiptType(stockBinDoc.getPreReceiptType());
                arrangeInReferReceipt.setReceiptHeadId(stockBinDoc.getPreReceiptHeadId());
                // 数量取标准计量单位的实际移动数量，不使用订单数量
                arrangeInReferReceipt.setQty(stockBinDoc.getActualQty());
                stocktakingBinDTO.setArrangeReferReceiptList(Stream.of(arrangeInReferReceipt).collect(Collectors.toList()));
                itemDTO.setBinList(Stream.of(stocktakingBinDTO).collect(Collectors.toList()));
                itemDTOMap.put(itemKey, itemDTO);
            } else {
                // 已存在同仓位同物料的情况下，累加仓位整理转入数量
                BizReceiptStocktakingPlanItemDTO itemDTO = itemDTOMap.get(itemKey);
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StocktakingBinKey binKey = UtilBean.newInstance(stocktakingBinDTO, StocktakingBinKey.class);
                    StocktakingBinKey binKeyFromDoc = UtilBean.newInstance(stockBinDoc, StocktakingBinKey.class);
                    if (binKey.equals(binKeyFromDoc)) {
                        // 累加数量
                        if (stocktakingBinDTO.getBinInputQty() == null) {
                            stocktakingBinDTO.setBinInputQty(BigDecimal.ZERO);
                        }
                        stocktakingBinDTO.setBinInputQty(stocktakingBinDTO.getBinInputQty().add(stockBinDoc.getActualQty()));
                        // 设置关联单据
                        StocktakingArrangeReferReceipt arrangeInReferReceipt = new StocktakingArrangeReferReceipt();
                        arrangeInReferReceipt.setReceiptType(stockBinDoc.getPreReceiptType());
                        arrangeInReferReceipt.setReceiptHeadId(stockBinDoc.getPreReceiptHeadId());
                        arrangeInReferReceipt.setQty(BigDecimal.ZERO);
                        // 已经存在关联单据，对数量累加
                        if (UtilCollection.isNotEmpty(stocktakingBinDTO.getArrangeReferReceiptList())
                            && stocktakingBinDTO.getArrangeReferReceiptList().contains(arrangeInReferReceipt)) {
                            arrangeInReferReceipt.setQty(arrangeInReferReceipt.getQty().add(stockBinDoc.getActualQty()));
                        } else {
                            if (stocktakingBinDTO.getArrangeReferReceiptList() == null) {
                                stocktakingBinDTO.setArrangeReferReceiptList(new ArrayList<>());
                            }
                            arrangeInReferReceipt.setQty(stockBinDoc.getActualQty());
                            stocktakingBinDTO.getArrangeReferReceiptList().add(arrangeInReferReceipt);
                        }
                        itemDTOMap.put(itemKey, itemDTO);
                    }
                }
            }
        }

        // 遍历仓位整理转出凭证，组装盘点表行项目及行项目明细
        for (StockInsDocBinDTO stockBinDoc : arrangeOutStockBinDocList) {
            StocktakingItemKey itemKey = new StocktakingItemKey()
                    .setWhId(stockBinDoc.getWhId())
                    .setTypeId(stockBinDoc.getTypeId())
                    .setBinId(stockBinDoc.getBinId())
                    .setBatchId(stockBinDoc.getBatchId());

            if (!itemDTOMap.containsKey(itemKey)) {
                // 不存在同仓位同物料的情况下，插入仓位整理转入数量
                BizReceiptStocktakingPlanItemDTO itemDTO = new BizReceiptStocktakingPlanItemDTO();
                itemDTO.setWhId(stockBinDoc.getWhId());
                itemDTO.setTypeId(stockBinDoc.getTypeId());
                itemDTO.setBinId(stockBinDoc.getBinId());

                BizReceiptStocktakingPlanBinDTO stocktakingBinDTO = new BizReceiptStocktakingPlanBinDTO();
                stocktakingBinDTO.setMatId(stockBinDoc.getMatId());
                stocktakingBinDTO.setBatchId(stockBinDoc.getBatchId());
                stocktakingBinDTO.setFtyId(stockBinDoc.getFtyId());
                stocktakingBinDTO.setLocationId(stockBinDoc.getLocationId());
                stocktakingBinDTO.setWhId(stockBinDoc.getWhId());
                stocktakingBinDTO.setTypeId(stockBinDoc.getTypeId());
                stocktakingBinDTO.setBinId(stockBinDoc.getBinId());
                stocktakingBinDTO.setUnitId(stockBinDoc.getUnitId());
                stocktakingBinDTO.setBinOutputQty(stockBinDoc.getActualQty());

                StocktakingArrangeReferReceipt arrangeInReferReceipt = new StocktakingArrangeReferReceipt();
                arrangeInReferReceipt.setReceiptType(stockBinDoc.getPreReceiptType());
                arrangeInReferReceipt.setReceiptHeadId(stockBinDoc.getPreReceiptHeadId());
                // 数量取标准计量单位的实际移动数量，不使用订单数量
                arrangeInReferReceipt.setQty(stockBinDoc.getActualQty());
                stocktakingBinDTO.setArrangeReferReceiptList(Stream.of(arrangeInReferReceipt).collect(Collectors.toList()));
                itemDTO.setBinList(Stream.of(stocktakingBinDTO).collect(Collectors.toList()));
                itemDTOMap.put(itemKey, itemDTO);
            } else {
                // 已存在同仓位同物料的情况下，累加仓位整理转入数量
                BizReceiptStocktakingPlanItemDTO itemDTO = itemDTOMap.get(itemKey);
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StocktakingBinKey binKey = UtilBean.newInstance(stocktakingBinDTO, StocktakingBinKey.class);
                    StocktakingBinKey binKeyFromDoc = UtilBean.newInstance(stockBinDoc, StocktakingBinKey.class);
                    if (binKey.equals(binKeyFromDoc)) {
                        // 累加数量
                        if (stocktakingBinDTO.getBinOutputQty() == null) {
                            stocktakingBinDTO.setBinOutputQty(BigDecimal.ZERO);
                        }
                        stocktakingBinDTO.setBinOutputQty(stocktakingBinDTO.getBinOutputQty().add(stockBinDoc.getActualQty()));
                        // 设置关联单据
                        StocktakingArrangeReferReceipt arrangeOutReferReceipt = new StocktakingArrangeReferReceipt();
                        arrangeOutReferReceipt.setReceiptType(stockBinDoc.getReferReceiptType());
                        arrangeOutReferReceipt.setReceiptHeadId(stockBinDoc.getReferReceiptHeadId());
                        arrangeOutReferReceipt.setQty(BigDecimal.ZERO);
                        // 已经存在关联单据，对数量累加
                        if (UtilCollection.isNotEmpty(stocktakingBinDTO.getArrangeReferReceiptList())
                                && stocktakingBinDTO.getArrangeReferReceiptList().contains(arrangeOutReferReceipt)) {
                            arrangeOutReferReceipt.setQty(arrangeOutReferReceipt.getQty().add(stockBinDoc.getActualQty()));
                        } else {
                            if (stocktakingBinDTO.getArrangeReferReceiptList() == null) {
                                stocktakingBinDTO.setArrangeReferReceiptList(new ArrayList<>());
                            }
                            arrangeOutReferReceipt.setQty(stockBinDoc.getActualQty());
                            stocktakingBinDTO.getArrangeReferReceiptList().add(arrangeOutReferReceipt);
                        }
                        itemDTOMap.put(itemKey, itemDTO);
                    }
                }
            }
        }

        List<BizReceiptStocktakingPlanItemDTO> dtoList = new ArrayList<>(itemDTOMap.values());

        /* ***** 填充当前仓位物料的库存数据 ***** */
        List<StockBinKey> keyList = new ArrayList<>();
        for (StockInsDocBinDTO insDocBinDTO : stockInsDocBinList) {
            keyList.add(new StockBinKey(
                    insDocBinDTO.getMatId(),
                    insDocBinDTO.getBatchId(),
                    insDocBinDTO.getFtyId(),
                    insDocBinDTO.getLocationId(),
                    insDocBinDTO.getWhId(),
                    insDocBinDTO.getTypeId(),
                    insDocBinDTO.getBinId(),
                    insDocBinDTO.getCellId())
            );
        }
        if (UtilCollection.isNotEmpty(keyList)) {
            // 由库存移动凭证得到的仓位库存
            List<StockBinDTO> stockBinList = stockCommonService.getStocktakingDocBinByStockBinKeyList(keyList, po.getStocktakingDocHeadId());
            Map<String, StockBinDTO> stockBinDTOMap = stockBinList.stream()
                    .collect(Collectors.toMap(e ->
                                    e.getMatId() + "#" + e.getBatchId() + "#" + e.getFtyId() + "#" + e.getLocationId()
                                            + "#" + e.getWhId() + "#" + e.getTypeId() + "#" + e.getBinId()
                            , Function.identity(), (k1, k2) -> k1));
            for (BizReceiptStocktakingPlanItemDTO itemDTO : dtoList) {
                // 组装仓位库存唯一键，作为仓位库存查询条件
                for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                    StockBinDTO stockBinDTO = stockBinDTOMap.get(
                            stocktakingBinDTO.getMatId() + "#"
                                    + stocktakingBinDTO.getBatchId() + "#"
                                    + stocktakingBinDTO.getFtyId() + "#"
                                    + stocktakingBinDTO.getLocationId() + "#"
                                    + stocktakingBinDTO.getWhId() + "#"
                                    + stocktakingBinDTO.getTypeId() + "#"
                                    + stocktakingBinDTO.getBinId()
                    );
                    if (stockBinDTO != null) {
                        stocktakingBinDTO.setStockQty(stockBinDTO.getQty());
                    }
                }
            }
        }

        for (BizReceiptStocktakingPlanItemDTO dto : dtoList) {
            for (BizReceiptStocktakingPlanBinDTO binDTO : dto.getBinList()) {
                // 填充物料组id
                DicMaterialDTO materialDTO = dictionaryService.getMatCacheById(binDTO.getMatId());
                binDTO.setMatGroupId(materialDTO.getMatGroupId());
                // 填充盘点凭证批次id
                WmsLambdaQueryWrapper<BizStocktakingDocBatchInfo> queryWrapper = new WmsQueryWrapper<BizStocktakingDocBatchInfo>()
                        .lambda()
                        .eq(BizStocktakingDocBatchInfo::getBatchId, binDTO.getBatchId())
                        .eq(BizStocktakingDocBatchInfo::getIsDelete, EnumRealYn.FALSE.getIntValue())
                        .eq(BizStocktakingDocBatchInfo::getHeadId, po.getStocktakingDocHeadId());
                BizStocktakingDocBatchInfo docBatchInfo = bizStocktakingDocBatchInfoDataWrap.getOne(queryWrapper, false);
                if (UtilObject.isNotNull(docBatchInfo)) {
                    binDTO.setBatchId(docBatchInfo.getId());
                    binDTO.setBatchOriginalId(binDTO.getBatchId());
                    binDTO.setBizBatchInfoDTO(UtilBean.newInstance(docBatchInfo, BizBatchInfoDTO.class));
                    binDTO.setBatchCode(docBatchInfo.getBatchCode());
                    binDTO.setIsSingle(docBatchInfo.getIsSingle());
                    binDTO.setOutFtyCode(docBatchInfo.getOutFtyCode());
                    binDTO.setFormatCode(docBatchInfo.getFormatCode());
                    binDTO.setSpecStock(docBatchInfo.getSpecStock());
                    binDTO.setSpecStockCode(docBatchInfo.getSpecStockCode());
                }
            }
            dataFillService.fillAttrBField(dto.getBinList(), BizReceiptStocktakingPlanBinDTO::getBatchId);
        }
        dataFillService.fillAttrBField(dtoList, BizReceiptStocktakingPlanItemDTO::getBinList);

        // 按照仓位排序
        dtoList = dtoList.stream().sorted(Comparator.comparing(BizReceiptStocktakingPlanItemDTO::getBinCode)).collect(Collectors.toList());

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dtoList));
    }

    /**
     * 专项盘点和计划盘点，查询盘点表创建结构
     *
     * @param ctx
     */
    public void queryStockBinListForSpecialStocktaking(BizContext ctx) {
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingSearchStockBinPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setIsUnitized(Boolean.TRUE);
        /* ********* 查询仓位库存 非交易盘点 ******** */
        List<BizReceiptStocktakingPlanBinDTO> dtoList = new ArrayList<>();
        if (UtilObject.isNull(po.getTradeDateStart()) && UtilObject.isNull(po.getTradeDateEnd())) {
            dtoList = bizReceiptStocktakingBinDataWrap.selectStockBinPlanList(po);
        }

        /* ********* 过滤默认存储类型 2023-06-03 专项盘点和计划盘点根据需求要求不再过滤默认存储类型 ******** */
        List<BizReceiptStocktakingPlanBinDTO> bizReceiptStocktakingBinDTOList = dtoList;
//        for (BizReceiptStocktakingPlanBinDTO binDTO : dtoList) {
//            if (!UtilConst.getInstance().getDefaultStorageTypeCodeSet().contains(binDTO.getTypeCode())) {
//                bizReceiptStocktakingBinDTOList.add(binDTO);
//            }
//        }
        /* ********* 仓位库存按仓库id 存储类型id 仓位id分组 ******** */
        Map<String, List<BizReceiptStocktakingPlanBinDTO>> stockBinMap = bizReceiptStocktakingBinDTOList.stream().collect(Collectors.groupingBy(e -> getGroupKey(e)));
        /* ********* 设置盘点单行项目集合 ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemList = new ArrayList();
        stockBinMap.values().stream().forEach(p -> {
            /* ********* 设置盘点单行项目对象 ******** */
            BizReceiptStocktakingPlanItemDTO bizReceiptStockTakingItemDTO = UtilBean.newInstance(p.get(0), BizReceiptStocktakingPlanItemDTO.class);
            /* ********* 如果仓位下物料批次id为空 证明是空仓位，空仓位不返回给前端 ******** */
            if (UtilNumber.isNotEmpty(p.get(0).getMatId()) && UtilNumber.isNotEmpty(p.get(0).getBatchId())) {
                /* ********* 设置仓位库存分组 按物料批次分组 ******** */
                Map<Long, List<BizReceiptStocktakingPlanBinDTO>> binMap = p.stream().collect(Collectors.groupingBy(BizReceiptStocktakingPlanBinDTO::getBatchId));
                /* ********* 设置盘点单物料明细集合 ******** */
                List<BizReceiptStocktakingPlanBinDTO> binList = new ArrayList();
                binMap.values().forEach(q -> {
                    /* ********* 设置盘点单物料明细对象 ******** */
                    BizReceiptStocktakingPlanBinDTO bizReceiptStocktakingBinDTO = q.get(0);
                    if (EnumRealYn.FALSE.getIntValue().equals(bizReceiptStocktakingBinDTO.getFreezeOutput())
                        && EnumRealYn.FALSE.getIntValue().equals(bizReceiptStocktakingBinDTO.getFreezeInput())) {
                        /* ********* 相同物料数量汇总 ******** */
                        BigDecimal stockQty = q.stream().map(BizReceiptStocktakingPlanBinDTO::getStockQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        bizReceiptStocktakingBinDTO.setStockQty(stockQty);
                        /* ********* 计算物料库存金额 ******** */
                        // bizReceiptStocktakingBinDTO.setStockPrice(stockQty.multiply(q.get(0).getMoveAvgPrice()));
                        binList.add(bizReceiptStocktakingBinDTO);
                    }
                });
                if (UtilCollection.isNotEmpty(binList)) {
                    bizReceiptStockTakingItemDTO.setBinList(binList);
                }
            }
            itemList.add(bizReceiptStockTakingItemDTO);
        });
        /* ********* 数据组装完成后，新增校验，冻结的仓位(组装后无物料的仓位)不返回给前端 ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemListReturn = new ArrayList();
        for (BizReceiptStocktakingPlanItemDTO item : itemList) {
            if (item.getBinList() != null && item.getBinList().size() > 0) {
                itemListReturn.add(item);
//                for (BizReceiptStocktakingPlanBinDTO binDTO : item.getBinList()) {
//                    DicMaterialDTO materialDTO = dictionaryService.getMatCacheById(binDTO.getMatId());
//                    binDTO.setMatGroupId(materialDTO.getMatGroupId());
//                }
            }
        }
        //dataFillService.fillAttr(itemList);
        itemListReturn = itemListReturn.stream().sorted(Comparator.comparing(BizReceiptStocktakingPlanItemDTO::getBinCode)).collect(Collectors.toList());
        /* ********* 盘点单行项目及物料明细放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(itemListReturn));
    }

    /**
     * 获取Key键 Key:仓库id#存储类型id#仓位id
     */
    private static String getGroupKey(BizReceiptStocktakingPlanBinDTO bizReceiptStocktakingBinDTO) {
        return bizReceiptStocktakingBinDTO.getWhId() + "#" + bizReceiptStocktakingBinDTO.getTypeId() + "#" + bizReceiptStocktakingBinDTO.getBinId();
    }

    /**
     * 保存盘点单
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO :"盘点单传输对象"}
     * @out ctx 出参 {@link String ("receiptCode":"盘点单号")}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInfo(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 判断是否为新增盘点单 ******** */
        String receiptCode = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            /* ********* 删除现有盘点单 ******** */
            this.remove(headDTO.getId());
        } else {
            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE.getValue());
        }
        /* ********* 保存盘点单抬头表 ******** */
        headDTO.setId(null);
        headDTO.setReceiptCode(receiptCode);
        if (EnumRealYn.FALSE.getIntValue().equals(headDTO.getIsElectronicScale())) {
            headDTO.setIsReplay(EnumStocktakingReplayType.STOCKTAKING_REPLAY_NO.getValue());
        } else {
            headDTO.setIsReplay(EnumStocktakingReplayType.STOCKTAKING_REPLAY_NO.getValue());
        }
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        headDTO.setCreateUserId(cUser.getId());
        headDTO.setModifyUserId(cUser.getId());
        bizReceiptStocktakingHeadDataWrap.saveDto(headDTO);
        Long headId = headDTO.getId();
        /* ********* 保存盘点单行项目表 ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemList = headDTO.getItemList();
        AtomicReference<Integer> rid = new AtomicReference<>(1);
        itemList.forEach(item -> {
            item.setId(null).setHeadId(headId).setRid(rid.toString())
                .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()).setCreateUserId(cUser.getId())
                .setModifyUserId(cUser.getId());
            rid.getAndSet(rid.get() + 1);
        });
        bizReceiptStocktakingItemDataWrap.saveBatchDto(itemList);
        /* ********* 保存盘点单物料明细表 ******** */
        List<BizReceiptStocktakingPlanBinDTO> binList = new ArrayList<>();
        itemList.forEach(item -> {
            Long itemId = item.getId();
            AtomicReference<Integer> bid = new AtomicReference<>(1);
            item.getBinList().stream().forEach(bin -> {
                bin.setId(null).setHeadId(headId).setItemId(itemId).setBid(bid.toString()).setBatchId((long)0)
                    .setCellId((long)0).setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .setModifyStatus(EnumRealYn.FALSE.getIntValue()).setCreateUserId(cUser.getId())
                    .setModifyUserId(cUser.getId());
                if (EnumRealYn.FALSE.getIntValue().equals(headDTO.getIsElectronicScale())) {
                    bin.setDiffType(EnumStocktakingDiffType.INITIALIZATION.getValue());
                }
                bid.getAndSet(bid.get() + 1);
            });
            binList.addAll(item.getBinList());
        });
        bizReceiptStocktakingBinDataWrap.saveBatchDto(binList);

        // 保存盘点关联单据
        List<BizReceiptStocktakingRelatedReceipt> relReceiptList = new ArrayList<>();
        binList.forEach(bin -> {
            bin.getInputReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.INPUT.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
            bin.getOutputReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.OUTPUT.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
            bin.getTransportReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.TRANSPORT.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
            bin.getArrangeReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.BIN_ARRANGE.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
        });
        bizReceiptStocktakingRelatedReceiptDataWrap.saveBatch(relReceiptList);
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        log.debug("盘点单{}保存成功", receiptCode);
    }




    public void saveBinRemarkAndReviewStatus(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 遍历盘点单的行项目，将复盘状态和备注信息更新
        List<BizReceiptStocktakingPlanBinDTO> binList = new ArrayList<>();
        for (BizReceiptStocktakingPlanItemDTO stocktakingItemDTO : headDTO.getItemList()) {
            if (UtilCollection.isNotEmpty(stocktakingItemDTO.getBinList())) {
                binList.addAll(stocktakingItemDTO.getBinList());
            }
        }
        if (UtilCollection.isEmpty(binList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        //当id数量大于200时，拼接过多Id报错。采用分断传参方式，最终进行结果拼接，进行修改。
        if (binList.size()>200){
            int size = binList.size();
            //计算需要分成几组
            int count = (size + 200 - 1) /200;
            //结果集
            List<List<BizReceiptStocktakingPlanBinDTO>> newList = new ArrayList<>();
            //结果集添加
            for (int i = 0; i < count; i++) {
                int start = i * 200;
                int end = Math.min(start + 200, size);
                List<BizReceiptStocktakingPlanBinDTO> subList = binList.subList(start, end);
                newList.add(subList);
            }
            //分批查询
            List<BizReceiptStocktakingPlanBinDTO> tempBinList = new ArrayList<>();
            for (List<BizReceiptStocktakingPlanBinDTO> subList : newList) {
                bizReceiptStocktakingBinDataWrap.updateBinRemarkAndReviewStatusPlan(subList);
                tempBinList.addAll(subList);
            }
            binList = tempBinList;
        } else {
            bizReceiptStocktakingBinDataWrap.updateBinRemarkAndReviewStatusPlan(binList);
        }
        BizReceiptStocktakingHead head=UtilBean.newInstance(headDTO, BizReceiptStocktakingHead.class);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO, head);
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
        log.info("盘点单{}保存成功", headDTO.getReceiptCode());
    }

    /**
     * 【首盘可过账模式】保存盘点单
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO :"盘点单传输对象"}
     * @out ctx 出参 {@link String ("receiptCode":"盘点单号")}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInfoFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 判断是否为新增盘点单 ******** */
        String receiptCode = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            /* ********* 删除现有盘点单 ******** */
            this.remove(headDTO.getId());
        } else {
            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE.getValue());
        }
        /* ********* 保存盘点单抬头表 ******** */
        headDTO.setId(null);
        headDTO.setReceiptCode(receiptCode);
        headDTO.setIsReplay(EnumStocktakingReplayType.STOCKTAKING_REPLAY_NO.getValue());
        headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        headDTO.setCreateUserId(cUser.getId());
        headDTO.setModifyUserId(cUser.getId());
        bizReceiptStocktakingHeadDataWrap.saveDto(headDTO);
        Long headId = headDTO.getId();
        if(headDTO.getOriginReceiptHeadId()==null){
            headDTO.setFirstReceiptHeadId(headId);
            headDTO.setLastReceiptHeadId(headId);
            bizReceiptStocktakingHeadDataWrap.updateDtoById(headDTO);
        }
        /* ********* 保存盘点人信息 ******** */
        List<BizReceiptStocktakingUserDTO> userList = headDTO.getStocktakingUserList();
        userList.forEach(user -> {
            user.setId(null).setHeadId(headId).setCreateUserId(cUser.getId());
        });
        bizReceiptStocktakingUserDataWrap.saveBatchDto(userList);
        /* ********* 保存盘点单行项目表 ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemList = headDTO.getItemList();
        AtomicReference<Integer> rid = new AtomicReference<>(1);
        itemList.forEach(item -> {
            item.setId(null).setHeadId(headId).setRid(rid.toString())
                .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()).setCreateUserId(cUser.getId())
                .setIsAppointMat(headDTO.getIsAppointMat()).setModifyUserId(100L);
            rid.getAndSet(rid.get() + 1);
        });
        /* ********* 保存盘点单物料明细表 ******** */
        bizReceiptStocktakingItemDataWrap.saveBatchDto(itemList);
        List<BizReceiptStocktakingPlanBinDTO> binList = new ArrayList<>();

        // 获取一下bin级别涉及的所有批次信息，在设置价格时使用批次信息上的属性查缓存 ---start ↓
        Set<Long> batchInfoIds = new HashSet<>();
        itemList.forEach(item -> {
            item.getBinList().forEach(bin -> {
                batchInfoIds.add(bin.getBatchId());
            });
        });
        List<BizBatchInfoDTO> batchInfoList = batchInfoService.getBatchInfoList(new ArrayList<>(batchInfoIds));
        Map<Long, BizBatchInfoDTO> batchInfoMap = batchInfoList.stream().collect(Collectors.toMap(BizBatchInfoDTO::getId, Function.identity()));
        // 获取一下bin级别涉及的所有批次信息，在设置价格时使用批次信息上的属性查缓存 ---end ↑

        for (BizReceiptStocktakingPlanItemDTO item : itemList) {
            Long itemId = item.getId();
            AtomicReference<Integer> bid = new AtomicReference<>(1);
            for (BizReceiptStocktakingPlanBinDTO bin : item.getBinList()) {
                bin.setId(null)
                    .setHeadId(headId).setItemId(itemId).setBid(bid.toString()).setCellId((long)0)
                    .setDiffType(EnumStocktakingDiffType.INITIALIZATION.getValue())
                    .setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                    .setModifyStatus(EnumRealYn.FALSE.getIntValue()).setCreateUserId(cUser.getId())
                    .setModifyUserId(cUser.getId()).setIsCount(EnumRealYn.TRUE.getIntValue());

                // 通过项目工厂物料的缓存查询对应项目工厂物料的移动平均价，再对Bin表对象赋值和计算总价
                String specStockStr = batchInfoMap.get(bin.getBatchId()) != null ? batchInfoMap.get(bin.getBatchId()).getSpecStock() : "";
                String specStockCodeStr = batchInfoMap.get(bin.getBatchId()) != null ? batchInfoMap.get(bin.getBatchId()).getSpecStockCode() : "";
                if (UtilString.isNullOrEmpty(specStockStr) || UtilString.isNullOrEmpty(specStockCodeStr)) {
                    DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(bin.getMatId(), bin.getFtyId());
                    bin.setPrice(materialFactoryDTO != null ? materialFactoryDTO.getMoveAvgPrice() : BigDecimal.ZERO);
                } else {
                    String materialFactoryWbsKey = bin.getMatId() + "##" + bin.getFtyId() + "##" + specStockStr + "##" + specStockCodeStr;
                    DicMaterialFacotryWbs dicMaterialFacotryWbs = dictionaryService.getMaterialFacotryWbsByKey(materialFactoryWbsKey);
                    bin.setPrice(dicMaterialFacotryWbs != null ? dicMaterialFacotryWbs.getMoveAvgPrice() : BigDecimal.ZERO);
                }

                bin.setTotalPrice(bin.getStockQty().multiply(bin.getPrice()==null?BigDecimal.ZERO:bin.getPrice()));
                bid.getAndSet(bid.get() + 1);
            }
            binList.addAll(item.getBinList());
        }

        bizReceiptStocktakingBinDataWrap.saveBatchDto(binList);
        if(UtilNumber.isEmpty(headDTO.getOriginReceiptHeadId())){ //首盘更新库存盘点物料明细表 首盘盘点单bin_id 最后一次复盘盘点单bin_id
            BizReceiptStocktakingHead head=UtilBean.newInstance(headDTO, BizReceiptStocktakingHead.class);
            bizReceiptStocktakingHeadDataWrap.updateStocktakingBinIdByFirst(head);
        }
        // 保存盘点关联单据
        List<BizReceiptStocktakingRelatedReceipt> relReceiptList = new ArrayList<>();
        binList.forEach(bin -> {
            bin.getInputReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.INPUT.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
            bin.getOutputReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.OUTPUT.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
            bin.getTransportReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.TRANSPORT.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
            bin.getArrangeReferReceiptList().forEach(relReceipt -> {
                BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                relatedReceiptEntity.setStocktakingBinId(bin.getId());
                relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.BIN_ARRANGE.getValue());
                relReceiptList.add(relatedReceiptEntity);
            });
        });
        bizReceiptStocktakingRelatedReceiptDataWrap.saveBatch(relReceiptList);

        // 保存标签与盘点单关系
        this.saveLabelRel(binList, headDTO);
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        log.debug("盘点单{}保存成功", receiptCode);
    }

    public void saveLabelRel(List<BizReceiptStocktakingPlanBinDTO> binList,BizReceiptStocktakingPlanHeadDTO headDTO){
        List<StockBinDTO> dtoList = UtilCollection.toList(binList, StockBinDTO.class);
        List<BizStockdocLabelData> labelDataList = labelDataDataWrap.selectByStockBinList(dtoList);
        List<BizStockdocLabelReceiptRel> relList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(labelDataList)) {
            Map<String,
                List<BizStockdocLabelData>> labelDataMap = labelDataList.stream()
                .collect(Collectors.groupingBy(e -> e.getMatId() + Const.HYPHEN + e.getFtyId() + Const.HYPHEN + e.getLocationId()
                    + Const.HYPHEN + e.getBatchId() + Const.HYPHEN + e.getWhId() + Const.HYPHEN + e.getTypeId() + Const.HYPHEN + e.getBinId()
                    + Const.HYPHEN + e.getCellId()));
            for (BizReceiptStocktakingPlanBinDTO stockBinDTO : binList) {
                String key = stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN + stockBinDTO.getLocationId()
                    + Const.HYPHEN + stockBinDTO.getBatchId() + Const.HYPHEN + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId()
                    + Const.HYPHEN + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                List<BizStockdocLabelData> innerLabelDataList = labelDataMap.get(key);
                if(UtilCollection.isNotEmpty(innerLabelDataList)){
                    for(BizStockdocLabelData labelData:innerLabelDataList){
                        BizStockdocLabelReceiptRel rel = new BizStockdocLabelReceiptRel();
                        rel.setHeadId(headDTO.getStocktakingDocHeadId());
                        rel.setLabelId(labelData.getId());
                        rel.setReceiptType(headDTO.getReceiptType());
                        rel.setReceiptHeadId(stockBinDTO.getHeadId());
                        rel.setReceiptItemId(stockBinDTO.getItemId());
                        rel.setReceiptBinId(stockBinDTO.getId());
                        relList.add(rel);
                    }
                }
            }
            if(UtilCollection.isNotEmpty(relList)){
                labelReceiptRelService.saveBatch(relList);
            }
        }
    }


    /**
     * 物理删除
     *
     * @param id 抬头表id
     */
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id) {
        /* ********* 删除盘点单抬头头表 ******** */
        bizReceiptStocktakingHeadDataWrap.physicalDeleteById(id);
        /* ********* 删除盘点单行项目表 ******** */
        UpdateWrapper<BizReceiptStocktakingItem> itemWrapper = new UpdateWrapper<>();
        itemWrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, id);
        bizReceiptStocktakingItemDataWrap.physicalDelete(itemWrapper);
        /* ********* 删除盘点单物料明细表 ******** */
        UpdateWrapper<BizReceiptStocktakingBin> binWrapper = new UpdateWrapper<>();
        binWrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, id);
        bizReceiptStocktakingBinDataWrap.physicalDelete(binWrapper);
        /* ********* 删除盘点单关联单据 ******** */
        UpdateWrapper<BizReceiptStocktakingRelatedReceipt> relateReceiptWrapper = new UpdateWrapper<>();
        relateReceiptWrapper.lambda().eq(BizReceiptStocktakingRelatedReceipt::getStocktakingHeadId, id);
        bizReceiptStocktakingRelatedReceiptDataWrap.physicalDelete(relateReceiptWrapper);
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "headDTO"："要保存附件的盘点单对象"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBizReceiptAttachment(BizContext ctx) {
        /* ********* 从上下文获取盘点单对象 ******** */
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        /* ********* 保存盘点单附件 ******** */
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            EnumReceiptType.UNITIZED_PLAN_STOCK_TAKE.getValue(), user.getId());
        log.debug("保存盘点单附件成功!");
    }

    /**
     * 提交盘点单效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "po"："盘点单传输对象"}
     */
    public void checkBySubmit(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 盘点人不能为空 ******** */
        if (UtilNumber.isEmpty(po.getStocktakingUserId())
            && EnumRealYn.FALSE.getIntValue().equals(po.getIsElectronicScale())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCKTAKING_STOCKTAKING_USER_CANNOT_BE_NULL);
        }
    }

    /**
     * 【首盘可过账模式】提交盘点单效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "po"："盘点单传输对象"}
     */
    public void checkBySubmitFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 盘点人不能为空 ******** */
        if (po.getStocktakingUserList() == null || po.getStocktakingUserList().size() == 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCKTAKING_STOCKTAKING_USER_CANNOT_BE_NULL);
        }
    }

    /**
     * 提交盘点单
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "po"："盘点单传输对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    public void submitInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 保存盘点单 ******** */
        this.saveInfo(ctx);
        /* ********* 更新盘点单状态为待计数 ******** */
        if (EnumRealYn.FALSE.getIntValue().equals(po.getIsElectronicScale())) {
            this.updateReceiptStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue());
        } else {
            this.updateReceiptStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }

    /**
     * 【首盘可过账】提交盘点单
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "po"："盘点单传输对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    public void submitInfoFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 保存盘点单 ******** */
        this.saveInfoFirstCanBePosted(ctx);
        /* ********* 更新状态及单人盘点时认领 ******** */
        this.updateFirstCanBePosted(ctx);
    }

    /**
     * 【首盘可过账，单人盘点时】更新盘点单状态
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "po"："盘点单传输对象"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getId();
        List<BizReceiptStocktakingUserDTO> userList = po.getStocktakingUserList();
        /* ********* 如果是单人盘点，保存单据后自动认领，单据状态改为待计数状态45，行项目表字段modify_user_id存放认领人id ******** */
        if (userList.size() == 1) {
            /* ********* 更新盘点单抬头表 ******** */
            UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
            headWrapper.lambda().eq(BizReceiptStocktakingHead::getId, headId).set(
                BizReceiptStocktakingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue());
            bizReceiptStocktakingHeadDataWrap.update(headWrapper);
            /* ********* 更新盘点单行项目表 ******** */
            UpdateWrapper<BizReceiptStocktakingItem> itemWrapper = new UpdateWrapper();
            itemWrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, headId)
                .set(BizReceiptStocktakingItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
                .set(BizReceiptStocktakingItem::getModifyUserId, userList.get(0).getStocktakingUserId());
            bizReceiptStocktakingItemDataWrap.update(itemWrapper);
            /* ********* 更新盘点单物料明细表 ******** */
            UpdateWrapper<BizReceiptStocktakingBin> binWrapper = new UpdateWrapper();
            binWrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, headId)
                .set(BizReceiptStocktakingBin::getMatStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
                .set(BizReceiptStocktakingBin::getModifyUserId, userList.get(0).getStocktakingUserId());
            bizReceiptStocktakingBinDataWrap.update(binWrapper);
        } else {
            /* ********* 如果是多人盘点，更新盘点单状态为待认领状态44 ******** */
            /* ********* 更新盘点单抬头表 ******** */
            UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
            headWrapper.lambda().eq(BizReceiptStocktakingHead::getId, headId)
                .set(BizReceiptStocktakingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue());
            bizReceiptStocktakingHeadDataWrap.update(headWrapper);
            /* ********* 更新盘点单行项目表 ******** */
            UpdateWrapper<BizReceiptStocktakingItem> itemWrapper = new UpdateWrapper();
            itemWrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, headId)
                .set(BizReceiptStocktakingItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue())
                .set(BizReceiptStocktakingItem::getModifyUserId, 100L);
            bizReceiptStocktakingItemDataWrap.update(itemWrapper);
            /* ********* 更新盘点单物料明细表 ******** */
            UpdateWrapper<BizReceiptStocktakingBin> binWrapper = new UpdateWrapper();
            binWrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, headId)
                .set(BizReceiptStocktakingBin::getMatStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue())
                .set(BizReceiptStocktakingBin::getModifyUserId, 100L);
            bizReceiptStocktakingBinDataWrap.update(binWrapper);
        }
    }

    /**
     * 【首盘可过账】仓位是否被认领效验
     *
     * @param ctx 入参上下文 {"id":"行项目表主键"}
     */
    public void checkBinIsGet(BizContext ctx) {
        /* ********* 从上下文能获取抬头表id ******** */
        Long iteamId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptStocktakingItem item = bizReceiptStocktakingItemDataWrap.getById(iteamId);
        if (!item.getModifyUserId().equals(100L)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_IS_GET);
        } else {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_ITEM_VO, item);
        }
    }

    /**
     * 【首盘可过账】多人盘点，认领逻辑
     *
     * @param ctx 入参上下文 {"id":"行项目表主键"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void getBin(BizContext ctx) {
        /* ********* 获取check方法放入上下文行项目信息 ******** */
        BizReceiptStocktakingItem item = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ITEM_VO);
        Long headId = item.getHeadId();
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 更新盘点单行项目表 ******** */
        UpdateWrapper<BizReceiptStocktakingItem> itemWrapper = new UpdateWrapper();
        itemWrapper.lambda().eq(BizReceiptStocktakingItem::getId, item.getId())
            .set(BizReceiptStocktakingItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
            .set(BizReceiptStocktakingItem::getModifyUserId, cUser.getId());
        bizReceiptStocktakingItemDataWrap.update(itemWrapper);
        /* ********* 更新盘点单物料明细表 ******** */
        UpdateWrapper<BizReceiptStocktakingBin> binWrapper = new UpdateWrapper();
        binWrapper.lambda().eq(BizReceiptStocktakingBin::getItemId, item.getId())
            .set(BizReceiptStocktakingBin::getMatStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
            .set(BizReceiptStocktakingBin::getModifyUserId, cUser.getId());
        bizReceiptStocktakingBinDataWrap.update(binWrapper);
        /* ********* 查看当前仓位对应的单据下所有的仓位是否都认领完成 ******** */
        QueryWrapper<BizReceiptStocktakingItem> wrapper = new QueryWrapper();
        wrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, headId).eq(BizReceiptStocktakingItem::getIsDelete, 0);
        List<BizReceiptStocktakingItem> iteamList = bizReceiptStocktakingItemDataWrap.list(wrapper);
        boolean updateFlag = true;
        /* ********* 单据下有任何一条修改人为空极为有未被认领的仓库 ******** */
        for (BizReceiptStocktakingItem iteam : iteamList) {
            if (iteam.getModifyUserId().equals(100L)) {
                updateFlag = false;
            }
        }
        if (updateFlag) {
            /* ********* 更新盘点单抬头表 ******** */
            UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
            headWrapper.lambda().eq(BizReceiptStocktakingHead::getId, headId).set(
                BizReceiptStocktakingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue());
            bizReceiptStocktakingHeadDataWrap.update(headWrapper);
        }
    }

    /**
     * 更新盘点单状态
     *
     * @param headId 抬头表id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReceiptStatus(Long headId, Integer receiptStatus) {
        /* ********* 更新盘点单抬头表 ******** */
        bizReceiptStocktakingHeadDataWrap.update(new UpdateWrapper<BizReceiptStocktakingHead>() {

            {
                lambda().eq(BizReceiptStocktakingHead::getId, headId);
                lambda().set(BizReceiptStocktakingHead::getReceiptStatus, receiptStatus);
            }
        });
        /* ********* 更新盘点单行项目表 ******** */
        bizReceiptStocktakingItemDataWrap.update(new UpdateWrapper<BizReceiptStocktakingItem>() {

            {
                lambda().eq(BizReceiptStocktakingItem::getHeadId, headId);
                lambda().set(BizReceiptStocktakingItem::getItemStatus, receiptStatus);
            }
        });
        /* ********* 更新盘点单物料明细表 ******** */
        bizReceiptStocktakingBinDataWrap.update(new UpdateWrapper<BizReceiptStocktakingBin>() {

            {
                lambda().eq(BizReceiptStocktakingBin::getHeadId, headId);
                lambda().set(BizReceiptStocktakingBin::getMatStatus, receiptStatus);
            }
        });
    }


    /**
     * 保存盘点人签名
     * @param ctx
     */
    public void saveStocktakingUser(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        QueryWrapper<BizReceiptStocktakingSignUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptStocktakingSignUser::getHeadId, headDTO.getId());
        List<BizReceiptStocktakingSignUser> entityList = bizReceiptStocktakingSignUserDataWrap.list(queryWrapper);
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(entityList)) {
            List<Long> idList = entityList.stream().map(BizReceiptStocktakingSignUser::getId).collect(Collectors.toList());
            bizReceiptStocktakingSignUserDataWrap.multiPhysicalDeleteByIdList(idList);
        }
        if(UtilCollection.isNotEmpty(headDTO.getStocktakingSignUserList())) {
            /* ********************** user处理开始 *************************/
            AtomicInteger rids = new AtomicInteger(1);
            for (BizReceiptStocktakingSignUserDTO userDTO : headDTO.getStocktakingSignUserList()) {
                userDTO.setId(null);
                userDTO.setHeadId(headDTO.getId());
                userDTO.setCreateUserId(cUser.getId());
                userDTO.setCreateTime(UtilDate.getNow());
                userDTO.setReceiptType(headDTO.getReceiptType());
            }
            bizReceiptStocktakingSignUserDataWrap.saveBatchDto(headDTO.getStocktakingSignUserList());
            /* ********************** user处理开始 *************************/
        }
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

    /**
     * 提交盘点人签名
     * @param ctx
     */
    public void submitStocktakingUser(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 保存盘点人签名
        this.saveStocktakingUser(ctx);
        // 更新盘点单已计数
        this.updateHeadReceiptStatus(po.getId(),EnumReceiptStatus.RECEIPT_STATUS_COUNTED.getValue());
    }

    /**
     * 更新盘点单状态
     *
     * @param headId 抬头表id
     */
    public void updateHeadReceiptStatus(Long headId, Integer receiptStatus) {

        /* ********* 更新盘点单抬头表 ******** */
        bizReceiptStocktakingHeadDataWrap.update(new UpdateWrapper<BizReceiptStocktakingHead>() {

            {
                lambda().eq(BizReceiptStocktakingHead::getId, headId);
                lambda().set(BizReceiptStocktakingHead::getReceiptStatus, receiptStatus);
            }
        });
    }

    /**
     * 冻结仓位
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanHeadDTO : "po"："盘点单传输对象"}
     */
    public void freezeStorageBin(BizContext ctx) {
        /* ********* 从上下文获取盘点单传输对象 ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 取出所有仓位id ******** */
        Set<Long> binIdSet = po.getItemList().stream().map(item -> item.getBinId()).collect(Collectors.toSet());
        /* ********* 修改仓位状态为已冻结 ******** */
        if (UtilCollection.isNotEmpty(binIdSet)) {
            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DicWhStorageBin::getId, binIdSet)
                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.INVENTORY_FREEZE.getValue())
                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.INVENTORY_FREEZE.getValue());
            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
                /* ********* 刷新缓存 ******** */
                editCacheService.refreshBinCache(new ArrayList<>(binIdSet));
            }
        }
    }

    /**
     * 【首盘可过账】首盘保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptFirstTree(BizContext ctx) {
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptStocktakingPlanItemDTO> itemDTOList = headDTO.getItemList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptStocktakingPlanItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(EnumReceiptType.UNITIZED_PLAN_STOCK_TAKE.getValue());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.UNITIZED_DOC_STOCK_TAKE.getValue());
            bizCommonReceiptRelation.setPreReceiptHeadId(headDTO.getStocktakingDocHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getUpIteamId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 【首盘可过账】复盘保存单据流
     *
     * @param ctx 上下文对象
     */
    public void saveReceiptOtherTree(BizContext ctx) {
        /* *********上下问获取新单据信息，为单据流使用 ******** */
        BizReceiptStocktakingHead headVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(headVO.getId());
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptStocktakingPlanHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(headDTO);
        List<BizReceiptStocktakingPlanItemDTO> itemDTOList = headDTO.getItemList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptStocktakingPlanItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(EnumReceiptType.UNITIZED_PLAN_STOCK_TAKE.getValue());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.UNITIZED_PLAN_STOCK_TAKE.getValue());
            bizCommonReceiptRelation.setPreReceiptHeadId(headDTO.getUpHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getUpIteamId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link Long : "id"："抬头表id"}
     */
    public void checkDeleteStocktaking(BizContext ctx) {
        /* ********* 从上下文获取抬头表id ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 存在已计数项 不可删除 ******** */
        QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, id).eq(BizReceiptStocktakingBin::getMatStatus,
            EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
        if (!CollectionUtils.isEmpty(bizReceiptStocktakingBinDataWrap.list(queryWrapper))) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXITS_ROW_COUNTED_DELLETE_FAILURE);
        }
    }

    /**
     * 刪除盘点单
     *
     * @in ctx 入参 {@link Long : "headId"："抬头表id"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInfo(BizContext ctx) {
        /* ********* 从上下文获取抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 设置盘点单号 ******** */
        String receiptCode = bizReceiptStocktakingHeadDataWrap.getById(headId).getReceiptCode();
        /* ********* 逻辑删除盘点单抬头头表 ******** */
        bizReceiptStocktakingHeadDataWrap.removeById(headId);
        /* ********* 逻辑删除盘点单行项目表 ******** */
        QueryWrapper<BizReceiptStocktakingItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, headId);
        bizReceiptStocktakingItemDataWrap.remove(itemWrapper);
        /* ********* 获取所有仓位id ******** */
        QueryWrapper<BizReceiptStocktakingBin> binWrapper = new QueryWrapper<>();
        binWrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, headId);
        Set<Long> binIdSet = bizReceiptStocktakingBinDataWrap.list(binWrapper).stream().map(bin -> bin.getBinId())
            .collect(Collectors.toSet());
        /* ********* 修改仓位状态未冻结 ******** */
        if (UtilCollection.isNotEmpty(binIdSet)) {
            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DicWhStorageBin::getId, binIdSet)
                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.NOT_FROZEN.getValue())
                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.NOT_FROZEN.getValue());
            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
                /* ********* 刷新缓存 ******** */
                editCacheService.refreshBinCache(new ArrayList<>(binIdSet));
            }
        }
        /* ********* 逻辑删除盘点单物料明细表 ******** */
        bizReceiptStocktakingBinDataWrap.remove(binWrapper);
        /* ********* 为3.1首盘可过账模式新增删除盘点人明细表begin ******** */
        QueryWrapper<BizReceiptStocktakingUser> queryUserWrapper = new QueryWrapper<>();
        queryUserWrapper.lambda().eq(BizReceiptStocktakingUser::getHeadId, headId);
        List<BizReceiptStocktakingUser> userList = bizReceiptStocktakingUserDataWrap.list(queryUserWrapper);
        if (userList != null && userList.size() > 0) {
            bizReceiptStocktakingUserDataWrap.remove(queryUserWrapper);
        }
        /* ********* 为3.1首盘可过账模式新增删除盘点人明细表end ******** */
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        log.debug("盘点单{}删除成功", receiptCode);
    }

    /**
     * 仓位是否存在效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingSearchStockBinPO : "po"："盘点单添加仓位入参对象"}
     */
    public void checkBinIsExist(BizContext ctx) {
        /* ********* 从上下文获取盘点单添加仓位入参对象 ******** */
        BizReceiptStocktakingSearchStockBinPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 效验仓位是否已经存在 ******** */
        QueryWrapper<BizReceiptStocktakingItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, po.getHeadId());
        Set<Long> binIdSet = bizReceiptStocktakingItemDataWrap.list(queryWrapper).stream().map(item -> item.getBinId())
            .collect(Collectors.toSet());
        if (binIdSet.contains(po.getBinId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_CODE_EXIST);
        }
    }

    /**
     * 【首盘可过账模式】设置盘点单类型
     *
     * @in ctx 入参 {@link BizReceiptStocktakingSearchStockBinPO : "po"："盘点单添加仓位入参对象"}
     */
    public void setReplayFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取盘点单添加仓位入参对象 ******** */
        BizReceiptStocktakingSearchStockBinPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 设置盘点单类型 ******** */
        po.setIsReplay(EnumStocktakingReplayType.STOCKTAKING_REPLAY_YES.getValue());
    }

    /**
     * 插入仓位库存
     *
     * @in ctx 入参 {@link BizReceiptStocktakingAddMatPO : "po"："盘点单添加仓位入参对象"}
     * @in ctx 入参 {@link BizReceiptStocktakingAddMatPO : "po"："盘点单行项目对象"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveStockBinList(BizContext ctx) {
        /* ********* 从上下文获取当前登录人信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点单添加仓位入参对象 ******** */
        BizReceiptStocktakingSearchStockBinPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 从上下文获取盘点单行项目对象 ******** */
        MultiResultVO<BizReceiptStocktakingPlanItemDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 查询盘点单已有行项目列表 ******** */
        QueryWrapper<BizReceiptStocktakingItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptStocktakingItem::getHeadId, po.getHeadId());
        List<BizReceiptStocktakingItem> itemList = bizReceiptStocktakingItemDataWrap.list(queryWrapper);
        /* ********* 设置行项目rid最大值 ******** */
        AtomicReference<Integer> ridMax = new AtomicReference<>(1);
        if (UtilCollection.isNotEmpty(itemList)) {
            ridMax = new AtomicReference<>(Collections
                .max(itemList.stream().map(item -> Integer.parseInt(item.getRid())).collect(Collectors.toList())) + 1);
        }
        /* ********* 装载行项目保存对象 ******** */
        AtomicReference<Integer> finalRidMax = ridMax;
        resultVO.getResultList().forEach(item -> {
            item.setHeadId(po.getHeadId()).setRid(finalRidMax.toString())
                .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue()).setCreateUserId(cUser.getId())
                .setModifyUserId(cUser.getId());
            finalRidMax.getAndSet(finalRidMax.get() + 1);
        });
        bizReceiptStocktakingItemDataWrap.saveBatchDto(resultVO.getResultList());
        /* ********* 装载物料明细保存对象 ******** */
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = new ArrayList<>();
        resultVO.getResultList().forEach(item -> {
            /* ********* 空仓位不添加物料明细 ******** */
            if (UtilCollection.isNotEmpty(item.getBinList())) {
                AtomicReference<Integer> bid = new AtomicReference<>(1);
                item.getBinList().forEach(bin -> {
                    if (po.getIsReplay().equals(EnumStocktakingReplayType.STOCKTAKING_REPLAY_NO.getValue())) {
                        bin.setBatchId((long)0).setCellId((long)0);
                    }
                    bin.setHeadId(po.getHeadId()).setItemId(item.getId()).setBid(bid.toString())
                        .setDiffType(EnumStocktakingDiffType.INITIALIZATION.getValue())
                        .setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
                        .setModifyStatus(EnumRealYn.FALSE.getIntValue()).setCreateUserId(cUser.getId())
                        .setModifyUserId(cUser.getId());
                    bid.getAndSet(bid.get() + 1);
                });
                binDTOList.addAll(item.getBinList());
            }
        });
        bizReceiptStocktakingBinDataWrap.saveBatchDto(binDTOList);
        /* ********* 取出所有仓位id ******** */
        Set<Long> binIdSet = resultVO.getResultList().stream().map(item -> item.getBinId()).collect(Collectors.toSet());
        /* ********* 修改仓位状态为已冻结 ******** */
        if (UtilCollection.isNotEmpty(binIdSet)) {
            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DicWhStorageBin::getId, binIdSet)
                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.INVENTORY_FREEZE.getValue())
                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.INVENTORY_FREEZE.getValue());
            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
                /* ********* 刷新缓存 ******** */
                editCacheService.refreshBinCache(new ArrayList<>(binIdSet));
            }
        }
    }

    /**
     * 【首盘可过账模式】添加物料校验，只有按仓位盘才能添加物料
     *
     * @in ctx 入参 {@link BizReceiptStocktakingBinSavePO : "po"："盘点单物料明细列表传输对象"}
     */
    public void checkCanAddMat(BizContext ctx) {
        /* ********* 从上下文获取盘点单添加物料入参对象 ******** */
        BizReceiptStocktakingAddMatPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long iteamId = po.getIteamId();
        BizReceiptStocktakingItem iteam = bizReceiptStocktakingItemDataWrap.getById(iteamId);
        if (iteam.getIsAppointMat().equals(EnumStocktakingIsAppointMatType.STOCKTAKING_ISAPPOINTMAT_YES.getValue())) {
            // 提示按仓位盘点才可添加物料
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCKTAKING_NO_ADD);
        }
    }

    /**
     * 搜索物料
     *
     * @in ctx 入参 {@link BizReceiptStocktakingAddMatPO : "po"："盘点单添加物料入参对象"}
     * @out ctx 出参 {@link MultiResultVO<> ("dtoList":"物料列表")}
     */
    public void getMatList(BizContext ctx) {
        /* ********* 从上下文获取盘点单添加物料入参对象 ******** */
        BizReceiptStocktakingAddMatPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 查询物料列表 首盘按物料维度查找 复盘按批次维度查找 ******** */
        List<BizReceiptStocktakingBinDTO> dtoList = new ArrayList<>();
        // po.setBatch(true);
        if (po.isBatch()) {
            dtoList = bizReceiptStocktakingBinDataWrap.selectMatListByBatch(po);
        } else {
            dtoList = bizReceiptStocktakingBinDataWrap.selectMatList(po);
        }
        /* ********* 物料列表放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dtoList));
    }

    /**
     * 设置物料图片信息
     *
     * @in ctx 入参 {@link BizResultVO<> ("BizReceiptStocktakingPlanBinDTO":"库存盘点单物料明细列表")}
     * @out ctx 出参 {@link BizResultVO<> ("BizReceiptStocktakingPlanBinDTO":"库存盘点单物料明细列表及物料图片")}
     */
    public void setMatImgByBin(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        MultiResultVO<BizReceiptStocktakingPlanBinDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = resultVO.getResultList();
        if (CollectionUtils.isEmpty(binDTOList)) {
            return;
        }
        /* ********* 设置物料id集合 ******** */
        Set<Long> collect = binDTOList.stream().map(BizReceiptStocktakingPlanBinDTO::getMatId).collect(Collectors.toSet());
        /* ********* 获取物料图片 ******** */
        Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByMatIdList(collect, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        /* ********* 赋值物料图片 ******** */
        binDTOList.forEach(binDTO -> {
            if (UtilNumber.isNotEmpty(binDTO.getMatId()) && !CollectionUtils.isEmpty(imgMap.get(binDTO.getMatId()))) {
                binDTO.setBizBatchImgDTOList(imgMap.get(binDTO.getMatId()));
            }
        });
    }

    /**
     * 盘点计数效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingBinSavePO : "po"："盘点单物料明细列表传输对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    public void checkCountedStocktaking(BizContext ctx) {
        /* ********* 从上下文获取盘点单物料明细列表传输对象 ******** */
        BizReceiptStocktakingPlanBinSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 如果工厂、库存地点、存储类型、仓位下物料存在相同批次 异常提示 ******** */
        Set<String> checkSameSet = new HashSet<>();
        for (BizReceiptStocktakingPlanBinDTO bin : po.getBinList()) {
            if (!checkSameSet.add(bin.getFtyId() + "-" + bin.getLocationId() + "-" + bin.getWhId() + "-"
                + bin.getTypeId() + "-" + bin.getBinId() + "-" + bin.getMatId() + "-" + bin.getBatchId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_SAME_ROW_INFO, bin.getMatId().toString(),
                    bin.getBatchId().toString());
            }
        }
    }

    /**
     * 盘点计数 保存物料和单据状态
     *
     * @in ctx 入参 {@link BizReceiptStocktakingBinSavePO : "po"："盘点单物料明细列表传输对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCount(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点单物料明细列表传输对象 ******** */
        BizReceiptStocktakingPlanBinSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptStocktakingPlanBinDTO> binList = po.getBinList();
        /* ********* 设置物料明细状态为已提交 ******** */
        binList.forEach(bin -> bin.setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue()));
        /* ********* 过滤出新增物料集合 ******** */
        List<BizReceiptStocktakingPlanBinDTO> newBinList =
            binList.stream().filter(bin -> UtilNumber.isEmpty(bin.getId())).collect(Collectors.toList());
        /* ********* 取出原有物料集合 ******** */
        binList.removeAll(newBinList);
        /* ********* 设置物料明细bid最大值 ******** */
        AtomicReference<Integer> bidMax = new AtomicReference<>(
            Collections.max(binList.stream().map(bin -> Integer.parseInt(bin.getBid())).collect(Collectors.toList()))
                + 1);
        /* ********* 填充保存对象 新增物料固定为盘盈 ******** */
        newBinList.forEach(bin -> {
            Long matId = bin.getMatId();
            Long unitId = bin.getUnitId();
            Long batchId = bin.getBatchId();
            BigDecimal qty = bin.getQty();
            Integer diffType = EnumStocktakingDiffType.INVENTORY_PROFIT.getValue();
            if (qty.equals(BigDecimal.ZERO)) {
                diffType = EnumStocktakingDiffType.NO_DIFFERENCE.getValue();
            }
            UtilBean.copy(binList.get(0), bin);
            bin.setId(null).setBid(bidMax.toString()).setBatchId(batchId).setCellId((long)0).setMatId(matId)
                .setUnitId(unitId).setDiffType(diffType).setModifyStatus(EnumRealYn.FALSE.getIntValue())
                .setStockQty(new BigDecimal(0)).setQty(qty).setCreateUserId(cUser.getId())
                .setModifyUserId(cUser.getId())
                .setCountingConfirmTime(new Date())
                .setCountingUserId(cUser.getId());
            bin.setMatStatus(
                bin.getMatStatus() == null ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() : bin.getMatStatus());
            bidMax.getAndSet(bidMax.get() + 1);
        });
        binList
            .forEach(bin -> bin.setDiffType( // 盘点差异 0 初始化 1 无差异 2 盘盈 3 盘亏
                bin.getQty().compareTo(bin.getStockQty()) == 0 ? EnumStocktakingDiffType.NO_DIFFERENCE.getValue()
                    : bin.getQty().compareTo(bin.getStockQty()) > 0
                        ? EnumStocktakingDiffType.INVENTORY_PROFIT.getValue()
                        : EnumStocktakingDiffType.INVENTORY_LOSS.getValue())
                .setSecondaryDiffType(bin.getDiffType()) //复盘盘点差异 0 初始化 1 无差异 2 盘盈 3 盘亏
                .setCountingConfirmTime(new Date())
                .setCountingUserId(cUser.getId())
                .setModifyUserId(cUser.getId()));
        /* ********* 保存/更新物料明细 ******** */
        bizReceiptStocktakingBinDataWrap.saveBatchDto(newBinList);
        bizReceiptStocktakingBinDataWrap.updateBatchDtoById(binList);
        /* ********* 更新行项目状态 ******** */
        updateItemStatus(binList.get(0).getHeadId(), binList.get(0).getItemId(), 1);
        // 更新行项目盘点计数操作人
        this.updateItemCountingUser(binList.get(0).getItemId(), cUser.getId());
        /* ********* 更新单据状态 ******** */
        updateHeadStatus(binList.get(0).getHeadId(), 1);
        /* ********* 设置PDA盘点仓位号 ******** */
        String binCode = dictionaryService.getBinCacheById(binList.get(0).getBinId()).getBinCode();
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, binCode);
    }

    /**
     * 行项目是已完成状态时，将当前行项目的计数人更新
     * @param itemId
     * @param userId
     */
    private void updateItemCountingUser(Long itemId, Long userId) {
        BizReceiptStocktakingItem item = bizReceiptStocktakingItemDataWrap.getById(itemId);
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(item.getItemStatus())) {
            // 行项目是完成时，更新盘点计数人id
            UpdateWrapper<BizReceiptStocktakingItem> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(BizReceiptStocktakingItem::getId, itemId)
                    .set(BizReceiptStocktakingItem::getCountingUserId, userId);
            bizReceiptStocktakingItemDataWrap.update(updateWrapper);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param headId 主表id
     * @param itemId 行项目表id
     * @param type 区分方法调用入口 1 盘点计数 2 盘点过账
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateItemStatus(Long headId, Long itemId, Integer type) {
        /* ********* 查询物料明细列表状态集合 ******** */
        QueryWrapper<BizReceiptStocktakingBin> binWrapper = new QueryWrapper<>();
        binWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingBin::getHeadId, headId)
            .eq(UtilNumber.isNotEmpty(itemId), BizReceiptStocktakingBin::getItemId, itemId);
        /* ********* 过账入口 只查看盘盈/盘亏的物料明细 ******** */
        if (type == 2) {
            List<Integer> diffTypeList = new ArrayList<>();
            diffTypeList.add(EnumStocktakingDiffType.INVENTORY_PROFIT.getValue());
            diffTypeList.add(EnumStocktakingDiffType.INVENTORY_LOSS.getValue());
            binWrapper.lambda().in(BizReceiptStocktakingBin::getDiffType, diffTypeList);
        }
        Set<Integer> binStatusSet = bizReceiptStocktakingBinDataWrap.list(binWrapper).stream()
            .map(bin -> bin.getMatStatus()).collect(Collectors.toSet());
        if (binStatusSet.size() == 1) {
            Integer itemStatus = 0;
            /* ********* 如果均为已提交 更新盘点单行项目表 已完成 ******** */
            if (binStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue())) {
                itemStatus = EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue();
            } else /* ********* 如果均为已记账 更新盘点单行项目表 已记账 ******** */
            if (binStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue())) {
                itemStatus = EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue();
            }
            UpdateWrapper<BizReceiptStocktakingItem> itemWrapper = new UpdateWrapper();
            itemWrapper.lambda().eq(UtilNumber.isNotEmpty(itemId), BizReceiptStocktakingItem::getId, itemId)
                .eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingItem::getHeadId, headId)
                .set(BizReceiptStocktakingItem::getItemStatus, itemStatus);
            bizReceiptStocktakingItemDataWrap.update(itemWrapper);
        }
    }

    /**
     * 更新单据状态
     *
     * @param headId 主表id
     * @param type 区分方法调用入口 1 盘点计数 2 盘点过账
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHeadStatus(Long headId, Integer type) {
        /* ********* 查询行项目列表状态集合 ******** */
        QueryWrapper<BizReceiptStocktakingItem> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingItem::getHeadId, headId);
        /* ********* 过账入口 只查看盘盈/盘亏的物料明细对应的行项目列表 ******** */
        if (type == 2) {
            List<Long> itemIdList = new ArrayList<>();
            QueryWrapper<BizReceiptStocktakingBin> binWrapper = new QueryWrapper<>();
            binWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingBin::getHeadId, headId);
            Map<Long, List<BizReceiptStocktakingBin>> binMap = bizReceiptStocktakingBinDataWrap.list(binWrapper)
                .stream().collect(Collectors.groupingBy(BizReceiptStocktakingBin::getItemId));
            binMap.keySet().forEach(key -> {
                Set<Integer> diffTypeSet =
                    binMap.get(key).stream().map(bin -> bin.getDiffType()).collect(Collectors.toSet());
                if (diffTypeSet.size() == 1 && diffTypeSet.contains(EnumStocktakingDiffType.NO_DIFFERENCE.getValue())) {
                    itemIdList.add(key);
                }
            });
            itemWrapper.lambda().notIn(UtilCollection.isNotEmpty(itemIdList), BizReceiptStocktakingItem::getId,
                itemIdList);
        }
        Set<Integer> itemStatusSet = bizReceiptStocktakingItemDataWrap.list(itemWrapper).stream()
            .map(bin -> bin.getItemStatus()).collect(Collectors.toSet());
        if (itemStatusSet.size() == 1) {
            Integer receiptStatus = 0;
            /* ********* 如果均为已完成 且是盘点计数调用 更新盘点单行项目表 已计数 ******** */
            if (itemStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()) && type == 1) {
                receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue(); //计划盘点 新增已提交状态
            } else /* ********* 如果均为已记账 更新盘点单行项目表 已记账 ******** */
            if (itemStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue())) {
                receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue();
            }
            UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
            headWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingHead::getId, headId)
                .set(BizReceiptStocktakingHead::getReceiptStatus, receiptStatus);
            bizReceiptStocktakingHeadDataWrap.update(headWrapper);
        }
    }

    /**
     * 仓位解冻
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanBinSavePO : "po"："盘点单物料明细列表传输对象"}
     */
    public void unfreezeStorageBin(BizContext ctx) {
        /* ********* 从上下文获取盘点单物料明细列表传输对象 ******** */
        BizReceiptStocktakingPlanBinSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptStocktakingPlanBinDTO> binList = po.getBinList();
        /* ********* 获取所有仓位id ******** */
        Set<Long> binIdSet = binList.stream().map(bin -> bin.getBinId()).collect(Collectors.toSet());
        /* ********* 修改仓位状态未冻结 ******** */
        if (UtilCollection.isNotEmpty(binIdSet)) {
            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DicWhStorageBin::getId, binIdSet)
                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.NOT_FROZEN.getValue())
                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.NOT_FROZEN.getValue());
            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
                /* ********* 刷新缓存 ******** */
                editCacheService.refreshBinCache(new ArrayList<>(binIdSet));
            }
        }
    }

    /**
     * 获取差异列表
     *
     * @in ctx 入参 {@link Long : "headId"："抬头表id"}
     * @out ctx 出参 {@link MultiResultVO<> : "binDTOList" : "差异列表"}
     */
    public void setDifferenceList(BizContext ctx) {
        /* ********* 从上下文能获取抬头表id ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getId();
        /* ********* 获取差异列表 泛型转为DTO ******** */
        List<BizReceiptStocktakingPlanBinDTO> binDTOList =
            UtilCollection.toList(this.getDifferenceListById(headId), BizReceiptStocktakingPlanBinDTO.class);
        /* ********* 填充关联属性 ******** */
        dataFillService.fillRlatAttrDataList(binDTOList);
        /* ********* 差异列表信息放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(binDTOList));
    }

    /**
     * 根据主表id获取差异列表
     *
     * @param headId 主表id
     * @return 差异列表
     */
    public List<BizReceiptStocktakingBin> getDifferenceListById(Long headId) {
        /* ********* 获取差异列表 ******** */
        QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingBin::getHeadId, headId)
            .notIn(BizReceiptStocktakingBin::getSecondaryDiffType,
                    EnumStocktakingDiffType.INITIALIZATION.getValue(),
                    EnumStocktakingDiffType.NO_DIFFERENCE.getValue())
            .apply("stock_qty != qty");
        List<BizReceiptStocktakingBin> binList = bizReceiptStocktakingBinDataWrap.list(queryWrapper);
        return binList;
    }

    /**
     * 校验是否所有的行项目都是已提交状态
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     */
    public void checkReInventory(BizContext ctx) {
        /* ********* 从上下文获取复盘入参对象 ******** */
        BizReceiptStocktakingReInventoryPlanPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 查询草稿状态的物料列表 ******** */
        List<Integer> list = new ArrayList<>();
        list.add(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue());
        list.add(EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue());
        QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(po.getHeadId()), BizReceiptStocktakingBin::getHeadId, po.getHeadId());
        /* *********新增待认领装填，目的是当物料中有待计数跟待认领的单据，不让复盘******** */
        queryWrapper.lambda().in(BizReceiptStocktakingBin::getMatStatus, list);
        List<BizReceiptStocktakingBin> binList = bizReceiptStocktakingBinDataWrap.list(queryWrapper);
        /* ********* 如果存在待计数状态物料 不可重新盘点 ******** */
        if (!CollectionUtils.isEmpty(binList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXITS_ROW_UN_COUNTED_REINVERTORY_FAILURE);
        }
        /* ********* 看是否为抽盘 ******** */
        if (po.getIsReplaySpotCheck()) {
            /* ********* 1.如果是抽盘看抽盘对象是否为空 ******** */
            if (UtilObject.isNull(po.getHeadDTO())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            /* ********* 2.看主表id与抽盘单据id是否一致 ******** */
            if (!po.getHeadId().equals(po.getHeadDTO().getId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
            // 物料不合法
            if (!checkBinExist(po.getHeadDTO())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCKTAKING_MAT_ERROR);
            }
            // 抽盘数量不足
            if (!checkBinCount(po.getHeadDTO())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCKTAKING_MAT_COUNT_ERROR);
            }
        } else {
            /* ********* 差异盘点查看是否有差异行项目 ******** */
            if (!po.getIsReplayAll()) {
                if (UtilCollection.isEmpty(getDifferenceListById(po.getHeadId()))) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                }
            }
        }
    }

    /**
     * 抽盘物料信息校验
     *
     * @param headDTO 复盘单据对象
     * @return 是与否
     */
    public boolean checkBinExist(BizReceiptStocktakingPlanHeadDTO headDTO) {
        /* ********* 如果是抽盘看抽盘物料是否来源被首盘列表 ******** */
        QueryWrapper<BizReceiptStocktakingBin> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, headDTO.getId())
            .eq(BizReceiptStocktakingBin::getIsDelete, 0);
        List<BizReceiptStocktakingBin> binList = bizReceiptStocktakingBinDataWrap.list(wrapper);
        Map<Long, Long> map = binList.stream()
            .collect(Collectors.toMap(BizReceiptStocktakingBin::getId, BizReceiptStocktakingBin::getHeadId));
        boolean flag = true;
        for (BizReceiptStocktakingPlanItemDTO iteam : headDTO.getItemList()) {
            for (BizReceiptStocktakingPlanBinDTO bin : iteam.getBinList()) {
                if (!map.get(bin.getId()).equals(headDTO.getId())) {
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 抽盘物料信息校验
     *
     * @param headDTO 复盘单据对象
     * @return 是与否
     */
    public boolean checkBinCount(BizReceiptStocktakingPlanHeadDTO headDTO) {
        /* ********* 如果是抽盘看抽盘物料行项目数量是否大于原来单据的 30% ******** */
        QueryWrapper<BizReceiptStocktakingBin> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, headDTO.getId())
            .eq(BizReceiptStocktakingBin::getIsDelete, 0);
        List<BizReceiptStocktakingBin> binList = bizReceiptStocktakingBinDataWrap.list(wrapper);
        Map<Long, Long> map = binList.stream()
            .collect(Collectors.toMap(BizReceiptStocktakingBin::getId, BizReceiptStocktakingBin::getHeadId));
        Integer binCount = 0;
        for (BizReceiptStocktakingPlanItemDTO iteam : headDTO.getItemList()) {
            binCount += iteam.getBinList().size();
        }
        boolean flagCount = true;
        if (binCount < binList.size() * 0.3) {
            flagCount = false;
        }
        return flagCount;
    }

    /**
     * 重新盘点 生成新的单号 新的单据 物料按照批次托盘分组
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveReInventory(BizContext ctx) {
        /* ********* 生成新抬头表 行项目表 ******** */
        this.saveHeadAndItem(ctx);
        /* ********* 生成新物料明细表 ******** */
        this.saveBin(ctx);
    }

    /**
     * 重新盘点 生成新抬头表 行项目表
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     * @out ctx 出参 {@link BizReceiptStocktakingItem : "itemList" : "行项目表"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveHeadAndItem(BizContext ctx) {
        /* ********* 从上下文获取复盘入参对象 ******** */
        BizReceiptStocktakingReInventoryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 通过主表id查询原盘点单抬头表 ******** */
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(po.getHeadId());
        /* ********* 通过主表id查询原盘点单行项目表 ******** */
        QueryWrapper<BizReceiptStocktakingItem> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(po.getHeadId()), BizReceiptStocktakingItem::getHeadId,
            po.getHeadId());
        List<BizReceiptStocktakingItem> itemList = bizReceiptStocktakingItemDataWrap.list(queryWrapper);
        /* ********* 定义新的盘点单号 ******** */
        String receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE.getValue());
        /* ********* 设置新抬头表保存对象 ******** */
        head.setId(null);
        head.setReceiptCode(receiptCode);
        head.setIsReplay(EnumStocktakingReplayType.STOCKTAKING_REPLAY_YES.getValue());
        head.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue());
        head.setCreateTime(null);
        /* ********* 保存抬头表 ******** */
        bizReceiptStocktakingHeadDataWrap.save(head);
        /* ********* 设置新行项目表保存对象 ******** */
        itemList.forEach(item -> item.setId(null).setHeadId(head.getId())
            .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue()).setCreateTime(null));
        /* ********* 保存行项目表 ******** */
        bizReceiptStocktakingItemDataWrap.saveBatch(itemList);
        /* ********* 取出所有仓位id ******** */
        Set<Long> binIdSet = itemList.stream().map(item -> item.getBinId()).collect(Collectors.toSet());
        /* ********* 修改仓位状态为已冻结 ******** */
        if (UtilCollection.isNotEmpty(binIdSet)) {
            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DicWhStorageBin::getId, binIdSet)
                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.INVENTORY_FREEZE.getValue())
                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.INVENTORY_FREEZE.getValue());
            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
                /* ********* 刷新缓存 ******** */
                editCacheService.refreshBinCache(new ArrayList<>(binIdSet));
            }
        }
        /* ********* 新的盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        /* ********* 新的行项目列表放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, itemList);
    }

    /**
     * 重新盘点 生成新物料明细表
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     * @@in ctx 入参 {@link BizReceiptStocktakingItem : "itemList" : "行项目表"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBin(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取复盘入参对象 ******** */
        BizReceiptStocktakingReInventoryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 从上下文获取新的行项目列表 ******** */
        List<BizReceiptStocktakingItem> itemList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 设置物料明细列表 1.整单复盘 取原有所有物料列表 2.差异复盘 取差异物料列表 ******** */
        List<BizReceiptStocktakingBin> binList = new ArrayList<>();
        if (po.getIsReplayAll()) {
            QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(UtilNumber.isNotEmpty(po.getHeadId()), BizReceiptStocktakingBin::getHeadId,
                po.getHeadId());
            binList = bizReceiptStocktakingBinDataWrap.list(queryWrapper);
        } else {
            binList = getDifferenceListById(po.getHeadId());
        }
        /* ********* 根据原抬头表id取出原盘点单的盘点类型 ******** */
        Integer isReplay = bizReceiptStocktakingHeadDataWrap.getById(po.getHeadId()).getIsReplay();
        /* ********* 首盘复盘 取出物料明细列表的批次id 托盘id 设置新的id 抬头表id 行项目id ******** */
        if (isReplay.equals(EnumStocktakingReplayType.STOCKTAKING_REPLAY_NO.getValue())) {
            List<BizReceiptStocktakingBin> saveBinList =
                bizReceiptStocktakingBinDataWrap.selectStockBinListByReInventory(binList);
            itemList.forEach(item -> {
                AtomicReference<Integer> bid = new AtomicReference<>(1);
                saveBinList.forEach(bin -> {
                    if (item.getWhId().equals(bin.getWhId()) && item.getTypeId().equals(bin.getTypeId())
                        && item.getBinId().equals(bin.getBinId())) {
                        bin.setId(null).setHeadId(item.getHeadId()).setItemId(item.getId()).setBid(bid.toString())
                            .setDiffType(EnumStocktakingDiffType.INITIALIZATION.getValue())
                            .setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
                            .setModifyStatus(EnumRealYn.FALSE.getIntValue()).setCreateUserId(cUser.getId())
                            .setModifyUserId(cUser.getId());
                        bid.getAndSet(bid.get() + 1);
                    }
                    bin.setMatStatus(bin.getMatStatus() == null ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()
                        : bin.getMatStatus());
                    bin.setDiffType(bin.getDiffType() == null ? EnumStocktakingDiffType.INITIALIZATION.getValue()
                        : bin.getDiffType());
                    bin.setModifyStatus(
                        bin.getModifyStatus() == null ? EnumRealYn.FALSE.getIntValue() : bin.getModifyStatus());
                });
            });
            /* ********* 保存物料明细表 ******** */
            bizReceiptStocktakingBinDataWrap.saveBatch(saveBinList);
            /* ********* 差异复盘情况下 若存在任一行项目物料明细无差异-即没有匹配到物料明细 删除复盘单对应的行项目 ******** */
            Set<Long> itemIdSet = saveBinList.stream().map(bin -> bin.getItemId()).collect(Collectors.toSet());
            Set<Long> removeItemIdList = new HashSet<>();
            itemList.forEach(item -> {
                if (!itemIdSet.contains(item.getId())) {
                    removeItemIdList.add(item.getId());
                }
            });
            bizReceiptStocktakingItemDataWrap.multiPhysicalDeleteByIdList(removeItemIdList);
        } else /* ********* 复盘再复盘 沿用原有物料明细列表 修改盘点数量 盘点差异 状态等 设置新的id 抬头表id 行项目id ******** */
        if (isReplay.equals(EnumStocktakingReplayType.STOCKTAKING_REPLAY_YES.getValue())) {
            List<BizReceiptStocktakingBin> saveBinList = binList;
            itemList.forEach(item -> {
                AtomicReference<Integer> bid = new AtomicReference<>(1);
                saveBinList.forEach(bin -> {
                    if (item.getWhId().equals(bin.getWhId()) && item.getTypeId().equals(bin.getTypeId())
                        && item.getBinId().equals(bin.getBinId())) {
                        bin.setId(null).setHeadId(item.getHeadId()).setItemId(item.getId()).setBid(bid.toString())
                            .setQty(BigDecimal.ZERO).setDiffType(EnumStocktakingDiffType.INITIALIZATION.getValue())
                            .setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue())
                            .setModifyStatus(EnumRealYn.FALSE.getIntValue()).setCreateTime(null)
                            .setCreateUserId(cUser.getId()).setModifyUserId(cUser.getId());
                        bid.getAndSet(bid.get() + 1);
                    }
                    bin.setMatStatus(bin.getMatStatus() == null ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()
                        : bin.getMatStatus());
                    bin.setDiffType(bin.getDiffType() == null ? EnumStocktakingDiffType.INITIALIZATION.getValue()
                        : bin.getDiffType());
                    bin.setModifyStatus(
                        bin.getModifyStatus() == null ? EnumRealYn.FALSE.getIntValue() : bin.getModifyStatus());
                });
            });
            /* ********* 保存物料明细表 ******** */
            bizReceiptStocktakingBinDataWrap.saveBatch(saveBinList);
            /* ********* 差异复盘情况下 若存在任一行项目物料明细无差异-即没有匹配到物料明细 删除复盘单对应的行项目 ******** */
            Set<Long> itemIdSet = saveBinList.stream().map(bin -> bin.getItemId()).collect(Collectors.toSet());
            Set<Long> removeItemIdList = new HashSet<>();
            itemList.forEach(item -> {
                if (!itemIdSet.contains(item.getId())) {
                    removeItemIdList.add(item.getId());
                }
            });
            bizReceiptStocktakingItemDataWrap.multiPhysicalDeleteByIdList(removeItemIdList);
        }
    }

    /**
     * 【首盘可过账模式】重新盘点 生成新的单号 新的单据 物料按照批次托盘分组
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveReInventoryFirstCanBePosted(BizContext ctx) {
        /* ********* 生成新抬头表 行项目表 ******** */
        this.saveHeadAndItemFirstCanBePosted(ctx);
        /* ********* 生成新物料明细表 ******** */
        this.saveBinFirstCanBePosted(ctx);
    }

    /**
     * 【首盘可过账模式】重新盘点 生成新抬头表 行项目表 盘点人
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     * @out ctx 出参 {@link String : "receiptCode" : "盘点单号"}
     * @out ctx 出参 {@link BizReceiptStocktakingItem : "itemList" : "行项目表"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveHeadAndItemFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取复盘入参对象 ******** */
        BizReceiptStocktakingReInventoryPlanPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 通过主表id查询原盘点单抬头表 ******** */
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(po.getHeadId());
        /* ********* 通过主表id查询原盘点单行项目表 ******** */
        QueryWrapper<BizReceiptStocktakingItem> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(po.getHeadId()), BizReceiptStocktakingItem::getHeadId,
            po.getHeadId());
        List<BizReceiptStocktakingItem> itemList = bizReceiptStocktakingItemDataWrap.list(queryWrapper);
        /* ********* 得到复盘选定的盘点人信息 ******** */
        List<BizReceiptStocktakingUserDTO> userList = po.getUserList();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue();
        if (userList != null && userList.size() > 1) {
            receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue();
        }
        /* ********* 定义新的盘点单号 ******** */
        Long originReceiptHeadId = head.getId();
        String originReceiptCode = head.getReceiptCode();
        String receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE.getValue());
        /* ********* 设置新抬头表保存对象 ******** */
        Integer isAppointMat = po.getIsAppointMat();
        head.setId(null);
        head.setIsAppointMat(isAppointMat);
        head.setOriginReceiptHeadId(originReceiptHeadId);
        head.setOriginReceiptCode(originReceiptCode);
        head.setReceiptCode(receiptCode);
        head.setIsReplay(EnumStocktakingReplayType.STOCKTAKING_REPLAY_YES.getValue());
        head.setReceiptStatus(receiptStatus);
        head.setCreateTime(null);
        head.setUpHeadId(po.getHeadId());
        head.setLastReceiptHeadId(po.getHeadId());
        /* ********* 保存抬头表 ******** */
        bizReceiptStocktakingHeadDataWrap.saveDto(head);
        /* ********* 设置盘点人信息 ******** */
        userList.forEach(user -> {
            user.setId(null).setHeadId(head.getId()).setCreateUserId(cUser.getId());
        });
        /* ********* 保存盘点人 ******** */
        bizReceiptStocktakingUserDataWrap.saveBatchDto(userList);
        /* ********* 设置新行项目表保存对象 ******** */
        if (userList != null && userList.size() == 1) {
            itemList.forEach(item -> item.setUpIteamId(item.getId()).setId(null).setHeadId(head.getId())
                .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue()).setCreateTime(null)
                .setModifyUserId(userList.get(0).getStocktakingUserId()).setModifyTime(null)
                .setIsAppointMat(isAppointMat));
        } else {
            itemList.forEach(item -> item.setUpIteamId(item.getId()).setId(null).setHeadId(head.getId())
                .setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue()).setCreateTime(null)
                .setModifyUserId(100L).setModifyTime(null).setIsAppointMat(isAppointMat));
        }
        /* ********* 保存行项目表 ******** */
        bizReceiptStocktakingItemDataWrap.saveBatch(itemList);
        // 2023-06-01 sdw 邓健要求盘点不冻结仓位
        /* ********* 取出所有仓位id ******** */
//        Set<Long> binIdSet = itemList.stream().map(item -> item.getBinId()).collect(Collectors.toSet());
        /* ********* 修改仓位状态为已冻结 ******** */
//        if (UtilCollection.isNotEmpty(binIdSet)) {
//            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.lambda().in(DicWhStorageBin::getId, binIdSet)
//                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.INVENTORY_FREEZE.getValue())
//                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.INVENTORY_FREEZE.getValue());
//            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
//                /* ********* 刷新缓存 ******** */
//                editCacheService.refreshBinCache(new ArrayList<>(binIdSet));
//            }
//        }
        /* ********* 新的盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        /* ********* 新的行项目列表放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, itemList);
        /* ********* 新的单据id放入上下文，为单据流使用 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO, head);
    }

    /**
     * 【首盘可过账模式】重新盘点 生成新物料明细表
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     * @@in ctx 入参 {@link BizReceiptStocktakingItem : "itemList" : "行项目表"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBinFirstCanBePosted(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取复盘入参对象 ******** */
        BizReceiptStocktakingReInventoryPlanPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 从上下文获取新的行项目列表 ******** */
        List<BizReceiptStocktakingItem> itemList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 设置物料明细列表 1.整单复盘 取原有所有物料列表 2.差异复盘 取差异物料列表 ******** */
        List<BizReceiptStocktakingBin> binList = new ArrayList<>();
        if (po.getIsReplaySpotCheck()) {
            /* ********* 数据泛型转换 ******** */
            List<BizReceiptStocktakingPlanBinDTO> binDTOListAll = new ArrayList<>();
            for (BizReceiptStocktakingPlanItemDTO iteam : po.getHeadDTO().getItemList()) {
                binDTOListAll.addAll(iteam.getBinList());
            }
            binList = UtilCollection.toList(binDTOListAll, BizReceiptStocktakingBin.class);
        } else {
            /* ********* 不是抽盘看是全盘还是差异盘 ******** */
            if (po.getIsReplayAll()) {
                QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper();
                queryWrapper.lambda().eq(UtilNumber.isNotEmpty(po.getHeadId()), BizReceiptStocktakingBin::getHeadId,
                    po.getHeadId());
                binList = bizReceiptStocktakingBinDataWrap.list(queryWrapper);
            } else {
                binList = this.getDifferenceListById(po.getHeadId());
            }
        }

        List<BizReceiptStocktakingPlanBinDTO> binDTOList = UtilCollection.toList(binList, BizReceiptStocktakingPlanBinDTO.class);
        dataFillService.fillAttr(binDTOList);

        Integer matStatus = EnumReceiptStatus.RECEIPT_STATUS_UN_COUNTED.getValue();
        List<BizReceiptStocktakingUserDTO> userList = po.getUserList();
        Long modifyUserId = userList.get(0).getStocktakingUserId();
        if (userList != null && userList.size() > 1) {
            matStatus = EnumReceiptStatus.RECEIPT_STATUS_UN_GET.getValue();
            modifyUserId = 100L;
        }
        /* ********* 复盘 沿用原有物料明细列表 修改盘点数量 盘点差异 状态等 设置新的id 抬头表id 行项目id ******** */
        List<BizReceiptStocktakingPlanBinDTO> saveBinList = binDTOList;
        Integer finalMatStatus = matStatus;
        Long finalModifyUserId = modifyUserId;
        itemList.forEach(item -> {
            AtomicReference<Integer> bid = new AtomicReference<>(1);
            saveBinList.forEach(bin -> {
                if (item.getWhId().equals(bin.getWhId()) && item.getTypeId().equals(bin.getTypeId())
                    && item.getBinId().equals(bin.getBinId())) {
                    Long receiptBinId=bin.getId();
                    bin.setId(null).setHeadId(item.getHeadId()).setItemId(item.getId()).setBid(bid.toString())
                        .setQty(BigDecimal.ZERO).setDiffType(EnumStocktakingDiffType.INITIALIZATION.getValue())
                        .setMatStatus(finalMatStatus).setModifyStatus(EnumRealYn.FALSE.getIntValue())
                        .setCreateTime(null).setCreateUserId(cUser.getId()).setModifyUserId(finalModifyUserId)
                            .setOriginBinId(receiptBinId).setLastBinId(receiptBinId);
                    bid.getAndSet(bid.get() + 1);
                }
                bin.setMatStatus(bin.getMatStatus() == null ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()
                    : bin.getMatStatus());
                bin.setDiffType(
                    bin.getDiffType() == null ? EnumStocktakingDiffType.INITIALIZATION.getValue() : bin.getDiffType());
                bin.setModifyStatus(
                    bin.getModifyStatus() == null ? EnumRealYn.FALSE.getIntValue() : bin.getModifyStatus());
            });
        });
        /* ********* 保存物料明细表 ******** */
        bizReceiptStocktakingBinDataWrap.saveBatchDto(saveBinList);
        /* ********* 差异复盘情况下 若存在任一行项目物料明细无差异-即没有匹配到物料明细 删除复盘单对应的行项目 ******** */
        Set<Long> itemIdSet = saveBinList.stream().map(bin -> bin.getItemId()).collect(Collectors.toSet());
        Set<Long> removeItemIdList = new HashSet<>();
        List<BizReceiptStocktakingItem> itemListFree = new ArrayList<>();
        itemList.forEach(item -> {
            if (!itemIdSet.contains(item.getId())) {
                removeItemIdList.add(item.getId());
                itemListFree.add(item);
            }
        });
        bizReceiptStocktakingItemDataWrap.multiPhysicalDeleteByIdList(removeItemIdList);
        /* ********* 解冻对应删除仓位 ******** */
        Set<Long> binFreeIdSet = itemListFree.stream().map(item -> item.getBinId()).collect(Collectors.toSet());
        if (UtilCollection.isNotEmpty(binFreeIdSet)) {
            UpdateWrapper<DicWhStorageBin> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DicWhStorageBin::getId, binFreeIdSet)
                .set(DicWhStorageBin::getFreezeInput, EnumFreezeType.NOT_FROZEN.getValue())
                .set(DicWhStorageBin::getFreezeOutput, EnumFreezeType.NOT_FROZEN.getValue());
            if (dicWhStorageBinDataWrap.update(updateWrapper)) {
                /* ********* 刷新缓存 ******** */
                editCacheService.refreshBinCache(new ArrayList<>(binFreeIdSet));
            }
        }


        BizReceiptStocktakingHead head = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        BizReceiptStocktakingPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptStocktakingPlanHeadDTO.class);
        // 保存盘点关联单据
        List<BizReceiptStocktakingRelatedReceipt> relReceiptList = new ArrayList<>();
        saveBinList.forEach(bin -> {
            if (UtilCollection.isNotEmpty(bin.getInputReferReceiptList())) {
                bin.getInputReferReceiptList().forEach(relReceipt -> {
                    BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                    relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                    relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                    relatedReceiptEntity.setStocktakingBinId(bin.getId());
                    relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.INPUT.getValue());
                    relReceiptList.add(relatedReceiptEntity);
                });
            }
            if (UtilCollection.isNotEmpty(bin.getOutputReferReceiptList())) {
                bin.getOutputReferReceiptList().forEach(relReceipt -> {
                    BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                    relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                    relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                    relatedReceiptEntity.setStocktakingBinId(bin.getId());
                    relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.OUTPUT.getValue());
                    relReceiptList.add(relatedReceiptEntity);
                });
            }
            if (UtilCollection.isNotEmpty(bin.getTransportReferReceiptList())) {
                bin.getTransportReferReceiptList().forEach(relReceipt -> {
                    BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                    relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                    relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                    relatedReceiptEntity.setStocktakingBinId(bin.getId());
                    relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.TRANSPORT.getValue());
                    relReceiptList.add(relatedReceiptEntity);
                });
            }
            if (UtilCollection.isNotEmpty(bin.getArrangeReferReceiptList())) {
                bin.getArrangeReferReceiptList().forEach(relReceipt -> {
                    BizReceiptStocktakingRelatedReceipt relatedReceiptEntity = UtilBean.newInstance(relReceipt, BizReceiptStocktakingRelatedReceipt.class);
                    relatedReceiptEntity.setStocktakingHeadId(bin.getHeadId());
                    relatedReceiptEntity.setStocktakingItemId(bin.getItemId());
                    relatedReceiptEntity.setStocktakingBinId(bin.getId());
                    relatedReceiptEntity.setRelateType(EnumStocktakingReceiptRelatedType.BIN_ARRANGE.getValue());
                    relReceiptList.add(relatedReceiptEntity);
                });
            }
        });
        bizReceiptStocktakingRelatedReceiptDataWrap.saveBatch(relReceiptList);

        // 保存标签与盘点单关系
        this.saveLabelRel(binDTOList, headDTO);
    }

    /**
     * 重新盘点 更新原单据状态 已完成
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReInventoryPO : "po"："复盘入参对象"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReceiptStatusByReInventory(BizContext ctx) {
        /* ********* 从上下文获取复盘入参对象 ******** */
        BizReceiptStocktakingReInventoryPlanPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 从上下文获取单据code ******** */
        String receiptCode = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        /* ********* 取值id放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, po.getHeadId());
        /* ********* 更新单据已完成 ******** */
        this.updateStockTakeStatus(ctx);
        /* ********* 替换更新单据状态方法中 放入上下文的单据code ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
    }

    /**
     * 更新单据状态 已完成
     *
     * @in ctx 入参 {@link Long : "headId"："抬头表id"}
     * @@in ctx 入参 {@link String : "receiptCode" : "盘点单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStockTakeStatus(BizContext ctx) {
        /* ********* 从上下文获取抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 更新抬头表 ******** */
        UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
        headWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingHead::getId, headId)
            .set(BizReceiptStocktakingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        bizReceiptStocktakingHeadDataWrap.update(headWrapper);
        /* ********* 更新行项目表 ******** */
        UpdateWrapper<BizReceiptStocktakingItem> itemWrapper = new UpdateWrapper();
        itemWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingItem::getHeadId, headId)
            .set(BizReceiptStocktakingItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        bizReceiptStocktakingItemDataWrap.update(itemWrapper);
        /* ********* 更新物料明细表 ******** */
        UpdateWrapper<BizReceiptStocktakingBin> binWrapper = new UpdateWrapper();
        binWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingBin::getHeadId, headId)
            .set(BizReceiptStocktakingBin::getMatStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        bizReceiptStocktakingBinDataWrap.update(binWrapper);
        BizReceiptStocktakingHead oldHead=bizReceiptStocktakingHeadDataWrap.getById(headId);
        /* ********* 设置盘点单号 ******** */
        String receiptCode = oldHead.getReceiptCode();
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        BizReceiptStocktakingHead head = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        if(head!=null){
            Long newHeadId =head.getId();
            BizReceiptStocktakingHead newhead =bizReceiptStocktakingHeadDataWrap.getById(newHeadId);
            bizReceiptStocktakingHeadDataWrap.updateStocktakingBinCountByFirst(newhead); //更新当前单据可计数
            if(UtilNumber.isEmpty(newhead.getOriginReceiptHeadId())){ //首盘
                bizReceiptStocktakingHeadDataWrap.updateStocktakingBinIdByFirst(newhead);//首盘更新库存盘点物料明细表 首盘盘点单bin_id 最后一次复盘盘点单bin_id
            }else{
                bizReceiptStocktakingHeadDataWrap.updateStocktakingHeadIdByLast(newhead); //复盘更新库存盘点抬头表  最后一次复盘盘点单head_id
                if(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(newhead.getReceiptStatus())){
                    bizReceiptStocktakingHeadDataWrap.updateStocktakingBinCountByLast(newhead); //更新上一次单据已选的不可计数
                    bizReceiptStocktakingHeadDataWrap.updateStocktakingBinIdByLast(newhead); //复盘更新库存盘点物料明细表  最后一次复盘盘点单bin_id
                }else{
                    bizReceiptStocktakingHeadDataWrap.updateStocktakingBinCountByLast(oldHead); //更新上一次单据已选的不可计数
                    bizReceiptStocktakingHeadDataWrap.updateStocktakingBinIdByLast(oldHead); //复盘更新库存盘点物料明细表  最后一次复盘盘点单bin_id
                }
            }
        }

    }

    /**
     * PDA-获取盘点单行项目列表
     *
     * @in ctx 入参 {@link Long : "headId"："抬头表id"}
     * @@in ctx 入参 {@link BizReceiptStocktakingPlanItemDTO : "itemDTOList" : "行项目列表"}
     */
    public void setItemListbyHeadId(BizContext ctx) {
        /* ********* 从上下文获取抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 根据主表id查询行项目列表 ******** */
        QueryWrapper<BizReceiptStocktakingItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptStocktakingItem::getHeadId, headId);
        List<BizReceiptStocktakingItem> itemList = bizReceiptStocktakingItemDataWrap.list(queryWrapper);
        /* ********* 数据泛型转换为DTO ******** */
        List<BizReceiptStocktakingPlanItemDTO> itemDTOList =
            UtilCollection.toList(itemList, BizReceiptStocktakingPlanItemDTO.class);
        /* ********* 填充关联属性 ******** */
        dataFillService.fillRlatAttrDataList(itemDTOList);
        /* ********* 设置盘点类型 盘点方式 *PDA判断使用* ******** */
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(headId);
        itemDTOList.forEach(item ->
                item.setIsReplay(head.getIsReplay())
                    .setStocktakingMode(head.getStocktakingMode()));
        /* ********* 行项目列表放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(itemDTOList));
    }

    /**
     * PDA-获取盘点单物料明细列表
     *
     * @in ctx 入参 {@link Long : "itemId"："行项目表id"}
     * @@in ctx 入参 {@link BizReceiptStocktakingPlanBinDTO : "binDTOList" : "物料明细列表"}
     */
    public void setBinListByItemId(BizContext ctx) {
        /* ********* 从上下文获取行项目表id ******** */
        Long itemId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 根据行项目表id查询物料明细列表 ******** */
        QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UtilNumber.isNotEmpty(itemId), BizReceiptStocktakingBin::getItemId, itemId);
        List<BizReceiptStocktakingBin> binList = bizReceiptStocktakingBinDataWrap.list(queryWrapper);
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(binList)) {
            /* ********* 数据泛型转换为DTO ******** */
            binDTOList = UtilCollection.toList(binList, BizReceiptStocktakingPlanBinDTO.class);
            /* ********* 填充关联属性 ******** */
            dataFillService.fillAttr(binDTOList);
            /* ********* 设置盘点类型 盘点方法 单据状态 仓位是否指定物料 行项目状态 *PDA判断使用* ******** */
            BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(binDTOList.get(0).getHeadId());
            BizReceiptStocktakingItem item = bizReceiptStocktakingItemDataWrap.getById(itemId);
            binDTOList.forEach(bin -> bin.setIsReplay(head.getIsReplay()).setStocktakingMode(head.getStocktakingMode())
                .setReceiptStatus(head.getReceiptStatus()).setIsAppointMat(item.getIsAppointMat())
                .setItemStatus(item.getItemStatus()).setToolCode(bin.getBatchCode()));

            /* ********* 对盘点单计数时关联的标签数据做特殊处理，当标签的binId=0时，不论标签qty是多少都显示为0
             * 标签binId=0表示该标签已经出库，此时对于盘点来说，不再显示标签本身的数量
             */
            for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : binDTOList) {
                // 如果是普通标签的物料，跳过这段处理
                if (stocktakingBinDTO != null && UtilCollection.isNotEmpty(stocktakingBinDTO.getLabelReceiptRelDTOList())) {
                    for (BizStockdocLabelReceiptRelDTO bizLabelReceiptRelDTO : stocktakingBinDTO.getLabelReceiptRelDTOList()) {
                        if (UtilNumber.isEmpty(bizLabelReceiptRelDTO.getLabelBinId())) {
                            bizLabelReceiptRelDTO.setQty(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }
        /* ********* 物料明细列表放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(binDTOList));
    }

    /**
     * 设置批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO<> ("BizReceiptStocktakingPlanBinDTO":"库存盘点单物料明细列表")}
     * @out ctx 出参 {@link BizResultVO<> ("BizReceiptStocktakingPlanBinDTO":"库存盘点单物料明细列表及批次图片")}
     */
    public void setBatchImgByBin(BizContext ctx) {
        /* ********* 从上下文获取库存盘点单物料明细列表 ******** */
        MultiResultVO<BizReceiptStocktakingPlanBinDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = resultVO.getResultList();
        if (CollectionUtils.isEmpty(binDTOList)) {
            return;
        }
        /* ********* 设置批次id集合 ******** */
        Set<Long> collect =
            binDTOList.stream().map(BizReceiptStocktakingPlanBinDTO::getBatchId).collect(Collectors.toSet());
        /* ********* 获取批次图片 ******** */
        Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(collect, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        /* ********* 赋值批次图片 ******** */
        binDTOList.forEach(binDTO -> {
            if (UtilNumber.isNotEmpty(binDTO.getBatchId())
                && !CollectionUtils.isEmpty(imgMap.get(binDTO.getBatchId()))) {
                binDTO.setBizBatchImgDTOList(imgMap.get(binDTO.getBatchId()));
            }
        });
    }

    /**
     * 过账效验
     *
     * @in ctx 入参 {@link ReceiptItemActionPO: "单据行项目级别操作通用入参对象")}
     */
    public void checkPost(BizContext ctx) {
        /* ********* 从上下文获取单据行项目级别操作通用入参对象 ******** */
        ReceiptItemActionPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ********* 参数效验 ******** */
        if (UtilNumber.isEmpty(po.getHeadId()) || UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        /* ********* 获取物料明细列表 ******** */
        QueryWrapper<BizReceiptStocktakingBin> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptStocktakingBin::getHeadId, po.getHeadId())
            .in(BizReceiptStocktakingBin::getId, po.getBinIds());
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = UtilCollection.toList(
                bizReceiptStocktakingBinDataWrap.list(queryWrapper), BizReceiptStocktakingPlanBinDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(binDTOList);
        /* ********* 效验是否存在无差异物料 ******** */
        Set<Integer> diffTypeSet = binDTOList.stream().map(bin -> bin.getDiffType()).collect(Collectors.toSet());
        if (diffTypeSet.contains(EnumStocktakingDiffType.INITIALIZATION.getValue())
            || diffTypeSet.contains(EnumStocktakingDiffType.NO_DIFFERENCE.getValue())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_DIFFERENCE_CAN_NOT_POST);
        }
        /* ********* 物料明细列表放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, binDTOList);
        /* ********* 盘点单号放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, binDTOList.get(0).getReceiptCode());
    }

    /**
     * 生成ins凭证
     *
     * @in ctx 入参 {@link BizReceiptStocktakingPlanBinDTO : "物料明细列表"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInsDocToPost(BizContext ctx) {
        /* ********* 从上下文获取物料明细列表 ******** */
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 装载ins凭证 ******** */
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            /* ********* 生成ins凭证 ******** */
            insMoveTypeDTO = stocktakingMoveTypeComponent.generateInsDocToPost(binDTOList);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        /* ********* 过账前的校验和数量计算 ******** */
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        /* ********* 设置ins凭证到上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 盘点sap过账
     *
     * @in ctx 入参 {@link ReceiptItemActionPO: "单据行项目级别操作通用入参对象")}
     */
    @Transactional(rollbackFor = Exception.class)
    public void postToSap(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取物料明细列表 ******** */
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 从上下文获取ins凭证 ******** */
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        /* ********* 过滤已记账物料 ******** */
        binDTOList = binDTOList.stream()
            .filter(bin -> !bin.getMatStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue()))
            .collect(Collectors.toList());
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(binDTOList)) {
            /* ******** 设置入库单账期 ******** */
            this.setInPostDate(binDTOList, cUser);
            /* ******** 调用sap ******** */
            returnObj = erpPostingService.posting(JSONArray.toJSONStringWithDateFormat(binDTOList, "yyyy-MM-dd",
                SerializerFeature.WriteDateUseDateFormat));;
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptStocktakingPlanBinDTO binDTO : binDTOList) {
                        /* ********* 获取当前item返回对象 ******** */
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                            .filter(item -> item.getReceiptCode().equals(binDTO.getReceiptCode())
                                && item.getReceiptRid().equals(binDTO.getRid())
                                && item.getReceiptBid().equals(binDTO.getBid()))
                            .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        binDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                        binDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                        binDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        binDTO.setMatStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                        /* ********* 过账成功，补全ins凭证 ******** */
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                if (insDocBatch.getPreReceiptBinId().equals(binDTO.getId())) {
                                    insDocBatch.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBatch.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBatch.setPostingDate(binDTO.getPostingDate());
                                    insDocBatch.setDocDate(binDTO.getDocDate());
                                    insDocBatch.setMatDocYear(
                                        UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                                if (insDocBin.getPreReceiptBinId().equals(binDTO.getId())) {
                                    insDocBin.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBin.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBin.setMatDocYear(
                                        UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    /* ********* 更新物料明细【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、物料状态】 ******** */
                    // 更新物料明细【物料凭证编号、物料凭证的行序号、物料凭证年度、过帐日期、凭证时间、物料状态】
                    bizReceiptStocktakingBinDataWrap.updateBatchDtoById(binDTOList);
                    /* ********* 更新行项目状态 ******** */
                    Map<Long, List<BizReceiptStocktakingPlanBinDTO>> binDTOMap =
                        binDTOList.stream().collect(Collectors.groupingBy(BizReceiptStocktakingPlanBinDTO::getItemId));
                    binDTOMap.keySet().forEach(key -> updateItemStatus(binDTOMap.get(key).get(0).getHeadId(), key, 2));
                    /* ********* 更新单据状态 ******** */
                    updateHeadStatus(binDTOList.get(0).getHeadId(), 2);
                }
            } else {
                log.error("库存盘点{}SAP过账失败", binDTOList.get(0).getReceiptCode());
                /* ********* 更新head状态-未同步 ******** */
                UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
                headWrapper.lambda().eq(BizReceiptStocktakingHead::getId, binDTOList.get(0).getHeadId()).set(
                    BizReceiptStocktakingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                bizReceiptStocktakingHeadDataWrap.update(headWrapper);
                /* ********* 抛出接口调用失败异常 ******** */
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * 盘点instock过账
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void postStocktakingToIns(BizContext ctx) {
        /* ********* 从上下文获取物料明细列表 ******** */
        List<BizReceiptStocktakingPlanBinDTO> binDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 从上下文获取ins凭证 ******** */
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        try {
            /* ********* 修改库存 ******** */
            stockCommonService.modifyStock(insMoveTypeDTO);
        } catch (Exception e) {
            log.error("库存盘点{}ins过账失败，失败原因：{}", binDTOList.get(0).getReceiptCode(), e.getMessage());
            /* ********* 更新head状态-未同步 ******** */
            UpdateWrapper<BizReceiptStocktakingHead> headWrapper = new UpdateWrapper();
            headWrapper.lambda().eq(BizReceiptStocktakingHead::getId, binDTOList.get(0).getHeadId())
                .set(BizReceiptStocktakingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            bizReceiptStocktakingHeadDataWrap.update(headWrapper);
            /* ********* 过账失败 抛出异常 ******** */
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 过账前设置物料明细账期
     *
     * @param binList 未同步sap盘点单行项目
     * @param cUser 当前用户
     */
    private void setInPostDate(List<BizReceiptStocktakingPlanBinDTO> binList, CurrentUser cUser) {
        if (UtilCollection.isEmpty(binList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = binList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        /* ******** 判断过账日期是否在帐期内 ******** */
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, cUser.getId());
        for (BizReceiptStocktakingPlanBinDTO bin : binList) {
            bin.setDocDate(UtilDate.getNow());
            bin.setPostingDate(postingDate);
        }
    }


    /**
     * 盘点结果导出
     * @param ctx
     */
    public void exportStocktakingDetail(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("计划盘点结果"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<StocktakingPlanExportVO> exportVOS = this.queryStocktakingExportResult(po.getId());
        // 查询结果并导出
        UtilExcel.writeExcel(StocktakingPlanExportVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }

    /**
     * 盘点创建导出
     * @param ctx
     */
    public void exportStocktakingCreateDetail(BizContext ctx) {
        /* ********* 从上下文获取盘点单抬头表id ******** */
        BizReceiptStocktakingPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("成套设备计划盘点创建"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        List<StocktakingCreateExportVO> exportVOS = this.queryStocktakingCreateExportResult(po.getId());
        // 查询结果并导出
        UtilExcel.writeExcel(StocktakingCreateExportVO.class, exportVOS, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);

    }

    private List<StocktakingPlanExportVO> queryStocktakingExportResult(Long headId) {
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(headId);
        BizReceiptStocktakingPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptStocktakingPlanHeadDTO.class);
        dataFillService.fillAttr(headDTO);

        List<StocktakingPlanExportVO> exportVOList = new ArrayList<>();
        for (BizReceiptStocktakingPlanItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                StocktakingPlanExportVO vo = UtilBean.newInstance(stocktakingBinDTO, StocktakingPlanExportVO.class);
                exportVOList.add(vo);
            }
        }
        return exportVOList;
    }

    private List<StocktakingCreateExportVO> queryStocktakingCreateExportResult(Long headId) {
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(headId);
        BizReceiptStocktakingPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptStocktakingPlanHeadDTO.class);
        dataFillService.fillAttr(headDTO);

        List<StocktakingCreateExportVO> exportVOList = new ArrayList<>();
        for (BizReceiptStocktakingPlanItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                StocktakingCreateExportVO vo = UtilBean.newInstance(stocktakingBinDTO, StocktakingCreateExportVO.class);
                DicMaterialFactoryDTO dicMaterialFactory = dictionaryService.getDicMaterialFactoryByUniqueKey(stocktakingBinDTO.getMatId(), stocktakingBinDTO.getFtyId());
                if (UtilObject.isNotNull(dicMaterialFactory)) {
                    vo.setPackageTypeI18n(EnumPackageType.getDescByValue(dicMaterialFactory.getPackageType()))
                            .setDepositTypeI18n(EnumDepositType.getDescByValue(dicMaterialFactory.getDepositType()));
                }
                exportVOList.add(vo);
            }
        }
        return exportVOList;
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }

    /**
     * 判断盘点单中是否有复盘差异，如果有，则需要发起审批
     * @param ctx
     * @return
     */
    public boolean isNeedApproval(BizContext ctx) {
        boolean result = false;

        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Set<Integer> diffSet = Stream.of(EnumStocktakingDiffType.INVENTORY_PROFIT.getValue(),
                EnumStocktakingDiffType.INVENTORY_LOSS.getValue()).collect(Collectors.toSet());

        for (BizReceiptStocktakingPlanItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptStocktakingPlanBinDTO stocktakingBinDTO : itemDTO.getBinList()) {
                // 复盘差异中有盘盈或盘亏的，需要审批
                if (diffSet.contains(stocktakingBinDTO.getSecondaryDiffType())) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void startWorkflow(BizContext ctx) {

        // 发起流程审批
        BizReceiptStocktakingPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(headDTO);

        // 审批校验
        Integer receiptType = this.approveCheck(ctx);

        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        Long ftyId = headDTO.getFtyId();
        variables.put("ftyId", ftyId);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getRemark());
        String procId = workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        if (UtilString.isNotNullOrEmpty(procId)) {
            // 更新盘点单 - 审批中
            updateReceiptStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        } else {
            // 未创建审批，暂定将单据更新为 - 已完成，使盘点流程结束
            updateReceiptStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }

    private Integer approveCheck(BizContext ctx) {
        // TODO 根据盘点审批流程设计，校验各个审批节点的部门和岗位是否存在
        // TODO PRD 没说审批流程 这里还沿用盘点计划审批流
        return EnumReceiptType.PLAN_STOCK_TAKE.getValue();
    }

    /**
     * 盘点审批回调
     *
     * @param wfReceiptCo
     */
//    @WmsMQListener(tags = TagConst.APPROVAL_STOCKTAKING_DIFF_SUBMIT)
    @Transactional(rollbackFor = Exception.class)
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取领料出库单申请信息
        BizReceiptStocktakingHead head = bizReceiptStocktakingHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        // 数据类型转换DTO
        BizReceiptStocktakingPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptStocktakingPlanHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 更新状态为已完成，使盘点流程结束
            updateReceiptStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        } else {
            // 驳回，更新单据状态为已驳回
            updateReceiptStatus(headDTO.getId(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

}
