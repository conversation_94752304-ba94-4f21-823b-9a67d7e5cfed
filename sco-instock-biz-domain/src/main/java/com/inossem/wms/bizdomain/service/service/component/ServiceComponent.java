package com.inossem.wms.bizdomain.service.service.component;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizdomain.facility.service.datawrap.DicFacilityDataWrap;
import com.inossem.wms.bizdomain.service.service.datawrap.BizReceiptServiceCostItemDataWrap;
import com.inossem.wms.bizdomain.service.service.datawrap.BizReceiptServiceFacilityItemDataWrap;
import com.inossem.wms.bizdomain.service.service.datawrap.BizReceiptServiceHeadDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptMatApplyHead;
import com.inossem.wms.common.model.bizdomain.service.dto.BizReceiptServiceCostItemDTO;
import com.inossem.wms.common.model.bizdomain.service.dto.BizReceiptServiceFacilityItemDTO;
import com.inossem.wms.common.model.bizdomain.service.dto.BizReceiptServiceHeadDTO;
import com.inossem.wms.common.model.bizdomain.service.entity.BizReceiptServiceCostItem;
import com.inossem.wms.common.model.bizdomain.service.entity.BizReceiptServiceFacilityItem;
import com.inossem.wms.common.model.bizdomain.service.entity.BizReceiptServiceHead;
import com.inossem.wms.common.model.bizdomain.service.po.BizReceiptServiceSearchPO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.facility.dto.DicFacilityDTO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 服务工单管理
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@Slf4j
public class ServiceComponent {

    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    protected BizCommonService bizCommonService;
    @Autowired
    protected ApprovalService approvalService;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    protected WorkflowService workflowService;
    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    protected SysUserDataWrap sysUserDataWrap;
    @Autowired
    private BizReceiptServiceHeadDataWrap bizReceiptServiceHeadDataWrap;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private BizReceiptServiceFacilityItemDataWrap bizReceiptServiceFacilityItemDataWrap;
    @Autowired
    private BizReceiptServiceCostItemDataWrap bizReceiptServiceCostItemDataWrap;
    @Autowired
    private DicFacilityDataWrap dicFacilityDataWrap;
    @Autowired
    private DicSupplierDataWrap dicSupplierDataWrap;

    /**
     * 初始化
     */
    public void init(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptServiceHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptServiceHeadDTO()
                        .setReceiptType(EnumReceiptType.SERVICE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setCreateUserName(ctx.getCurrentUser().getUserName())
                        .setSupplierList(dicSupplierDataWrap.list()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 分页
     */
    public void getPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptServiceSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po.getCreateTimeEnd())) {
            po.setCreateTimeEnd(UtilDate.getEndOfDay(po.getCreateTimeEnd()));
        }
        // 分页查询处理
        IPage<BizReceiptServiceHeadDTO> page = po.getPageObj(BizReceiptServiceHeadDTO.class);
        if (page.orders().isEmpty()) {
            page.orders().add(OrderItem.desc("create_time"));
        } else {
            page.orders().forEach(obj -> {
                // 排序特殊处理
                obj.setColumn(obj.getColumn().replace("supplier_code", "supplier_id"));
            });
        }
        WmsQueryWrapper<BizReceiptServiceSearchPO> queryWrapper = new WmsQueryWrapper<>();
        queryWrapper.lambda()
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptServiceSearchPO::getReceiptStatus, BizReceiptServiceHead.class, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptServiceSearchPO::getReceiptCode, BizReceiptServiceHead.class, po.getReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getSupplierCode()), BizReceiptServiceSearchPO::getSupplierCode, DicSupplier.class, po.getSupplierCode())
                .like(UtilString.isNotNullOrEmpty(po.getSupplierName()), BizReceiptServiceSearchPO::getSupplierName, DicSupplier.class, po.getSupplierName())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptServiceSearchPO::getUserName, SysUser.class, po.getCreateUserName())
                .between(UtilObject.isNotEmpty(po.getCreateTimeStart()) && UtilObject.isNotEmpty(po.getCreateTimeEnd()), BizReceiptServiceSearchPO::getCreateTime, BizReceiptServiceHead.class, po.getCreateTimeStart(), po.getCreateTimeEnd());
        bizReceiptServiceHeadDataWrap.selectPage(page, queryWrapper);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(page.getRecords());
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 详情
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptServiceHead head = bizReceiptServiceHeadDataWrap.getById(headId);
        // 属性填充
        BizReceiptServiceHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptServiceHeadDTO.class);
        dataFillService.fillAttr(headDTO);

        // 供应商列表
        if (UtilNumber.isEmpty(headDTO.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(headDTO.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headDTO.getReceiptStatus())) {
            headDTO.setSupplierList(dicSupplierDataWrap.list());
        }

        // 更新上次更新时间
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headDTO.getReceiptStatus())) {
            List<DicFacilityDTO> facilityDTOList = headDTO.getFacilityItemDTOList().stream()
                    .map(item ->
                            new DicFacilityDTO()
                                    .setSupplierCode(headDTO.getSupplierCode())
                                    .setFacilityCode(item.getFacilityCode())
                                    .setTargetDate(item.getTargetDate()))
                    .collect(Collectors.toList());
            List<BizReceiptServiceFacilityItemDTO> preSettlementDateItemDTOList = dicFacilityDataWrap.selectServiceItemDTOListBySupplier(new BizReceiptServiceSearchPO().setFacilityList(facilityDTOList));
            for (BizReceiptServiceFacilityItemDTO itemDTO : headDTO.getFacilityItemDTOList()) {
                itemDTO.setPreSettlementDate(null);
                for (BizReceiptServiceFacilityItemDTO preSettlementDateItemDTO : preSettlementDateItemDTOList) {
                    if (preSettlementDateItemDTO.getSupplierCode().equals(headDTO.getSupplierCode())
                            && preSettlementDateItemDTO.getFacilityCode().equals(itemDTO.getFacilityCode())
                            && preSettlementDateItemDTO.getTargetDate().equals(itemDTO.getTargetDate())) {
                        itemDTO.setPreSettlementDate(preSettlementDateItemDTO.getPreSettlementDate());
                    }
                }
            }
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    private ButtonVO setButton(BizReceiptServiceHeadDTO headDTO) {
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(headDTO.getReceiptStatus())) {
            // 草稿 -【保存、提交、删除】
            buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(headDTO.getReceiptStatus())) {
            // 待处理 -【提交、撤销】
            buttonVO.setButtonSubmit(true).setButtonRevoke(true);

        } else if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue().equals(headDTO.getReceiptStatus())) {
            // 待结算 -【确认结算】
            buttonVO.setButtonConfirmSettlement(true);

        }
        return buttonVO;
    }

    /**
     * 撤销
     */
    public void revoke(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilNumber.isEmpty(po.getId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, "主键为空");
        }
        BizReceiptServiceHead head = bizReceiptServiceHeadDataWrap.getById(po.getId());
        if (UtilObject.isNull(head) || !EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        // 更新单据状态草稿
        this.updateReceiptStatus(po, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE);
    }

    /**
     * 保存校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 计算费用合计
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (UtilCollection.isNotEmpty(po.getFacilityItemDTOList())) {
            for (BizReceiptServiceFacilityItemDTO facilityItemDTO : po.getFacilityItemDTOList()) {
                totalAmount = totalAmount.add(UtilObject.isNull(facilityItemDTO.getTotalPrice()) ? BigDecimal.ZERO : facilityItemDTO.getTotalPrice());
            }
        }
        if (UtilCollection.isNotEmpty(po.getCostItemDTOList())) {
            for (BizReceiptServiceCostItemDTO costItemDTO : po.getCostItemDTOList()) {
                totalAmount = totalAmount.add(UtilObject.isNull(costItemDTO.getTotalPrice()) ? BigDecimal.ZERO : costItemDTO.getTotalPrice());
            }
        }
        po.setTotalAmount(totalAmount);
    }

    /**
     * 提交校验入参
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 基础数据校验
        this.checkSaveData(ctx);
        // 单据状态校验
        if (UtilNumber.isNotEmpty(po.getId())) {
            BizReceiptServiceHead head = bizReceiptServiceHeadDataWrap.getById(po.getId());
            if (UtilObject.isNotNull(head) && !head.getReceiptStatus().equals(po.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
            }
        }
        // 供应商重复单据检验
        if (EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(po.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())) {
            WmsQueryWrapper<BizReceiptServiceHead> queryWrapper = new WmsQueryWrapper<>();
            queryWrapper.lambda().eq(BizReceiptServiceHead::getSupplierId, po.getSupplierId());
            queryWrapper.lambda().in(BizReceiptServiceHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue());
            BizReceiptServiceHead duplicateReceipt = bizReceiptServiceHeadDataWrap.getOne(queryWrapper, false);
            if (UtilObject.isNotNull(duplicateReceipt)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_SUPPLIER_SERVICE_RECEIPT_DUPLICATE, duplicateReceipt.getReceiptCode());
            }
        }
    }

    /**
     * 提交
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存单据
        this.save(ctx);
        // 更新单据状态
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())) {
            // 更新单据状态待处理
            this.updateReceiptStatus(po, EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue());
        } else if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(po.getReceiptStatus())) {
            // 更新单据状态待结算
            this.updateReceiptStatus(po, EnumReceiptStatus.RECEIPT_STATUS_WAIT_SETTLEMENT.getValue());
        }
    }

    /**
     * 确认结算
     */
    public void confirmSettlement(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_CONFIRM);
        // 保存单据
        this.save(ctx);
        // 更新单据状态已结算
        this.updateReceiptStatus(po, EnumReceiptStatus.RECEIPT_STATUS_SETTLEMENT_COMPLETED.getValue());
    }

    /**
     * 保存
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        dto.setModifyUserId(user.getId());
        dto.setModifyTime(UtilDate.getNow());
        if (UtilNumber.isEmpty(dto.getReceiptStatus())
                || EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue().equals(dto.getReceiptStatus())) {
            dto.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        }
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(dto.getId())) {
            // 更新
            bizReceiptServiceHeadDataWrap.updateDtoById(dto);

            // 设施行项目物理删除
            QueryWrapper<BizReceiptServiceFacilityItem> facilityItemQueryWrapper = new QueryWrapper<>();
            facilityItemQueryWrapper.lambda().eq(BizReceiptServiceFacilityItem::getHeadId, dto.getId());
            bizReceiptServiceFacilityItemDataWrap.physicalDelete(facilityItemQueryWrapper);

            // 费用行项目物理删除
            QueryWrapper<BizReceiptServiceCostItem> costItemQueryWrapper = new QueryWrapper<>();
            costItemQueryWrapper.lambda().eq(BizReceiptServiceCostItem::getHeadId, dto.getId());
            bizReceiptServiceCostItemDataWrap.physicalDelete(costItemQueryWrapper);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            dto.setId(null);
            dto.setCreateUserId(user.getId());
            String receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SERVICE.getValue());
            dto.setReceiptCode(receiptCode);
            // 新增
            bizReceiptServiceHeadDataWrap.saveDto(dto);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }

        // 设施行项目处理
        List<BizReceiptServiceFacilityItemDTO> facilityItemDTOList = dto.getFacilityItemDTOList();
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptServiceFacilityItemDTO itemDTO : facilityItemDTOList) {
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(dto.getId());
            itemDTO.setItemStatus(dto.getReceiptStatus());
            itemDTO.setCreateUserId(UtilNumber.isEmpty(itemDTO.getCreateUserId()) ? user.getId() : itemDTO.getCreateUserId());
            itemDTO.setModifyUserId(user.getId());
        }
        bizReceiptServiceFacilityItemDataWrap.saveBatchDto(facilityItemDTOList);

        // 费用行项目处理
        List<BizReceiptServiceCostItemDTO> costItemDTOList = dto.getCostItemDTOList();
        rid = new AtomicInteger(1);
        for (BizReceiptServiceCostItemDTO itemDTO : costItemDTOList) {
            itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            itemDTO.setId(null);
            itemDTO.setHeadId(dto.getId());
            itemDTO.setCreateUserId(UtilNumber.isEmpty(itemDTO.getCreateUserId()) ? user.getId() : itemDTO.getCreateUserId());
            itemDTO.setModifyUserId(user.getId());
        }
        bizReceiptServiceCostItemDataWrap.saveBatchDto(costItemDTOList);

        log.debug("保存服务工单申请head成功!单号{},主键{},操作人{}", dto.getReceiptCode(), dto.getId(), user.getUserName());

        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, dto.getReceiptCode());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, dto);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, dto.getId());
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptServiceHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptServiceHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的服务工单申请
        BizReceiptServiceHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptServiceHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存服务工单申请附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 更新状态
     */
    private void updateReceiptStatus(BizReceiptServiceHeadDTO head, Integer receiptStatus) {
        head.setReceiptStatus(receiptStatus);
        bizReceiptServiceHeadDataWrap.updateDtoById(head);
    }

    /**
     * 获取供应商设施行项目
     */
    public void supplierFacility(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptServiceSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po.getSettlementDate())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 查询供应商设施行项目
        List<BizReceiptServiceFacilityItemDTO> facilityItemDTOList = dicFacilityDataWrap.selectServiceItemDTOListBySupplier(po);
        for (BizReceiptServiceFacilityItemDTO facilityItemDTO : facilityItemDTOList) {
            // 结算日期
            facilityItemDTO.setSettlementDate(po.getSettlementDate());
            // 天数
            if (UtilObject.isNotNull(facilityItemDTO.getPreSettlementDate())) {
                // 有上一结算日期：结算日期-上一结算日期
                facilityItemDTO.setSettlementDays(-UtilDate.getDaysDifference(po.getSettlementDate(), facilityItemDTO.getPreSettlementDate()));
            } else {
                // 无上一结算日期：结算日期-预订日期
                facilityItemDTO.setSettlementDays(-UtilDate.getDaysDifference(po.getSettlementDate(), facilityItemDTO.getTargetDate()));
            }
            // 币种
            facilityItemDTO.setCurrency(EnumContractCurrency.USD.getDesc());
            // 总价
            facilityItemDTO.setTotalPrice(facilityItemDTO.getPrice().multiply(new BigDecimal(facilityItemDTO.getSettlementDays())));
        }
//        // 转为供应商设施父子结构
//        List<BizReceiptServiceSupplierFacilityItemVO> voList = new ArrayList<>();
//        Map<Long, List<BizReceiptServiceFacilityItemDTO>> supplierFacilityMap = facilityItemDTOList.stream().collect(Collectors.groupingBy(BizReceiptServiceFacilityItemDTO::getSupplierId));
//        for (Map.Entry<Long, List<BizReceiptServiceFacilityItemDTO>> entry : supplierFacilityMap.entrySet()) {
//            BizReceiptServiceSupplierFacilityItemVO vo = new BizReceiptServiceSupplierFacilityItemVO();
//            vo.setSupplierCode(entry.getValue().get(0).getSupplierCode());
//            vo.setSupplierName(entry.getValue().get(0).getSupplierName());
//            vo.setFacilityItemDTOList(entry.getValue());
//            voList.add(vo);
//        }
//        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(voList));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(facilityItemDTOList));
    }

    /**
     * 删除
     *
     * @in ctx 入参
     */
    public void remove(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 单据状态校验
        BizReceiptServiceHead head = bizReceiptServiceHeadDataWrap.getById(id);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        /* ********* 删除服务工单 ******** */
        bizReceiptServiceHeadDataWrap.removeById(id);
        /* ********* 删除服务工单设施行项目 ******** */
        bizReceiptServiceFacilityItemDataWrap.remove(new QueryWrapper<BizReceiptServiceFacilityItem>().lambda().eq(BizReceiptServiceFacilityItem::getHeadId, id));
        /* ********* 删除服务工单费用行项目 ******** */
        bizReceiptServiceCostItemDataWrap.remove(new QueryWrapper<BizReceiptServiceCostItem>().lambda().eq(BizReceiptServiceCostItem::getHeadId, id));
    }

}
