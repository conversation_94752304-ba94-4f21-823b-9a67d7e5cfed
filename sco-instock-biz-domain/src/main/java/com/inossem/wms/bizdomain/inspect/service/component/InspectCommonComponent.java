package com.inossem.wms.bizdomain.inspect.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchImgDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizCommonReceiptAttachmentDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectUserDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.rel.entity.SysUserRoleRel;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchImg;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptAttachment;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectUserDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectUser;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchUserListPO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.corp.entity.DicCorp;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 质检验收公共 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-26
 */
@Slf4j
@Service
public class InspectCommonComponent {

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected ApprovalService approvalService;

    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected SysUserDataWrap sysUserDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected BizReceiptInspectUserDataWrap bizReceiptInspectUserDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private BizBatchImgDataWrap bizBatchImgDataWrap;

    @Autowired
    protected BizCommonReceiptAttachmentDataWrap bizCommonReceiptAttachmentDataWrap;

    @Autowired
    protected DataFillService dataFillService;

    /**
     * 获取参检人/审批人列表
     *
     * @in ctx 入参 {@link BizReceiptInspectSearchUserListPO : "查询用户列表入参"}
     * @out ctx 出参 {@link MultiResultVO <> ("sysUserDTOList":"用户列表")}
     */
    public void setUserList(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInspectSearchUserListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<SysUser> page = new Page<>(po.getPageIndex(), po.getPageSize());
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getUserName()), SysUser::getUserName, po.getUserName());
        queryWrapper.lambda().in(UtilCollection.isNotEmpty(po.getUserCodeList()), SysUser::getUserCode, po.getUserCodeList());
        sysUserDataWrap.page(page, queryWrapper);
        List<SysUser> userList = page.getRecords();
        List<SysUserDTO> userDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userList)) {
            userList.forEach(item -> {
                MetaDataDeptOfficePO deptOfficePO = new MetaDataDeptOfficePO();
                deptOfficePO.setUserId(item.getId());
                List<DicDeptDTO> dicDeptDTOList = dicDeptDataWrap.getUserDeptOfficeTree(deptOfficePO);
                Set<String> deptOfficeIds = new HashSet<>();
                List<String> deptOfficeNames = new ArrayList<>();
                dicDeptDTOList.forEach(deptDTO -> {
                    if (UtilCollection.isEmpty(deptDTO.getOfficeDTOList())) {
                        String deptId = String.valueOf(deptDTO.getId());
                        String deptName = deptDTO.getDeptName();
                        if (!deptOfficeIds.contains(deptId)) {
                            deptOfficeIds.add(String.valueOf(deptId));
                            deptOfficeNames.add(deptName);
                        }
                    } else {
                        deptDTO.getOfficeDTOList().forEach(officeDTO -> {
                            Long officeId = officeDTO.getId();
                            String officeName = officeDTO.getDeptOfficeName();
                            if (UtilObject.isNotEmpty(officeId)) {
                                deptOfficeIds.add(String.valueOf(officeId));
                                deptOfficeNames.add(officeName);
                            }
                            String deptId = String.valueOf(deptDTO.getId());
                            String deptName = deptDTO.getDeptName();
                            if (!deptOfficeIds.contains(deptId)) {
                                deptOfficeIds.add(String.valueOf(deptId));
                                deptOfficeNames.add(deptName);
                            }
                        });
                    }
                });
                SysUserDTO sysUserDTO = UtilBean.newInstance(item, SysUserDTO.class);
                if (UtilCollection.isNotEmpty(dicDeptDTOList)){
                    sysUserDTO.setDeptId(dicDeptDTOList.get(0).getId());
                    sysUserDTO.setDeptCode(dicDeptDTOList.get(0).getDeptCode());
                    sysUserDTO.setDeptName(dicDeptDTOList.get(0).getDeptName());
                }
                sysUserDTO.setDeptOfficeNames(String.join(",", deptOfficeNames));
                userDTOList.add(sysUserDTO);
            });
        }
        // 放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(userDTOList, page.getTotal()));
    }

    /**
     * 获取参检人/审批人反显name
     *
     * @in ctx 入参 {@link BizReceiptInspectSearchUserListPO : "查询用户列表入参"}
     * @out ctx 出参 {@link String ("userName":"用户name描述")}
     */
    public void setUserNameList(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInspectSearchUserListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 定义出参
        List<String> userNameList = new ArrayList<>(po.getUserCodeList().size());
        if(UtilCollection.isNotEmpty(po.getUserCodeList())) {
            // 查询处理
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SysUser::getUserCode, po.getUserCodeList());
            List<SysUser> userList = sysUserDataWrap.list(queryWrapper);
            po.getUserCodeList().forEach(p -> {
                userList.forEach(q -> {
                    if(p.equals(q.getUserCode())) {
                        userNameList.add(q.getUserName());
                    }
                });
            });
        }
        // 放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, String.join(",", userNameList));
    }

    /**
     * 保存质检单
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检单"}
     * @out ctx 出参 {"receiptCode" : "质检单单号"},{@link BizReceiptInspectHeadDTO : "已保存的质检单}
     */
    public void saveInspect(BizContext ctx) {
        // 入参上下文 - 要保存的质检单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptStatus = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();

        // 2023-04-24 需求调整，退库&退转库申请不再直接生成"退库/退转库质检"，改为生成"退库/退转库分配质检单"

        // 判断是否是分配质检单
        boolean isDistributeInspection = headDTO.getReceiptType().equals(EnumReceiptType.DISTRIBUTE_INSPECTION_PURCHASE.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.DISTRIBUTE_INSPECTION_RETURN_TRANSFER.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_DISTRIBUTE_INSPECTION_RETURN_TRANSFER.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_DISTRIBUTE_INSPECTION_RETURN.getValue());

        // 判读是否是采购入库质检会签单
        boolean isPurchaseSignInspection = headDTO.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue());
        // 判读是否是退库质检会签单
        boolean isReturnSignInspection = headDTO.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_RETURN.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN.getValue());
        // 判读是否是退转库质检会签单
        boolean isReturnTransferSignInspection = headDTO.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_RETURN_TRANSFER.getValue())
                || headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN_TRANSFER.getValue());

        // 判读是否是质检会签单
        boolean isSignInspection = isPurchaseSignInspection || isReturnSignInspection || isReturnTransferSignInspection;

        headDTO.setReceiptStatus(receiptStatus);
        headDTO.setModifyUserId(user.getId());
        headDTO.setModifyTime(UtilDate.getNow());
//        Boolean isPda = headDTO.getIsPda();
        // web同步修改成按行项目提交
        Boolean isPda = true;
        Boolean isSubmit = headDTO.getIsSubmit();
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            //PDA分批操作，不进行删除行操作
            // 更新质检单
            bizReceiptInspectHeadDataWrap.updateDtoById(headDTO);
            
            if(null==isPda || !isPda){
                // 非PDA模式 - 全量更新
                // 修改前删除item
                UpdateWrapper<BizReceiptInspectItem> wrapperItem = new UpdateWrapper<>();
                wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptInspectItem::getHeadId, headDTO.getId());
                bizReceiptInspectItemDataWrap.physicalDelete(wrapperItem);
                // 物理删除附件信息
                UpdateWrapper<BizCommonReceiptAttachment> wrapper = new UpdateWrapper<>();
                wrapper.lambda().in(BizCommonReceiptAttachment::getReceiptItemId, headDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getId).collect(Collectors.toList()));
                bizCommonReceiptAttachmentDataWrap.physicalDelete(wrapper);
                // 修改前删除user
                QueryWrapper<BizReceiptInspectUser> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(BizReceiptInspectUser::getHeadId, headDTO.getId());
                List<BizReceiptInspectUser> entityList = bizReceiptInspectUserDataWrap.list(queryWrapper);
                if (!CollectionUtils.isEmpty(entityList)) {
                    List<Long> idList = entityList.stream().map(BizReceiptInspectUser::getId).collect(Collectors.toList());
                    bizReceiptInspectUserDataWrap.multiPhysicalDeleteByIdList(idList);
                }
            } else {
                //不需要删除
                //如果是提交，重新查询行项目
                if (null != isSubmit && isSubmit) {
                    headDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(headDTO.getId()), BizReceiptInspectHeadDTO.class);
                    dataFillService.fillAttr(headDTO);
                }
            }
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            headDTO.setCreateUserId(user.getId());
            if(headDTO.getMainCreateTime()!=null){ //质检会签子单创建时间取母单创建时间
                headDTO.setCreateTime(headDTO.getMainCreateTime());
            }else{
                headDTO.setCreateTime(UtilDate.getNow());
            }
            // 分配质检单生成单号
            DicCorp corpCache = dictionaryService.getCorpCacheById(user.getCorpId());
            if(headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_DISTRIBUTE_INSPECTION_RETURN_TRANSFER.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSFER_RETURN_DISTRIBUTE.getValue());
            }
            else if(isDistributeInspection) {
                log.debug("设置单据类型为：分配质检单，receiptType={}", headDTO.getReceiptType());
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_DISTRIBUTE_INSPECT.getValue());
            }
            else if(headDTO.getReceiptType().equals(EnumReceiptType.DISTRIBUTE_INSPECTION_RETURN.getValue())) {
                // 退库质检分配
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_DISTRIBUTE_INSPECT_RETURN.getValue());

            }
            // 判读是否是采购入库质检会签单
            else if (isPurchaseSignInspection) {
                log.debug("设置单据类型为：质检会签单，receiptType={}", headDTO.getReceiptType());
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(headDTO.getItemList().get(0).getFtyId());
                log.debug("设置单据类型为：分配质检单，receiptType={}", headDTO.getReceiptType());
                if(corpCache.getCorpCode().equals(Const.HL_59C0)){
                    // HL-5ST-RCBG11-ZJHQ-YYZZZZ
                    receiptCode = "HL-5ST-RCBG11-ZJHQ-" + bizCommonService.getNextSequenceYear("distribute_inspect_hl");

                } else {
                    receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.INSPECT_ORDER_PURCHASE.getValue());
                }
            }
            // 判读是否是退库质检会签单
            else if (isReturnSignInspection) {
                log.debug("设置单据类型为：质检会签单，receiptType={}", headDTO.getReceiptType());
                receiptCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.INSPECT_ORDER_RETURN.getValue());
            }
            // 判读是否是退转库质检会签单
            else if (headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN_TRANSFER.getValue())) {
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSFER_RETURN_INSPECT.getValue());
            }
            else if (isReturnTransferSignInspection) {
                log.debug("设置单据类型为：质检会签单，receiptType={}", headDTO.getReceiptType());
//                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(headDTO.getItemList().get(0).getFtyId());
//                if(corpCache.getCorpCode().equals(Const.HL_59C0)){
//                    // HL-5ST-RCBG11-TZZJ-YYZZZZ
//                    receiptCode = "HL-5ST-RCBG11-TZZJ-" + bizCommonService.getNextSequenceYear("inspect_order_return_transfer_hl");
//
//                } else {
//                    receiptCode = bizCommonService.getNextReturnTransferSeqInspectOrder(user,EnumSequenceCode.INSPECT_ORDER_RETURN_TRANSFER.getValue(), factoryDTO.getFtyCode(), headDTO.getItemList().get(0).getReferReceiptCode());
//                }
                receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_INSPECT.getValue());
            }
            else {
                log.error("创建验收单/分配质检单/质检会签单过程执行失败，单据类型错误，当前类型为: {}", headDTO.getReceiptType());
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
            }
            headDTO.setReceiptCode(receiptCode);
            bizReceiptInspectHeadDataWrap.saveDto(headDTO);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid;
        if(null!=isPda && isPda) {
            // PDA模式下需要查询当前最大的rid
            QueryWrapper<BizReceiptInspectItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(BizReceiptInspectItem::getHeadId, headDTO.getId())
                    .orderByDesc(BizReceiptInspectItem::getRid);
            List<BizReceiptInspectItem> existingItems = bizReceiptInspectItemDataWrap.list(queryWrapper);
            
            // 获取当前最大的rid值
            int maxRid = 0;
            if(CollectionUtils.isNotEmpty(existingItems)) {
                maxRid = existingItems.stream()
                        .mapToInt(item -> Integer.parseInt(item.getRid()))
                        .max()
                        .orElse(0);
            }
            rid = new AtomicInteger(maxRid + 1);
        } else {
            rid = new AtomicInteger(1);
        }

        for (BizReceiptInspectItemDTO itemDTO : headDTO.getItemList()) {
            if(null==isPda || !isPda){
                itemDTO.setId(null);
            }
            itemDTO.setHeadId(headDTO.getId());
            // 只有在id为空时(新增行)才设置新的rid
            if(UtilNumber.isEmpty(itemDTO.getId())) {
                itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            }
            itemDTO.setItemStatus(receiptStatus);
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
            if (headDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN.getValue())) { //成套设备质检会签-退库
                itemDTO.setProductDate(itemDTO.getBizBatchInfoDTO().getProductionDate());
            }
            // 质检会签提交时拆单且为母单时更新待质检数量

            // if(isSignInspection && EnumRealYn.FALSE.getIntValue().equals(headDTO.getIsSonReceipt()) && operationLogType != null) {
            //     itemDTO.setArrivalQty(itemDTO.getArrivalQty().subtract(itemDTO.getQty()).subtract(itemDTO.getUnqualifiedQty()).subtract(itemDTO.getUnarrivalQty()));
            // }

            //7.19 子单的待验收数量固定为0
            // if(isSignInspection && EnumRealYn.TRUE.getIntValue().equals(headDTO.getIsSonReceipt())) {
            //     itemDTO.setArrivalQty(itemDTO.getArrivalQty().subtract(itemDTO.getQty()).subtract(itemDTO.getUnqualifiedQty()).subtract(itemDTO.getUnarrivalQty()));
            // }
        }
        if(null==isPda || !isPda){
            bizReceiptInspectItemDataWrap.saveBatchDto(headDTO.getItemList());
        }else{
            headDTO.getItemList().forEach(System.out::println);
            bizReceiptInspectItemDataWrap.saveOrUpdateBatchDto(headDTO.getItemList());
        }
        /* ********************** item处理结束 *************************/
        if(isSignInspection && UtilCollection.isNotEmpty(headDTO.getInspectUserList())) {
            /* ********************** user处理开始 *************************/
            AtomicInteger rids = new AtomicInteger(1);
            for (BizReceiptInspectUserDTO userDTO : headDTO.getInspectUserList()) {
                userDTO.setId(null);
                userDTO.setHeadId(headDTO.getId());
                userDTO.setRid(Integer.toString(rids.getAndIncrement()));
                userDTO.setCreateUserId(user.getId());
                userDTO.setModifyUserId(user.getId());
                userDTO.setCreateTime(UtilDate.getNow());
                userDTO.setModifyTime(UtilDate.getNow());
            }
            bizReceiptInspectUserDataWrap.saveBatchDto(headDTO.getInspectUserList());
            /* ********************** user处理开始 *************************/
        }
        // 保存单据流
        this.saveReceiptTree(headDTO);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        // 返回保存的质检单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 设置批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO ("head":"质检单详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"质检详情及批次图片")}
     */
    public void setBatchImg(BizContext ctx) {
        // 入参上下文
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead())) {
            return;
        }
        // 设置批次图片
        this.setBatchImg(resultVO.getHead());
        // 设置质检单详情批次图片到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 设置批次图片信息
     *
     * @param headDTO {@link BizReceiptInspectHeadDTO ("head":"质检单详情")}
     */
    public void setBatchImg(BizReceiptInspectHeadDTO headDTO) {
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            Set<Long> batchIdSet = headDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getBatchId).collect(Collectors.toSet());
            // 获取批次图片
            Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
            if (imgMap.isEmpty()) {
                return;
            }
            // 全部行项目赋值批次图片
            headDTO.getItemList().forEach(itemDTO -> {
                if (UtilNumber.isNotEmpty(itemDTO.getBatchId())
                        && UtilCollection.isNotEmpty(imgMap.get(itemDTO.getBatchId()))) {
                    itemDTO.setBizBatchImgDTOList(imgMap.get(itemDTO.getBatchId()));
                }
            });
        }
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的质检单
     */
    public void saveReceiptTree(BizReceiptInspectHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInspectItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存批次图片
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "保存的质检单"}
     */
    public void saveBizBatchImg(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        for (BizReceiptInspectItemDTO itemDto : headDTO.getItemList()) {
            if (UtilCollection.isNotEmpty(itemDto.getBizBatchImgDTOList())) {
                itemDto.getBizBatchImgDTOList().forEach(imgDTO -> {
                    imgDTO.setId(null);
                    imgDTO.setMatId(itemDto.getMatId());
                    imgDTO.setFtyId(itemDto.getFtyId());
                    imgDTO.setImgBizType(EnumImageBizType.SIGN_INSPECTION.getValue());
                    imgDTO.setReceiptType(headDTO.getReceiptType());
                    imgDTO.setReceiptHeadId(headDTO.getId());
                    imgDTO.setReceiptItemId(itemDto.getId());
                    imgDTO.setCreateUserId(ctx.getCurrentUser().getId());
                });
            } else {
                itemDto.setBizBatchImgDTOList(new ArrayList<>());
            }
        }
        // 批量保存质检单批次图片
        bizBatchImgService.multiSaveBizBatchImg(headDTO.getItemList().stream()
                .flatMap(item -> item.getBizBatchImgDTOList().stream()).collect(Collectors.toList()));
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保持附件的质检单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的质检单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存质检单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());

        // 保存行项目附件
        this.saveItemAttachment(headDTO, user);
    }

    /**
     * 清空行项目上的图片和附件
     *
     * @param itemDTOList itemDTOList
     */
    public void resetImageAndAttachment(List<BizReceiptInspectItemDTO> itemDTOList) {

        List<Long> itemIdList = itemDTOList.stream().map(BizReceiptInspectItemDTO::getId).collect(Collectors.toList());
        // 物理删除附件信息
        UpdateWrapper<BizCommonReceiptAttachment> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(BizCommonReceiptAttachment::getReceiptItemId, itemIdList);
        bizCommonReceiptAttachmentDataWrap.physicalDelete(wrapper);
        // 物理删除图片
        UpdateWrapper<BizBatchImg> imgUpdateWrapper = new UpdateWrapper<>();
        imgUpdateWrapper.lambda().in(BizBatchImg::getReceiptItemId, itemIdList);
        bizBatchImgDataWrap.physicalDelete(imgUpdateWrapper);

    }

    public void saveAttachment(BizContext ctx) {
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存行项目附件
        this.saveItemAttachment(headDTO, user);
        // 保存图片
        this.saveBizBatchImg(ctx);
    }

    private void saveItemAttachment(BizReceiptInspectHeadDTO headDTO, CurrentUser user) {
        headDTO.getItemList().forEach(c -> {
            receiptAttachmentService.saveBizReceiptAttachment(c.getFileList(), c.getHeadId(), c.getId(), headDTO.getReceiptType(), user.getId());
        });

    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的质检单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的质检单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }
    public void updateStatusByItem(BizContext ctx) {
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(null, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        int i = (int)bizReceiptInspectItemDataWrap.count(new QueryWrapper<BizReceiptInspectItem>().lambda().eq(BizReceiptInspectItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                .eq(BizReceiptInspectItem::getHeadId, headDTO.getId()));
        if(i == 0){
            // 行项目全完成则修改抬头状态
            this.updateStatus(headDTO, null, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 质检单抬头
     * @param itemDTOList 质检单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptInspectHeadDTO headDTO, List<BizReceiptInspectItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptInspectHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptInspectHead::getId, headDTO.getId())
                    .set(BizReceiptInspectHead::getReceiptStatus, receiptStatus);
            bizReceiptInspectHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptInspectItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptInspectItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptInspectItem::getItemStatus, receiptStatus);
            bizReceiptInspectItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 质检单抬头
     * @param itemDTOList 质检单行项目
     * @param receiptStatus 单据状态
     * @param delFlag true--更新head数据为已删除
     */
    public void updateStatusAndDelete(BizReceiptInspectHeadDTO headDTO, List<BizReceiptInspectItemDTO> itemDTOList, Integer receiptStatus, boolean delFlag) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptInspectHead> headUpdateWrapper = new UpdateWrapper<>();
            if (delFlag) {
                headUpdateWrapper.lambda().eq(BizReceiptInspectHead::getId, headDTO.getId())
                        .set(BizReceiptInspectHead::getReceiptStatus, receiptStatus).set(BizReceiptInspectHead::getIsDelete, EnumRealYn.TRUE.getIntValue());
            } else {
                headUpdateWrapper.lambda().eq(BizReceiptInspectHead::getId, headDTO.getId())
                        .set(BizReceiptInspectHead::getReceiptStatus, receiptStatus);
            }
            bizReceiptInspectHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptInspectItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptInspectItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptInspectItem::getItemStatus, receiptStatus);
            bizReceiptInspectItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    resultVO.getHead().setApproveList(approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode()));
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    public void setButtonHidden(BizContext ctx) {
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead())) {
            return;
        }

        CurrentUser currentUser = ctx.getCurrentUser();
        // 当角色有且只有"JS006"时，才需要隐藏所有按钮只保留打印
        if (ObjectUtils.isNotEmpty(currentUser.getSysUserRoleRelList()) && currentUser.getSysUserRoleRelList().size() == 1){
            Set<String> tempRole = currentUser.getSysUserRoleRelList().stream().map(SysUserRoleRel::getRoleCode).collect(Collectors.toSet());
            if(tempRole.contains("JS006")) {
                // 进行权限特殊处理，需求组角色开放质检会签与验收入库打印 
                boolean buttonPrint = resultVO.getButton().getButtonPrint();
                ButtonVO buttonVO = new ButtonVO();
                buttonVO.setButtonPrint(buttonPrint);
                resultVO.setButton(buttonVO);
                resultVO.getHead().setRelationList(null);
            }
        }
    }

}
