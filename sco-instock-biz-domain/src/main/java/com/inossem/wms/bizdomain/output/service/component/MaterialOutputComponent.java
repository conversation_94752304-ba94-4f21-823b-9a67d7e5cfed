package com.inossem.wms.bizdomain.output.service.component;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.erp.service.biz.ReserveReceiptService;
import com.inossem.wms.bizbasis.erp.service.biz.SaleReceiptService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReceiveReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpReceiveReceiptItemDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.SapWbsDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsMatReqOutputMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsMatReqOutputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.*;
import com.inossem.wms.bizdomain.task.service.component.TaskComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.apply.EnumReceiveType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssemble;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyItem;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputSignatureGraphDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.*;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskItem;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.erp.dto.ErpReceiveReceiptItemDTO;
import com.inossem.wms.common.model.erp.dto.ErpReserveReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpReceiveReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpReceiveReceiptItem;
import com.inossem.wms.common.model.erp.entity.SapWbs;
import com.inossem.wms.common.model.erp.po.ReserveReceiptQueryPO;
import com.inossem.wms.common.model.file.img.entity.BizCommonImage;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.file.service.datawrap.BizCommonImageDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wang
 * @description 领料出库组件类
 * @date 2022/4/24 13:35
 */
@Service
@Slf4j
public class MaterialOutputComponent {

    @Autowired
    private ReserveReceiptService reserveReceiptService;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private ErpPostingService erpPostingService;
    @Autowired
    private HXSapIntegerfaceService hxInterfaceService;

    @Autowired
    private SapInterfaceService sapInterfaceService;
    @Autowired
    private SaleReceiptService saleReceiptService;
    @Autowired
    private PurchaseReceiptService purchaseReceiptService;


    @Autowired
    private OutputComponent outputComponent;
    @Autowired
    private InsMatReqOutputMoveTypeComponent insMatReqOutputMoveTypeComponent;
    @Autowired
    private InsMatReqOutputWriteOffMoveTypeComponent insMatReqOutputWriteOffMoveTypeComponent;

    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private BizReceiptOutputInfoDataWrap bizReceiptOutputInfoDataWrap;
    @Autowired
    private BizReceiptOutputBinDataWrap bizReceiptOutputBinDataWrap;


    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    private SapWbsDataWrap sapWbsDataWrap;
    @Autowired
    private BizLabelReceiptRelDataWrap bizLabelReceiptRelDataWrap;

    @Autowired
    private BizReceiptOutputSignatureGraphDataWrap signatureGraphDataWrap;

    @Autowired
    private DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    private SysUserDataWrap sysUserDataWrap;

    @Autowired
    private BizCommonImageDataWrap commonImageDataWrap;
    @Autowired
    private ErpReceiveReceiptHeadDataWrap erpReceiveReceiptHeadDataWrap;
    @Autowired
    private ErpReceiveReceiptItemDataWrap erpReceiveReceiptItemDataWrap;
    @Autowired
    private BizCommonImageDataWrap bizCommonImageDataWrap;

    @Autowired
    private TaskComponent taskComponent;


    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true).setButtonClose(true);
        BizResultVO<BizReceiptOutputHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptOutputHeadDTO().setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()), new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    /**
     * 获取出库单列表(分页)
     *
     * @param ctx 上下文
     */
    public void getPage(BizContext ctx) {
        BizReceiptOutputQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        Date startTime = null;
        Date endTime = null;
        Date startCreateTime = null;
        Date endCreateTime = null;
        if (Objects.nonNull(po.getPostCreateTime()) && Objects.nonNull(po.getPostEndTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getPostCreateTime());
            endTime = UtilLocalDateTime.getEndTime(po.getPostEndTime());
        }
        if (Objects.nonNull(po.getStartTime()) && Objects.nonNull(po.getEndTime())) {
            startCreateTime = UtilLocalDateTime.getStartTime(po.getStartTime());
            endCreateTime = UtilLocalDateTime.getEndTime(po.getEndTime());
        }
        String remark = po.getRemark();
        String actualReceiverName = po.getActualReceiverName();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptOutputQueryListPO> pageWrapper = new WmsQueryWrapper<>();
        pageWrapper.lambda()
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptOutputQueryListPO::getReceiptCode,
                        BizReceiptOutputHead.class, po.getReceiptCode())
                .eq(Boolean.TRUE,BizReceiptOutputQueryListPO::getReceiptType,BizReceiptOutputHead.class, po.getReceiptType())
                .eq(UtilString.isNotNullOrEmpty(po.getMatDocCode()), BizReceiptOutputQueryListPO::getMatDocCode,
                        BizReceiptOutputItem.class, po.getMatDocCode())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptOutputQueryListPO::getReceiptStatus,
                        BizReceiptOutputHead.class, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptOutputQueryListPO::getLocationId,
                        BizReceiptOutputItem.class,locationIdList)
                .between((Objects.nonNull(startTime)), BizReceiptOutputQueryListPO::getPostingDate,
                        BizReceiptOutputItem.class, startTime, endTime)
                .between((Objects.nonNull(startCreateTime)), BizReceiptOutputQueryListPO::getCreateTime,
                        BizReceiptOutputHead.class, startCreateTime, endCreateTime)

                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()),BizReceiptOutputQueryListPO::getMatCode, DicMaterial.class,po.getMatCode())
                .eq(UtilNumber.isNotEmpty(po.getReservedOrderCode()),BizReceiptOutputQueryListPO::getReservedOrderCode,BizReceiptApplyItem.class,po.getReservedOrderCode())
                .between((Objects.nonNull(po.getApplyStartTime())), BizReceiptOutputQueryListPO::getApplyTime,
                        BizReceiptOutputInfo.class,po.getApplyStartTime() , po.getApplyEndTime())
                .like(UtilString.isNotNullOrEmpty(remark), BizReceiptOutputQueryListPO::getRemark, BizReceiptOutputHead.class, remark)
                .like(UtilString.isNotNullOrEmpty(actualReceiverName), BizReceiptOutputQueryListPO::getActualReceiverName, BizReceiptOutputHead.class, actualReceiverName)
                .like(Objects.nonNull(po.getCreateUserName()), BizReceiptOutputQueryListPO::getUserName,
                        SysUser.class, po.getCreateUserName())
                .like(UtilString.isNotNullOrEmpty(po.getPreReceiptCode()), BizReceiptOutputQueryListPO::getReceiptCode, BizReceiptApplyHead.class, po.getPreReceiptCode())
                .eq(UtilObject.isNotNull(po.getReceiveType()), BizReceiptOutputQueryListPO::getReceiveType, BizReceiptApplyHead.class, po.getReceiveType())
                .like(UtilString.isNotNullOrEmpty(po.getUserName()), BizReceiptOutputQueryListPO::getUserName, SysUser.class, po.getUserName())
                .like(UtilString.isNotNullOrEmpty(po.getMatDept()), BizReceiptOutputQueryListPO::getDeptName, DicDept.class, po.getMatDept());
        IPage<BizReceiptOutputPageVO> page = po.getPageObj(BizReceiptOutputPageVO.class);
//        IPage<BizReceiptOutputPageVO> outputPageVoList = bizReceiptOutputHeadDataWrap.getOutputPageVoList(page, pageWrapper);
        IPage<BizReceiptOutputPageVO> outputPageVoList = bizReceiptOutputHeadDataWrap.getOutputInfoPageVoList(page, pageWrapper);
        dataFillService.fillAttr(outputPageVoList);
        long totalCount = page.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), totalCount));
    }



    /**
     * 获取领料出库详情
     *
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizReceiptOutputHeadDTO headDTO = outputComponent.getItemListById(headId);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        int itemSize = itemDTOList.size();
        Set<String> stockCodeSet = new HashSet<>(itemSize);
        List<String> stockCodeList = new ArrayList<>(itemSize);
        for (int i=0; i < itemSize; i++) {
            BizReceiptOutputItemDTO outputItemDTO = itemDTOList.get(i);
            if (i == 0) {
                headDTO.setDocDate(outputItemDTO.getDocDate());
                headDTO.setPostingDate(outputItemDTO.getPostingDate());
                headDTO.setMatDocCode(outputItemDTO.getMatDocCode());
            }
            String specStockCode = outputItemDTO.getSpecStockCode();
            if (StringUtils.hasText(specStockCode) && stockCodeSet.add(specStockCode)) {
                stockCodeList.add(specStockCode);
            }
            List<BizReceiptOutputBinDTO> binDTOList = outputItemDTO.getBinDTOList();
            if (UtilCollection.isNotEmpty(binDTOList)) {
                String binCodeStr = binDTOList.stream().map(BizReceiptOutputBinDTO::getBinCode).collect(Collectors.joining(","));
                outputItemDTO.setBinCodeStr(binCodeStr);
            }

        }
        if (!CollectionUtils.isEmpty(stockCodeList)) {
            List<SapWbs> wbsList = sapWbsDataWrap.findByCodes(stockCodeList);
            if (!CollectionUtils.isEmpty(wbsList)) {
                Map<String, String> wbsMap = new HashMap<>(wbsList.size());
                for (SapWbs sapWbs : wbsList) {
                    wbsMap.put(sapWbs.getWbsCodeOut(), sapWbs.getWbsName());
                }
                for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                    String specStockCode = itemDTO.getSpecStockCode();
                    if (StringUtils.hasText(specStockCode)) {
                        String specStockName = wbsMap.get(specStockCode);
                        if (specStockName != null) {
                            itemDTO.setSpecStockName(specStockName);
                        }
                    }
                }
            }
        }
        // 已发货数量
        this.setSubmitQty(itemDTOList, user);
        // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
        outputComponent.setMaterialFactoryInfo(itemDTOList);
        // 可用数量
        this.setAvailableQty(itemDTOList);
        // 填充申请信息(领料信息)
        this.fillingApplyData(headDTO);
        // 填充下架信息
        this.fillTask(headDTO);
        // 填充签字信息
        this.fillSignature(headDTO);

        this.calAmountAndPrice(headDTO);

        this.setUserInfo(headDTO);

        // 设置按钮
        ButtonVO button = this.setButton(headId, headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }

    private void fillTask(BizReceiptOutputHeadDTO headDTO) {
        for (BizReceiptOutputItemDTO outputItemDTO : headDTO.getItemDTOList()) {
            List<BizReceiptTaskItemDTO> taskItemDTOList = taskComponent.getTaskItemByPreReceiptItemIds(new ArrayList<>(Collections.singletonList(outputItemDTO.getId())));
            if (UtilCollection.isNotEmpty(taskItemDTOList)) {
                // List<String> collect = taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getSubmitUserSignImg).collect(Collectors.toList());
                outputItemDTO.setSubmitUserSignImg(taskItemDTOList.get(0).getSubmitUserSignImg());
                outputItemDTO.setSubmitTime(taskItemDTOList.get(0).getSubmitTime());
            }

        }
    }

    private void setUserInfo(BizReceiptOutputHeadDTO headDTO){

        if(UtilCollection.isNotEmpty(headDTO.getItemDTOList())){
            if(UtilCollection.isNotEmpty(headDTO.getItemDTOList().get(0).getBinDTOList())){
                Long userId = headDTO.getItemDTOList().get(0).getBinDTOList().get(0).getCreateUserId();
                Date time = headDTO.getItemDTOList().get(0).getBinDTOList().get(0).getCreateTime();

                SysUser user = sysUserDataWrap.getById(userId);
                if(user!=null){
                    headDTO.setOutputUser(user.getUserName());
                }

                headDTO.setOutputDate(time);
            }
        }
    }



    /**
     * 计算金额和单价
     * @param headDTO
     */
    private void calAmountAndPrice(BizReceiptOutputHeadDTO headDTO) {
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        BigDecimal ZERO = BigDecimal.ZERO;
        BigDecimal totalPrice = ZERO;
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            List<BizReceiptOutputBinDTO> binDTOList = item.getBinDTOList();
            if(UtilCollection.isNotEmpty(binDTOList)){
                for (BizReceiptOutputBinDTO outputBinDTO : binDTOList) {
                    BigDecimal dmbtr = outputBinDTO.getDmbtr();
                    if(UtilNumber.isEmpty(dmbtr)){
                        dmbtr = ZERO;
                    }
                    if(ZERO.compareTo(outputBinDTO.getQty())==0){
                        continue;
                    }
                    outputBinDTO.setPrice(dmbtr.divide(outputBinDTO.getQty(),2, RoundingMode.HALF_UP));
                    totalPrice = totalPrice.add(dmbtr);
                }
            }
        }
        headDTO.setTotalAmount(totalPrice);
        headDTO.setTotalAmountInChinese(UtilNumber.bigDecimalToLocalStr(totalPrice));
        headDTO.setTotalAmountStr(headDTO.getTotalAmount() + "元");
    }


    /**
     * 获取打印数据
     * @param ctx
     */
    public void getPrintInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        BizReceiptOutputHeadDTO headDTO = outputComponent.getItemListById(headId);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        BizReceiptOutputItemDTO bizReceiptOutputItemDTO = itemDTOList.get(0);
        if (Objects.nonNull(bizReceiptOutputItemDTO)) {
            headDTO.setDocDate(bizReceiptOutputItemDTO.getDocDate());
            headDTO.setPostingDate(bizReceiptOutputItemDTO.getPostingDate());
            headDTO.setMatDocCode(bizReceiptOutputItemDTO.getMatDocCode());
        }

        // 已发货数量
        this.setSubmitQty(itemDTOList, user);
        // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
        outputComponent.setMaterialFactoryInfo(itemDTOList);
        // 可用数量
        this.setAvailableQty(itemDTOList);
        // 填充申请信息(领料信息)
        this.fillingApplyData(headDTO);
        // 组装打印数据
        headDTO.getItemDTOList().stream().forEach(itemDTO->{
            String binCodeStr = itemDTO.getBinDTOList().stream().map(BizReceiptOutputBinDTO::getBinCode).collect(Collectors.joining(Const.COMMA));
            // 仓位
            itemDTO.setBinCode(binCodeStr);
            List<BizBatchInfoDTO> batchInfoDTOList = itemDTO.getBinDTOList().stream().map(BizReceiptOutputBinDTO::getBatchInfo).collect(Collectors.toList());
            String batchCodeStr = batchInfoDTOList.stream().map(BizBatchInfoDTO::getBatchCode).collect(Collectors.joining(Const.COMMA));
            itemDTO.setBatchCodeStr(batchCodeStr);
        });
        // 设置按钮
        ButtonVO button = this.setButton(headId,headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }

    /**
     * 填充申请信息(领料信息)
     *
     * @param headDTO
     */
    private void fillingApplyData(BizReceiptOutputHeadDTO headDTO) {
        if (headDTO.getItemDTOList().size() > 0) {
            Long preReceiptHeadId = headDTO.getItemDTOList().get(0).getPreReceiptHeadId();
            if(UtilNumber.isEmpty(preReceiptHeadId)){
                return;
            }
            // 根据前续单据id查询申请表的领料信息
            BizReceiptApplyHead receiptApplyHead = bizReceiptApplyHeadDataWrap.getById(preReceiptHeadId);
            BizReceiptApplyHeadDTO applyHeadDTO = UtilBean.newInstance(receiptApplyHead, BizReceiptApplyHeadDTO.class);
            headDTO.setIsInnerFlag(applyHeadDTO.getIsInnerFlag());
            headDTO.setIsLaborFlag(applyHeadDTO.getIsLaborFlag());
            headDTO.setCostCenterId(applyHeadDTO.getCostCenterId());
            headDTO.setCostCenterCode(applyHeadDTO.getCostCenterCode());
            headDTO.setMatApplyTime(applyHeadDTO.getCreateTime());

            SysUser user = sysUserDataWrap.getById(applyHeadDTO.getCreateUserId());
            Optional.ofNullable(bizCommonImageDataWrap.getById(user.getCommonImgId())).ifPresent(commonImg -> {
                headDTO.setMatApplyUserSignImg(commonImg.getImgBase64());
            });
            headDTO.setMatApplyUser(user.getUserName());
            headDTO.setWbsCode(applyHeadDTO.getWbsCode());
            Long deptId = applyHeadDTO.getCounterpartDeptId();
            if (deptId != null) {
                headDTO.setCounterpartDeptId(deptId);
            }
            Long officeId = applyHeadDTO.getCounterpartOfficeId();
            if (officeId != null) {
                headDTO.setCounterpartOfficeId(officeId);
            }
            headDTO.setReceiveType( applyHeadDTO.getReceiveType()); // 领用类型 1 手工领用 2需求计划领用

            // 属性填充
            BizReceiptOutputInfo bizReceiptOutputInfo = bizReceiptOutputInfoDataWrap.getById(applyHeadDTO.getOutInfoId());
            if (UtilObject.isNotNull(bizReceiptOutputInfo)) {
                headDTO.setReceiptNum(bizReceiptOutputInfo.getReceiptNum());
                headDTO.setReceiptRemark(bizReceiptOutputInfo.getReceiptRemark());
                Long matReceiverId = bizReceiptOutputInfo.getMatReceiverId();
                if (UtilNumber.isEmpty(matReceiverId)) {
                    matReceiverId = headDTO.getCreateUserId();
                }
                headDTO.setMatReceiverId(matReceiverId);
                headDTO.setMatDeptId(bizReceiptOutputInfo.getMatDeptId());
                headDTO.setApplyTime(bizReceiptOutputInfo.getApplyTime());
                headDTO.setInstallSite(bizReceiptOutputInfo.getInstallSite());
                headDTO.setInstallSystem(bizReceiptOutputInfo.getInstallSystem());
                headDTO.setReceiptRemark(bizReceiptOutputInfo.getReceiptRemark());
                if (headDTO!=null  && EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(headDTO.getReceiveType())) { //需求计划领用
                    Long referReceiptHeadId = headDTO.getItemDTOList().get(0).getReferReceiptHeadId();
                    ErpReceiveReceiptHead erpReceiveReceiptHead = erpReceiveReceiptHeadDataWrap.getById(referReceiptHeadId);
                    if (UtilObject.isNotNull(erpReceiveReceiptHead)) {
                        headDTO.setErpCreateUserCode(erpReceiveReceiptHead.getErpCreateUserCode());
                    }
                }

                headDTO.setPreReceiptCode(headDTO.getItemDTOList().get(EnumRealYn.FALSE.getIntValue()).getPreReceiptCode());
                dataFillService.fillRlatAttrForDataObj(headDTO);
            }
        }
    }

    /**
     * 填充签字信息，包括实际领料人签字和保管员的签字信息
     * @param headDTO
     */
    private void fillSignature(BizReceiptOutputHeadDTO headDTO) {

        BizReceiptOutputSignatureGraph receiverSignatureObj = signatureGraphDataWrap.getOne(new QueryWrapper<BizReceiptOutputSignatureGraph>()
                .lambda()
                .eq(BizReceiptOutputSignatureGraph::getHeadId, headDTO.getId())
                .eq(BizReceiptOutputSignatureGraph::getSignatureType, EnumOutputSignatureType.ACTUAL_RECEIVER.getValue()));
        BizReceiptOutputSignatureGraphDTO receiverSignDTO = UtilBean.newInstance(receiverSignatureObj, BizReceiptOutputSignatureGraphDTO.class);
        headDTO.setMatReceiverSignature(receiverSignDTO);
        if (UtilObject.isEmpty(receiverSignatureObj) && EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(SysUser::getUserName, headDTO.getActualReceiverName());
            List<SysUser> list = sysUserDataWrap.list(queryWrapper);
            if (UtilCollection.isNotEmpty(list)) {
                Long commonImgId = list.get(0).getCommonImgId();
                BizCommonImage commonImage = commonImageDataWrap.getById(commonImgId);
                if (UtilObject.isNotEmpty(commonImage)) {
                    receiverSignDTO.setSignatureType(1);
                    receiverSignDTO.setSignatureGraph(commonImage.getImgBase64());
                }
            }
        }

        BizReceiptOutputSignatureGraph keeperSignatureObj = signatureGraphDataWrap.getOne(new QueryWrapper<BizReceiptOutputSignatureGraph>()
                .lambda()
                .eq(BizReceiptOutputSignatureGraph::getHeadId, headDTO.getId())
                .eq(BizReceiptOutputSignatureGraph::getSignatureType, EnumOutputSignatureType.STORE_KEEPER.getValue()));
        BizReceiptOutputSignatureGraphDTO keeperSignDTO = UtilBean.newInstance(keeperSignatureObj, BizReceiptOutputSignatureGraphDTO.class);
        headDTO.setStoreKeeperSignature(keeperSignDTO);
        if (UtilObject.isEmpty(keeperSignatureObj) && EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(headDTO.getReceiptStatus())) {
            SysUser sysUser = sysUserDataWrap.getById(headDTO.getStoreKeeperUserId());
            if (UtilObject.isNotEmpty(sysUser)) {
                Long commonImgId = sysUser.getCommonImgId();
                BizCommonImage commonImage = commonImageDataWrap.getById(commonImgId);
                if (UtilObject.isNotEmpty(commonImage)) {
                    keeperSignDTO.setSignatureType(2);
                    keeperSignDTO.setSignatureGraph(commonImage.getImgBase64());
                }
            }
        }
        List<BizCommonReceiptOperationLogDTO> logList = headDTO.getLogList();
        if (UtilCollection.isNotEmpty(logList)) {
            List<BizCommonReceiptOperationLogDTO> collect = logList.stream().filter(c -> c.getReceiptOperationType().equals(EnumReceiptOperationType.RECEIPT_OPERATION_POSTING.getValue())).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(collect)) {
                BizCommonReceiptOperationLogDTO logDTO = collect.get(0);
                SysUser sysUser = dictionaryService.getSysUserCacheById(logDTO.getCreateUserId());
                if (UtilObject.isNotEmpty(sysUser)) {
                    Long commonImgId = sysUser.getCommonImgId();
                    BizCommonImage commonImage = commonImageDataWrap.getById(commonImgId);
                    if (UtilObject.isNotEmpty(commonImage)) {
                        headDTO.setSubmitUserSignImg(commonImage.getImgBase64());
                        if (Objects.isNull(headDTO.getSubmitTime())) {
                            headDTO.setSubmitTime(logDTO.getCreateTime());
                        }
                    }
                }
            }
        }
    }


    /**
     * 保存单据流
     *
     * @param ctx
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            Integer receiptType=headDTO.getReceiptType();
            if(EnumReceiptType.STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ.getValue().equals(receiptType)){
                bizCommonReceiptRelation.setReceiptType(EnumReceiptType.STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ.getValue());
                bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ_APPLY.getValue());
            }else if(EnumReceiptType.UNITIZED_STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ.getValue().equals(receiptType)){
                bizCommonReceiptRelation.setReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ.getValue());
                bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ_APPLY.getValue());
            }else{
                bizCommonReceiptRelation.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
                bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ_APPLY.getValue());
            }
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptHeadId(item.getPreReceiptHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 提交校验
     *
     * @param ctx
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 参数基本校验
        outputComponent.check(ctx);

        // 出库数量是否为0校验
        outputComponent.checkItemQtyIsZero(ctx);

        // 出库数是否小于可出库数
        this.checkOperatedQty(headDTO);
        // 校验单据状态
        outputComponent.checkReceiptStatus(ctx);
    }

    /*======================================================*/

    /**
     * 设置已发货数量
     *
     * @param itemDTOList 行项目列表
     * @param user        用户
     */
    public void setSubmitQty(List<BizReceiptOutputItemDTO> itemDTOList, CurrentUser user) {

        ReserveReceiptQueryPO po = new ReserveReceiptQueryPO();
        po.setIsReturnFlag(itemDTOList.get(0).getIsReturnFlag());
        po.setPreReceiptCode(itemDTOList.get(0).getPreReceiptCode());
        List<ErpReserveReceiptItemDTO> reserveReceiptItemList = reserveReceiptService.getReserveReceiptItemList(po, user);

        for (BizReceiptOutputItemDTO item : itemDTOList) {
            for (ErpReserveReceiptItemDTO erpReserveReceiptItemDTO : reserveReceiptItemList) {
                if (item.getPreReceiptItemId().equals(erpReserveReceiptItemDTO.getId())) {
                    item.setSubmitQty(erpReserveReceiptItemDTO.getSubmitQty());
                    break;
                }
            }
        }
    }


    /**
     * 设置可用数量
     *
     * @param itemList 行项目列表
     */
    public void setAvailableQty(List<BizReceiptOutputItemDTO> itemList) {
        List<BizReceiptOutputItemDTO> qtyList = this.getWmsCreatedQtyByList(itemList);
        for (BizReceiptOutputItemDTO item : itemList) {
            BizReceiptOutputItemDTO qtyItemVo = qtyList.stream()
                    .filter(q -> q.getPreReceiptType().equals(item.getPreReceiptType())
                            && q.getPreReceiptItemId().equals(item.getPreReceiptItemId()))
                    .findFirst().orElse(new BizReceiptOutputItemDTO());
            BigDecimal createdQty = UtilObject.getBigDecimalOrZero(qtyItemVo.getCreatedQty());
            // 订单数量采用的是前续单据数量
            BigDecimal receiptQty = UtilObject.getBigDecimalOrZero(item.getPreReceiptQty());
            BigDecimal submitQty = UtilObject.getBigDecimalOrZero(item.getSubmitQty());
            // 可用数量 = SAP订单数量 - SAP已发货数量 - wms已创建但未过账的出库单数量
            BigDecimal availableQty = receiptQty.subtract(submitQty).subtract(createdQty);
            item.setAvailableQty(availableQty);
        }
    }


    /**
     * 获取行项目的wms已创建未同步SAP的数量
     *
     * @param itemList 行项目list
     * @return 数量列表
     */
    public List<BizReceiptOutputItemDTO> getWmsCreatedQtyByList(List<BizReceiptOutputItemDTO> itemList) {
        if (UtilCollection.isNotEmpty(itemList)) {
            // 查询单据状态大于10， 并且SAP物料凭证为空的，具有相同前序单号的其他的出库单的出库数
            return bizReceiptOutputHeadDataWrap.getWmsCreatedQtyByList(itemList.get(0).getHeadId(), itemList);
        } else {
            return new ArrayList<>();
        }
    }


    /**
     * 出库数是否小于可出库数
     *
     * @param headDTO
     */
    private void checkOperatedQty(BizReceiptOutputHeadDTO headDTO) {
        // 出库数量大于可出库数量时抛出异常
        this.setSubmitQty(headDTO.getItemDTOList(), null);
        this.setAvailableQty(headDTO.getItemDTOList());
        for (BizReceiptOutputItemDTO itemDTO : headDTO.getItemDTOList()) {
            BigDecimal qty = BigDecimal.ZERO;
            // 计算单个行项目的出库总数
            if (itemDTO.getAssembleDTOList() != null && itemDTO.getAssembleDTOList().size() > 0) {
                for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                    qty = qty.add(assembleDTO.getQty());
                }
            }
            if (qty.compareTo(itemDTO.getAvailableQty()) > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QUANTITY_DIFFERENCES);
            }
            // TODO-BO: 2022/5/20 这里可以将单据进行分类，分两种情况：1.配货的生成下架请求；2.没有配货的调用SAP进行关闭预留单
        }
    }

    /**
     * 获取过账移动类型并校验
     *
     * @param ctx
     */
    public void generateInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = new StockInsMoveTypePostTaskDTO();

        StockInsMoveTypeDTO postingInsMoveTypeDTO;
        try {
            postingInsMoveTypeDTO = insMatReqOutputMoveTypeComponent.generatePostingInsDoc(headDTO);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeDTO);
        // 过账凭证(处理批次库存、临时区仓位库存)
        stockInsMoveTypePostTaskDTO.setPostDTO(postingInsMoveTypeDTO);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        stockInsMoveTypePostTaskDTO.setTaskDTO(null);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    /**
     * 先过账模式-获取冲销移动类型并校验
     *
     * @param ctx
     */
    public void generateWriteOffInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = this.setWriteOffMoveType(headDTO);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    /**
     * @param headDTO
     * @return
     */
    private StockInsMoveTypePostTaskDTO setWriteOffMoveType(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO postingInsMoveTypeVo;
        StockInsMoveTypeDTO taskInsMoveTypeVo = null;
        try {
            postingInsMoveTypeVo = insMatReqOutputWriteOffMoveTypeComponent.generatePostingInsDoc(headDTO);
            List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
            boolean isGenerateTaskInsDoc = false;
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if (UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    isGenerateTaskInsDoc = true;
                    break;
                }
            }
            // 非同时模式判断是否生成task凭证
            if (isGenerateTaskInsDoc) {
                taskInsMoveTypeVo = insMatReqOutputWriteOffMoveTypeComponent.generateTaskInsDoc(headDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeVo);
        bizCommonService.setInsDocCode(taskInsMoveTypeVo);
        StockInsMoveTypePostTaskDTO dto = new StockInsMoveTypePostTaskDTO();
        // 过账凭证(处理批次库存、临时区仓位库存)
        dto.setPostDTO(postingInsMoveTypeVo);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        dto.setTaskDTO(taskInsMoveTypeVo);
        return dto;
    }

    /**
     * 非先过账模式-获取冲销移动类型并校验
     *
     * @param ctx 上下文
     */
    public void generateWriteOffInsMoveTypeAndCheckNonPostFirst(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = this.setWriteOffMoveTypeNonPostFirst(headDTO);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    private StockInsMoveTypePostTaskDTO setWriteOffMoveTypeNonPostFirst(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO postingInsMoveTypeVo;
        StockInsMoveTypeDTO taskInsMoveTypeVo = null;
        try {
            postingInsMoveTypeVo = insMatReqOutputWriteOffMoveTypeComponent.generatePostingInsDocNonPostFirst(headDTO);
            List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
            boolean isGenerateTaskInsDoc = false;
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if (UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    isGenerateTaskInsDoc = true;
                    break;
                }
            }
            // 非同时模式判断是否生成task凭证
            if (isGenerateTaskInsDoc) {
                taskInsMoveTypeVo = insMatReqOutputWriteOffMoveTypeComponent.generateTaskInsDoc(headDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeVo);
        bizCommonService.setInsDocCode(taskInsMoveTypeVo);
        StockInsMoveTypePostTaskDTO dto = new StockInsMoveTypePostTaskDTO();
        // 过账凭证(处理批次库存、临时区仓位库存)
        dto.setPostDTO(postingInsMoveTypeVo);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        dto.setTaskDTO(taskInsMoveTypeVo);
        return dto;
    }

    /**
     * 校验删除
     *
     * @param ctx
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 判断出库单是否已同步SAP
        boolean syncStatus = outputComponent.getSapPostSyncStatus(id);
        if (syncStatus) {
            log.warn("出库单{}删除状态校验，失败，出库单已同步SAP，存在SAP过账物料凭证", id);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
    }


    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue(), headId);
    }

    /**
     * 调用sap关闭预留信息，全部关闭
     */
    public void closeReservationList(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        ErpReturnObject returnObj;
        // 过滤有sap凭证的行项目
        List<BizReceiptOutputItemDTO> syncList = headDTO.getItemDTOList().stream()
                .filter(item -> StringUtils.hasText(item.getMatDocCode())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(syncList)) {
            log.info("关闭预留信息不存在SAP凭证信息,该行项目为:{};", syncList);
            return;
        }
        headDTO.setItemDTOList(syncList);
        // 过滤后重新设置上下文-headDTO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        returnObj = reserveReceiptService.erpCloseReservation(
                JSONArray.toJSONStringWithDateFormat(syncList, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
        log.info("需要关闭预留的信息：{}", syncList);
        log.info("关闭预留返回的信息：{}", JSON.toJSONString(returnObj));
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {

        } else {
            log.error("关闭-删除预留失败,参数信息为:{}", JSON.toJSONString(syncList));
        }
    }

    /**
     * sap过账
     *
     * @param ctx 上下文
     */
    public void postToSap(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.setReceiveInfo(ctx); //设置出库单对应领料单信息
        CurrentUser currentUser = ctx.getCurrentUser();
        Set<Integer> statusSet = new HashSet<>();
        statusSet.add(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
        statusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        statusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        // 过滤没有sap凭证的行项目
        List<BizReceiptOutputItemDTO> syncList = headDTO.getItemDTOList().stream()
                .filter(item -> statusSet.contains(item.getItemStatus()) && !StringUtils.hasText(item.getMatDocCode())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(syncList)) {
            return;
        }
        headDTO.setItemDTOList(syncList);
        // 过滤后重新设置上下文-headDTO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        /* ******** 设置过账账期 ******** */
        this.setInPostDate(headDTO, currentUser);

        /* ******** 设置领料单领用类型 ******** */
        if (headDTO.getItemDTOList().size() > 0) {
            Long preReceiptHeadId = headDTO.getItemDTOList().get(0).getPreReceiptHeadId();
            if(UtilNumber.isNotEmpty(preReceiptHeadId)){
                // 根据前续单据id查询申请表的领料信息
                BizReceiptApplyHead receiptApplyHead = bizReceiptApplyHeadDataWrap.getById(preReceiptHeadId);
                BizReceiptApplyHeadDTO applyHeadDTO = UtilBean.newInstance(receiptApplyHead, BizReceiptApplyHeadDTO.class);
                headDTO.setReceiveType(applyHeadDTO.getReceiveType()); // 领用类型 1 手工领用 2需求计划领用
            } else if(UtilNumber.isNotEmpty(headDTO.getCostCenterId())){
                headDTO.setReceiveType(1); // 领用类型 1 手工领用 2需求计划领用
            } else if(UtilNumber.isNotEmpty(headDTO.getWbsId())){
                headDTO.setReceiveType(2); // 领用类型 1 手工领用 2需求计划领用
            }
        }

        HXPostingHeader header = this.materialOutputPosting(headDTO, syncList, false);
        HXPostingReturn returnObj = hxInterfaceService.posting(header,false);
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
//            // 更新冲销物料凭证号
//            List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
//            if (UtilCollection.isNotEmpty(returnObjectItems)) {
//                for (BizReceiptOutputItemDTO itemDTO : headDTO.getItemDTOList()) {
//                    ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
//                            .filter(item -> item.getReceiptCode().equals(itemDTO.getReceiptCode())
//                                    && item.getReceiptRid().equals(itemDTO.getRid()))
//                            .findFirst().orElse(null);
//                    if (null == currentReturnObject) {
//                        continue;
//                    }
//                    itemDTO.setWriteOffMatDocCode(currentReturnObject.getMatDocCode());
//                    itemDTO.setWriteOffMatDocRid(currentReturnObject.getMatDocRid());
//                    itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
//                    itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
//                }
//                // 更新行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
//                bizReceiptOutputItemDataWrap.updateBatchDtoById(headDTO.getItemDTOList());
//            }
            // 更新过账时间和过账标识
            this.updatePostingDateAndIsPost(ctx);
            // 修改出库单SAP物料凭证字段
            this.updateItemAfterSyncSap(syncList, returnObj);
            // 更新单据行项目状态已记账
            this.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            List<Long> itemIdList = syncList.stream().map(BizReceiptOutputItemDTO::getId).collect(Collectors.toList());
            log.debug("出库单{}行项目{}过账同步SAP成功", headDTO.getReceiptCode(), itemIdList);
        } else {
            log.warn("出库单{}过账同步SAP失败", headDTO.getReceiptCode());
            // 失败时，更新出库单及行项目为【未同步】状态
            if (!checkCompletedStatus(headDTO)) {
                this.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            }
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    /**
     * 领料出库单过账或者冲销入参
     */
    private HXPostingHeader materialOutputPosting(BizReceiptOutputHeadDTO headDTO, List<BizReceiptOutputItemDTO> itemList, boolean isWriteOff) {

        HXPostingHeader header = new HXPostingHeader();
        header.setReceiptId(headDTO.getId());
        header.setReceiptCode(headDTO.getReceiptCode());
        header.setReceiptType(headDTO.getReceiptType());
        header.setReceiveType(headDTO.getReceiveType());
        if (isWriteOff) {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffDocDate()));
        } else {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getDocDate()));
        }

        List<HXPostingItem> items = itemList.stream().map(output -> {
            HXPostingItem item = new HXPostingItem();
            item.setReceiptRid(output.getRid());
            item.setFtyCode(output.getFtyCode());
            item.setLocationCode1(output.getLocationCode());
            item.setMatCode(output.getMatCode());
            item.setQty(UtilBigDecimal.getString(output.getQty()));
            item.setUnitCode(output.getUnitCode());

            if (!isWriteOff && (1 == headDTO.getReceiveType() || 3 == headDTO.getReceiveType())) {
                // 过账201
                item.setMoveType(EnumMoveType.TYPE_COST_CENTER_PICKING.getValue());
            }
            if (isWriteOff && (1 == headDTO.getReceiveType() || 3 == headDTO.getReceiveType())) {
                // 冲销202
                item.setMoveType(EnumMoveType.TYPE_COST_CENTER_RETURN.getValue());
            }
            if (!isWriteOff && 2 == headDTO.getReceiveType()) {
                // 过账221
                item.setMoveType(EnumMoveType.TYPE_COST_WBS_PICKING.getValue());
            }
            if (isWriteOff && 2 == headDTO.getReceiveType()) {
                // 冲销222
                item.setMoveType(EnumMoveType.TYPE_COST_WBS_RETURN.getValue());
            }
            if (!isWriteOff && 4 == headDTO.getReceiveType()) {
                // 资产领用过账241
                item.setMoveType(EnumMoveType.TYPE_ASSET.getValue());
            }
            if (isWriteOff && 4 == headDTO.getReceiveType()) {
                // 资产领用-冲销242
                item.setMoveType(EnumMoveType.TYPE_ASSET_WRITE_OFF.getValue());
            }
            if (isWriteOff) {
                item.setMatDocCode(output.getMatDocCode());
                item.setMatDocYear(output.getMatDocYear());
                item.setReceiptRid(output.getMatDocRid());
            }

            item.setCostCenter(UtilString.isNullOrEmpty(headDTO.getCostCenterCode()) ? "" : headDTO.getCostCenterCode());
            item.setWbsCode(UtilString.isNullOrEmpty(headDTO.getWbsCode()) ? "" : headDTO.getWbsCode());
            item.setAssetCode(UtilString.isNullOrEmpty(headDTO.getAssetCode()) ? "" : headDTO.getAssetCode());
            item.setAssetSubCode(UtilString.isNullOrEmpty(headDTO.getAssetSubCode()) ? "" : headDTO.getAssetSubCode());

            // B10817 【UAT】【领料出库】出库过账接口需要将备注字段传给SAP
            item.setRemark(UtilString.isNullOrEmpty(output.getItemRemark()) ? "" : output.getItemRemark());

            return item;
        }).collect(Collectors.toList());

        header.setItems(items);

        return header;
    }

    public boolean checkCompletedStatus(BizReceiptOutputHeadDTO headDTO) {
        return bizReceiptOutputHeadDataWrap.getById(headDTO.getId()).getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }


    /**
     * 设置出库单对应领料单信息
     *
     * @param ctx 上下文
     */
    public void setReceiveInfo(BizContext ctx) {
        // 获取上下文
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemList = headDTO.getItemDTOList();
        Long preReceiptHeadId = itemList.get(0).getPreReceiptHeadId();
        // 根据前续单据id查询申请表的领料信息
        BizReceiptApplyHead receiptApplyHead = bizReceiptApplyHeadDataWrap.getById(preReceiptHeadId);
        if (receiptApplyHead!=null  && EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(receiptApplyHead.getReceiveType())) {//需求计划领用
            QueryWrapper<ErpReceiveReceiptItem> receiveReceiptItemQueryWrapper = new QueryWrapper<>();
            receiveReceiptItemQueryWrapper.lambda().in(ErpReceiveReceiptItem::getId, itemList.stream().map(p -> p.getReferReceiptItemId()).collect(Collectors.toList()));
            List<ErpReceiveReceiptItemDTO> erpReceiveReceiptItemDTOList = UtilCollection.toList(erpReceiveReceiptItemDataWrap.list(receiveReceiptItemQueryWrapper), ErpReceiveReceiptItemDTO.class);
            dataFillService.fillRlatAttrDataList(erpReceiveReceiptItemDTOList);
            for (BizReceiptOutputItemDTO outputItemDTO : itemList) {
                erpReceiveReceiptItemDTOList.forEach(receiveReceiptItemDTO -> {
                    if(outputItemDTO.getReferReceiptItemId().equals(receiveReceiptItemDTO.getId())) {
                        outputItemDTO.setReceiveReceiptItemDTO(receiveReceiptItemDTO);
                    }
                });
                outputItemDTO.setReceiptNum(headDTO.getReceiptNum()); //传入领用单号
                outputItemDTO.setActReceiveUserName(headDTO.getActualReceiverName());//实际领料人
                outputItemDTO.setSendMatUserName(headDTO.getSendMatUserName());//发料人
            }
        }
    }

    /**
     * 更新过账时间和过账标识
     *
     * @param ctx 上下文
     */
    public void updatePostingDateAndIsPost(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypePostTaskDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        Date postingDate = itemDTOList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        Date now = UtilDate.getNow();
        for (BizReceiptOutputItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setDocDate(now);
            outputItemDTO.setPostingDate(postingDate);
            outputItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
        }
        // 补全凭证的过账日期、凭证日期
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            for (StockInsDocBatch stockInsDocBatch : insMoveTypeDTO.getPostDTO().getInsDocBatchList()) {
                stockInsDocBatch.setDocDate(now);
                stockInsDocBatch.setPostingDate(postingDate);
            }
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 过账成功，修改出库单SAP物料凭证字段,年,凭证行号,凭证时间
     *
     * @param itemDTOList 行项目列表
     * @param returnObj      sap返回结果
     */
    public void updateItemAfterSyncSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn returnObj) {
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(returnObj.getMatDocCode());
            itemDTO.setMatDocRid(itemDTO.getRid());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
    }
    /**
     * 过账成功，修改出库单bin表金额
     *
     * @param itemDTOList 行项目列表
     * @param retObj      sap返回结果
     */
    public void updateBinAfterSyncSap(List<BizReceiptOutputItemDTO> itemDTOList, ErpReturnObject retObj) {
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            List<BizReceiptOutputBinDTO> binDTOList = itemDTO.getBinDTOList();
            List<ErpReturnObjectItem> returnItemList = retObj.getReturnItemList();
            for (int i = 0; i < binDTOList.size(); i++) {
                binDTOList.get(i).setDmbtr(returnItemList.get(i).getDmbtr());
            }
            bizReceiptOutputBinDataWrap.updateBatchDtoById(binDTOList);
        }
    }

    /**
     * 更新单据,行项目状态 如不更新单据状态，headDTO参数传null
     *
     * @param headDTO     headDTO
     * @param itemDTOList 行项目列表
     * @param status      状态
     */
    public void updateStatus(BizReceiptOutputHeadDTO headDTO, List<BizReceiptOutputItemDTO> itemDTOList,
                             Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateReceiptStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateItemStatus(itemDTOList, status);
            this.updateReceiptStatus(headDTO, status);
        }
    }


    /**
     * 更新行项目状态
     *
     * @param itemDTOList 行项目列表
     * @param status      状态
     */
    public void updateItemStatus(List<BizReceiptOutputItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(item -> item.setItemStatus(status));
            bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }

    /**
     * 更新单据状态
     *
     * @param headDTO       headDTO
     * @param receiptStatus 单据状态
     */
    public void updateReceiptStatus(BizReceiptOutputHeadDTO headDTO, Integer receiptStatus) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(receiptStatus);
            bizReceiptOutputHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param headDTO 单据行项目
     * @param user        当前用户
     */
    private void setInPostDate(BizReceiptOutputHeadDTO headDTO, CurrentUser user) {
        if (UtilObject.isEmpty(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        Date postingDate = headDTO.getPostingDate();
        Date writeOffPostingDate = itemDTOList.get(0).getWriteOffPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        if (UtilObject.isNull(writeOffPostingDate)) {
            writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
        for (BizReceiptOutputItemDTO inspectItemDTO : itemDTOList) {
            if (EnumRealYn.FALSE.getIntValue().equals(inspectItemDTO.getIsWriteOff())) {
                inspectItemDTO.setDocDate(UtilDate.getNow());
                inspectItemDTO.setPostingDate(postingDate);
            } else {
                inspectItemDTO.setWriteOffDocDate(UtilDate.getNow());
                inspectItemDTO.setWriteOffPostingDate(writeOffPostingDate);
            }
        }
    }


    /**
     * sap冲销
     *
     * @param ctx 上下文
     */
    public void writeOffToSap(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        itemDTOList.forEach(x -> x.setIsWriteOff(1));
        itemDTOList.forEach(item->{
            // 设置冲销标识。
            item.setIsWriteOff(1);
        });
        this.setInPostDate(headDTO,ctx.getCurrentUser());

        HXPostingHeader header = this.materialOutputPosting(headDTO, itemDTOList, true);

        HXPostingReturn returnObj = hxInterfaceService.writeOff(header,true);

        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 更新单据行项目状态冲销
            this.updateStatus(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            List<Long> itemIdList = itemDTOList.stream().map(BizReceiptOutputItemDTO::getId).collect(Collectors.toList());

            // 更新冲销过账时间和冲销标识
            this.updateWriteOffPostingDateAndIsWriteOff(ctx);
            // 冲销成功，修改出库单SAP物料凭证字段
            // 调用SAP
            if (UtilConst.getInstance().isErpSyncMode()) {
                this.updateItemAfterWriteOffSap(itemDTOList, returnObj);
            }else {
                this.updateItemAfterWriteOffNoSap(itemDTOList, returnObj);
            }
            log.debug("出库单{}行项目{}冲销同步SAP成功", headDTO.getReceiptCode(), itemIdList);

        } else {
            log.warn("出库单{}冲销同步SAP失败，返回信息：{}", headDTO.getId(), returnObj.getReturnMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }


    /**
     * 冲销成功，修改出库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param returnObj      sap返回结果
     */
    public void updateItemAfterWriteOffSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn returnObj) {
        List<BizReceiptOutputBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(returnObj.getMatDocCode());
            itemDTO.setWriteOffMatDocRid(UtilObject.getStringOrEmpty(itemDTO.getRid()));
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptOutputBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 冲销成功，修改出库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj      sap返回结果
     */
    public void updateItemAfterWriteOffNoSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn retObj) {
        int matDocRid = 1;
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(retObj.getMatDocCode());
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setWriteOffMatDocRid(matDocRidStr);
            itemDTO.setWriteOffDocDate(new Date());
            matDocRid++;
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
    }



    /**
     * 更新冲销过账时间
     *
     * @param ctx 上下文
     */
    public void updateWriteOffPostingDateAndIsWriteOff(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypePostTaskDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        // 校验账期
        Date postingDate = itemDTOList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        Date now = UtilDate.getNow();
        for (BizReceiptOutputItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setWriteOffDocDate(now);
            outputItemDTO.setWriteOffPostingDate(postingDate);
            outputItemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
        }
        // 补全凭证的冲销过账日期、冲销凭证日期
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            for (StockInsDocBatch stockInsDocBatch : insMoveTypeDTO.getPostDTO().getInsDocBatchList()) {
                stockInsDocBatch.setDocDate(now);
                stockInsDocBatch.setPostingDate(postingDate);
            }
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 保存签名
     *
     * @param ctx
     */
    public void saveAutograph(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long id = headDTO.getId();
        if (UtilNumber.isNotEmpty(id)) {
            if (UtilObject.isNotNull(headDTO.getMatReceiverSignature())
                && UtilString.isNotNullOrEmpty(headDTO.getMatReceiverSignature().getSignatureGraph())) {

                signatureGraphDataWrap.physicalDelete(new QueryWrapper<BizReceiptOutputSignatureGraph>()
                    .lambda()
                    .eq(BizReceiptOutputSignatureGraph::getHeadId, headDTO.getId())
                    .eq(BizReceiptOutputSignatureGraph::getSignatureType, EnumOutputSignatureType.ACTUAL_RECEIVER.getValue()));

                BizReceiptOutputSignatureGraph ms = new BizReceiptOutputSignatureGraph();
                ms.setHeadId(headDTO.getId());
                ms.setSignatureType(EnumOutputSignatureType.ACTUAL_RECEIVER.getValue());
                ms.setSignatureGraph(headDTO.getMatReceiverSignature().getSignatureGraph());
                signatureGraphDataWrap.save(ms);

                log.debug("出库单{},领料人签名信息更改为:{}", headDTO.getReceiptCode(), headDTO.getMatReceiverSignature().getSignatureGraph());
            } else {
                log.debug("领料人签名信息为空:{}", headDTO.getAutograph());
            }

            if (UtilObject.isNotNull(headDTO.getStoreKeeperSignature())
                    && UtilString.isNotNullOrEmpty(headDTO.getStoreKeeperSignature().getSignatureGraph())) {

                signatureGraphDataWrap.physicalDelete(new QueryWrapper<BizReceiptOutputSignatureGraph>()
                        .lambda()
                        .eq(BizReceiptOutputSignatureGraph::getHeadId, headDTO.getId())
                        .eq(BizReceiptOutputSignatureGraph::getSignatureType, EnumOutputSignatureType.STORE_KEEPER.getValue()));

                BizReceiptOutputSignatureGraph ss = new BizReceiptOutputSignatureGraph();
                ss.setHeadId(headDTO.getId());
                ss.setSignatureType(EnumOutputSignatureType.STORE_KEEPER.getValue());
                ss.setSignatureGraph(headDTO.getStoreKeeperSignature().getSignatureGraph());
                signatureGraphDataWrap.save(ss);

                log.debug("出库单{},保管员签名信息更改为:{}", headDTO.getReceiptCode(), headDTO.getStoreKeeperSignature().getSignatureGraph());
            } else {
                log.debug("保管员签名信息为空:{}", headDTO.getAutograph());
            }
        }
    }


    /*=============================================================*/

    /**
     * 获取按钮权限
     *
     * @param headId 单据id
     * @return 按钮设置
     */
    public ButtonVO setButton(Long headId,BizReceiptOutputHeadDTO headDTO) {
        ButtonVO button = new ButtonVO();
        QueryWrapper<BizReceiptOutputHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputHead::getId, headId).eq(BizReceiptOutputHead::getIsDelete, 0);
        BizReceiptOutputHead one = bizReceiptOutputHeadDataWrap.getOne(queryWrapper);
        button.setButtonSave(this.setButtonSave(headId, one));
        button.setButtonSubmit(this.setButtonSubmit(headId, one));
        button.setButtonPost(this.setButtonPost(headId, one));
        button.setButtonRevoke(this.setButtonRevoke(headId, one));
        button.setButtonDebtOffset(this.setButtonDebtOffset(headId, one));
        button.setButtonPrint(this.setButtonPrint(headId,one));
        button.setButtonWriteOff(this.setButtonWriteOff(headId,one));
        if (headDTO!=null  && EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(headDTO.getReceiveType())) { //需求计划领用
            button.setButtonCloseOnly(this.setButtonClose(headId,one));
        }else{
//            button.setButtonWriteOff(this.setButtonWriteOff(headId,one));
//            button.setButtonClose(this.setButtonClose(headId,one));
        }
        button.setButtonDeal(this.setButtonDeal(headDTO));
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(one.getReceiptStatus())) {
            // 草稿状态
            button.setButtonDelete(true);
        }
        return button;
    }

    /**
     * 出库单能否保存
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonSave(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }


    /**
     * 出库单能否需要处理
     *
     * @param headDTO
     * @return 0 不能、1能
     */
    public Boolean setButtonDeal( BizReceiptOutputHeadDTO headDTO) {
        if (Objects.isNull(headDTO)) {
            return false;
        }
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            List<BizReceiptOutputItemDTO> itemList=headDTO.getItemDTOList();
            for(BizReceiptOutputItemDTO item:itemList){
                if(item.getQty().compareTo(BigDecimal.ZERO) == 0){
                    return true;
                }
            }

        }
        return false;
    }


    /**
     * 出库单能否删除
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonDelete(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 石岛湾订制化需求 采购退货草稿状态不允许删除
            // 草稿状态 可以删除
            return !EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue().equals(one.getReceiptType());
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 驳回状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 单据已提交状态，且没有开始作业的，允许删除
            byte taskStatus = this.getReceiptTaskStatus(headId);
            return EnumReceiptTaskStatus.NOT_STARTED.getValue().equals(taskStatus);
        }
        return false;
    }

    /**
     * 出库单能否提交 0否、1是
     *
     * @param headId 单据id
     * @return 出库单能否提交 0否、1是
     */
    public Boolean setButtonSubmit(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否未同步再次过账 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonPost(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步状态
            return true;
        }
//        if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
//            return true;
//        }
        if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            return true;
        }
        // 已作业状态
        return EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否显示冲销按钮 0否、1是
     *
     * @param headId 单据id
     * @return 出库单能否冲销 0否、1是
     */
    public Boolean setButtonWriteOff(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        // 单据当前状态
        Integer receiptStatus = one.getReceiptStatus();
        QueryWrapper<BizReceiptOutputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptOutputItem::getHeadId, headId);
        List<BizReceiptOutputItem> itemList = bizReceiptOutputItemDataWrap.list(queryWrapper);
        // 未同步状态和已完成
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 如果存在任意一个行项目未冲销，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptOutputItem item : itemList) {
                if (EnumRealYn.FALSE.getIntValue().equals(item.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中
            // 如果存在任意一个行项目已过账，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptOutputItem item : itemList) {
                if (EnumRealYn.TRUE.getIntValue().equals(item.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        }
        return false;
    }

    /**
     * 出库单能否撤销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonRevoke(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        // 待核销状态
        return EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus);
    }

    /**
     * 出库单能否核销 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonDebtOffset(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        // 待核销状态
        return EnumReceiptStatus.RECEIPT_STATUS_PENDING_DEBT_OFFSET.getValue().equals(receiptStatus);
    }
    /**
     * 出库单能否打印
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonPrint(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        // 已完成状态
        return EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus);
    }

    private Boolean setButtonClose(Long headId, BizReceiptOutputHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 草稿状态
        return EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus);
    }

    /**
     * 获取出库单作业状态
     *
     * @param headId 单据id
     * @return 0未作业, 1作业中, 2已作业
     */
    public byte getReceiptTaskStatus(Long headId) {
        // 单据作业状态，判断行项目的taskQty
        List<BizReceiptOutputItemDTO> itemList = this.getItemListByIdNoDataFill(headId).getItemDTOList();
        boolean allDone = true;
        boolean started = false;
        for (BizReceiptOutputItemDTO item : itemList) {
            BigDecimal taskQty = item.getTaskQty();
            // taskQty大于0
            if (taskQty.compareTo(BigDecimal.ZERO) > 0) {
                started = true;
            }
            // 行项目的出库数量总数
            BigDecimal totalOperatedQty = BigDecimal.ZERO;
            if (UtilCollection.isNotEmpty(item.getBinDTOList())) {
                for (BizReceiptOutputBinDTO bin : item.getBinDTOList()) {
                    totalOperatedQty = totalOperatedQty.add(bin.getQty());
                }
            }
            // 作业数与出库总数相等且不为0，代表行项目已作业
            boolean tasked = !totalOperatedQty.equals(BigDecimal.ZERO) && taskQty.compareTo(totalOperatedQty) == 0;
            if (!tasked) {
                // 存在任意一个行项目不是已作业状态，则修改allDone标识为false
                allDone = false;
            }
        }
        if (!started) {
            // 所有行项目taskQty都不大于0，未开始
            return EnumReceiptTaskStatus.NOT_STARTED.getValue();
        } else {
            if (allDone) {
                // 所有行项目作业数与出库总数相等，已完成作业
                return EnumReceiptTaskStatus.DONE.getValue();
            } else {
                // 任意一个行项目作业数与出库总数不相等，但作业数大于0，作业中
                return EnumReceiptTaskStatus.IN_PROGRESS.getValue();
            }
        }
    }

    /**
     * 根据headId查询出库单列表(不填充)
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptOutputHeadDTO getItemListByIdNoDataFill(Long headId) {
        BizReceiptOutputHead bizReceiptOutputHead = bizReceiptOutputHeadDataWrap.getById(headId);
        List<BizReceiptOutputItem> itemList =
                bizReceiptOutputItemDataWrap.list(new LambdaQueryWrapper<BizReceiptOutputItem>() {

                    {
                        eq(BizReceiptOutputItem::getHeadId, headId);
                    }
                });
        List<BizReceiptOutputBin> binList =
                bizReceiptOutputBinDataWrap.list(new LambdaQueryWrapper<BizReceiptOutputBin>() {

                    {
                        eq(BizReceiptOutputBin::getHeadId, headId);
                        in(BizReceiptOutputBin::getItemId,
                                itemList.stream().map(BizReceiptOutputItem::getId).collect(Collectors.toList()));
                    }
                });
        BizReceiptOutputHeadDTO headDTO = UtilBean.newInstance(bizReceiptOutputHead, BizReceiptOutputHeadDTO.class);
        List<BizReceiptOutputItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptOutputItemDTO.class);
        List<BizReceiptOutputBinDTO> binDTOList = UtilCollection.toList(binList, BizReceiptOutputBinDTO.class);
        Map<Long, List<BizReceiptOutputBinDTO>> map =
                binDTOList.stream().collect(Collectors.groupingBy(BizReceiptOutputBinDTO::getItemId));
        for (BizReceiptOutputItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setBinDTOList(map.get(outputItemDTO.getId()));
        }
        headDTO.setItemDTOList(itemDTOList);
        return headDTO;
    }

    /**
     * 提交单据【非同时模式】
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitReceipt(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        this.saveReceipt(ctx);
    }


    /**
     * 保存单据【非同时模式】
     *
     * @param ctx 上下文
     */
    public void saveReceipt(BizContext ctx) {
        boolean isNew = true;
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        Long id = headDTO.getId();
        String code = headDTO.getReceiptCode();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptOutputHead bizReceiptOutputHead = null;
        // head处理
        if (UtilNumber.isNotEmpty(id)) {
            bizReceiptOutputHead = bizReceiptOutputHeadDataWrap.getById(id);
            // 根据id更新
            bizReceiptOutputHeadDataWrap.updateDtoById(headDTO);
            // item物理删除
            QueryWrapper<BizReceiptOutputItem> queryWrapperItem = new QueryWrapper<>();
            queryWrapperItem.lambda().eq(BizReceiptOutputItem::getHeadId, id);
            bizReceiptOutputItemDataWrap.physicalDelete(queryWrapperItem);
            // assemble物理删除
            QueryWrapper<BizReceiptAssemble> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizReceiptAssemble::getReceiptHeadId, id);
            bizReceiptAssembleDataWrap.physicalDelete(queryWrapperAssemble);
            // 签字信息
            QueryWrapper<BizReceiptOutputSignatureGraph> qwSignature = new QueryWrapper<>();
            qwSignature.lambda().eq(BizReceiptOutputSignatureGraph::getHeadId, id);
            signatureGraphDataWrap.physicalDelete(qwSignature);

            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            Long ftyId = headDTO.getItemDTOList().get(0).getFtyId();
            String ftyCode = dictionaryService.getFtyCacheById(ftyId).getFtyCode();
            Integer receiptType=headDTO.getReceiptType();
            if(EnumReceiptType.STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ.getValue().equals(receiptType)){ //退转库领料出库单
                code = bizCommonService.getNextSeqMaterialOutTransferReturn(user, EnumSequenceCode.SEQUENCE_TRANSFER_RETURN_MATERIAL_OUT.getValue(), ftyCode);
            }else if(EnumReceiptType.UNITIZED_STOCK_OUTPUT_RETURN_TRANSFER_MAT_REQ.getValue().equals(receiptType)){ //退转库领料出库单
                code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSFER_RETURN_OUTPUT.getValue());
            }else{
//                if(dictionaryService.getCorpCacheById(user.getCorpId()).getCorpCode().equals(Const.HL_59C0)){
//                    // HL-5ST-RCBD0-CKYYZZZZ
//                    code = "HL-5ST-RCBD0-CK" + bizCommonService.getNextSequenceYear(EnumSequenceCode.MATERIAL_OUT.getValue());
//
//                } else {
//                    code = bizCommonService.getNextSeqMaterialOut(user, EnumSequenceCode.MATERIAL_OUT.getValue(), ftyCode);
//                }
                code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.MATERIAL_OUT.getValue());
            }

            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(createUserId);
            headDTO.setModifyUserId(createUserId);
            headDTO.setReceiptType(receiptType);
            headDTO.setReceiptStatus(status);
            headDTO.setCreateTime(createTime);
            bizReceiptOutputHeadDataWrap.saveDto(headDTO);

            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptOutputHead.getReceiptStatus();
            createTime = bizReceiptOutputHead.getCreateTime();
            createUserId = bizReceiptOutputHead.getCreateUserId();
        }
        // item处理
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        int rid = 1;
        for (BizReceiptOutputItemDTO itemDto : itemDTOList) {
            itemDto.setRid(Integer.toString(rid++));
            itemDto.setId(null);
            itemDto.setHeadId(headDTO.getId());
            itemDto.setItemStatus(status);
            itemDto.setCreateTime(createTime);
            itemDto.setCreateUserId(createUserId);
            itemDto.setModifyUserId(user.getId());
            itemDto.setQty(this.getItemOperatedQty(itemDto));
        }
        bizReceiptOutputItemDataWrap.saveBatchDto(itemDTOList);
        // assemble处理
        List<BizReceiptAssembleDTO> assembleList = new ArrayList<>();
        for (BizReceiptOutputItemDTO itemDto : itemDTOList) {
            for (BizReceiptAssembleDTO assembleDTO : itemDto.getAssembleDTOList()) {
                assembleDTO.setId(null);
                assembleDTO.setReceiptHeadId(headDTO.getId());
                assembleDTO.setReceiptItemId(itemDto.getId());
                assembleDTO.setReceiptType(headDTO.getReceiptType());
                assembleDTO.setCreateUserId(createUserId);
                assembleDTO.setModifyUserId(user.getId());
                assembleDTO.setSpecType(assembleDTO.getSpecType() == null
                        ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue() : assembleDTO.getSpecType());
                assembleList.add(assembleDTO);
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleList);

        // 签名信息处理
        if (UtilObject.isNotNull(headDTO.getMatReceiverSignature())
            && UtilString.isNotNullOrEmpty(headDTO.getMatReceiverSignature().getSignatureGraph())) {
            // 实际领料人签名信息
            BizReceiptOutputSignatureGraphDTO receiverSignature = headDTO.getMatReceiverSignature();
            receiverSignature.setId(null);
            receiverSignature.setHeadId(headDTO.getId());
            receiverSignature.setSignatureType(EnumOutputSignatureType.ACTUAL_RECEIVER.getValue());
            signatureGraphDataWrap.saveDto(receiverSignature);
        }
        if (UtilObject.isNotNull(headDTO.getStoreKeeperSignature())
            && UtilString.isNotNullOrEmpty(headDTO.getStoreKeeperSignature().getSignatureGraph())) {
            // 保管员签名信息
            BizReceiptOutputSignatureGraphDTO keeperSignature = headDTO.getStoreKeeperSignature();
            keeperSignature.setId(null);
            keeperSignature.setHeadId(headDTO.getId());
            keeperSignature.setSignatureType(EnumOutputSignatureType.STORE_KEEPER.getValue());
            signatureGraphDataWrap.saveDto(keeperSignature);
        }

        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, headDTO.getId());
    }

    /**
     * 获取行项目出库数
     *
     * @param itemDTO 行项目
     * @return 出库数量
     */
    public BigDecimal getItemOperatedQty(BizReceiptOutputItemDTO itemDTO) {
        BigDecimal operatedQty = BigDecimal.ZERO;
        if (Objects.nonNull(itemDTO) && itemDTO.getAssembleDTOList() != null) {
            for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                if (assembleDTO.getQty() != null && assembleDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    operatedQty = operatedQty.add(assembleDTO.getQty());
                }
            }
        }
        return operatedQty;
    }

    /**
     * 更新批次维保日期
     *
     * @param ctx 上下文
     */
    public void updateBatchMaintenanceDate(BizContext ctx) {
        // 入参上下文
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 获取出库配货批次信息
        List<Long> batchIdList = new ArrayList<>();
        headDTO.getItemDTOList().forEach(p -> batchIdList.addAll(p.getBinDTOList().stream().map(q -> q.getBatchId()).collect(Collectors.toList())));
        List<BizBatchInfoDTO> batchInfoDTOList = batchInfoService.getBatchInfoList(batchIdList);
        // 更新批次维保日期
        headDTO.getItemDTOList().forEach(p -> {
            p.getBinDTOList().forEach(m -> {
                batchInfoDTOList.forEach(n -> {
                    if(m.getBatchId().equals(n.getId())) {
                        n.setMaintenanceDate(p.getWriteOffDocDate());
                    }
                });
            });
        });
        batchInfoService.multiUpdateBatchInfo(batchInfoDTOList);
    }

    /**
     * 调用sap关闭预留信息，全部关闭
     */
    public void closeReservationListNew(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        // 过滤有sap凭证的行项目
        List<BizReceiptOutputItemDTO> syncList = headDTO.getItemDTOList().stream()
                .filter(item -> !StringUtils.hasText(item.getMatDocCode())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(syncList)) {
            log.info("关闭预留信息不存在SAP凭证信息,该行项目为:{};", syncList);
            return;
        }
        headDTO.setItemDTOList(syncList);
        // 过滤后重新设置上下文-headDTO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        String receiptCode = headDTO.getReceiptCode();
        Map<Long, List<BizReceiptOutputItemDTO>> reservedMap = itemDTOList.stream().collect(Collectors.groupingBy(BizReceiptOutputItemDTO::getReservedOrderCode));
        for (Long reservedOrderCode : reservedMap.keySet()) {
            List<BizReceiptOutputItemDTO> itemList = reservedMap.get(reservedOrderCode);
            ErpReturnObject returnObj = reserveReceiptService.erpCloseReservation(JSONArray.toJSONStringWithDateFormat(itemList, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
            log.info("单据{}需要关闭预留的信息：{}", receiptCode, reservedOrderCode);
            log.info("关闭预留返回的信息：{}", JSON.toJSONString(returnObj));
            if (!Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                log.error("单据{}关闭-删除预留失败,参数信息为:{}", receiptCode, reservedOrderCode);
                throw new WmsException(EnumReturnMsg.CLOSE_RESERVE_RECEIPT_FAIL);
            }
        }
    }

    public void rollbackUnLoadTask(BizContext ctx) {
        BizReceiptTaskItem taskItem = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskReqItem taskReqItem = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        Long outputHeadId = taskReqItem.getPreReceiptHeadId();
        Long outputItemId = taskReqItem.getPreReceiptItemId();
        Long taskItemId = taskItem.getId();
        BigDecimal rollbackQty = taskItem.getQty();
        // 撤回作业单对应的BizReceiptOutputBin(TaskItemId)和BizLabelReceiptRel(ReceiptBinId)
        // 撤回BizReceiptOutputItem对应的TaskQty和FinishQty, 减掉taskItem的qty
        // 撤回出库单和行项目的状态, 未同步、已作业->作业中, 作业中->作业中、已提交(taskQty还原后是0即状态为已提交)
        BizReceiptOutputBin outputBin = bizReceiptOutputBinDataWrap.rollbackTaskItem(outputHeadId, outputItemId, taskItemId);
        Long binId = outputBin.getId();
        bizReceiptOutputBinDataWrap.deleteById(binId, EnumDeleteFlag.ROLLBACK.getIntValue());

        // 撤回出库单与之前操作的标签关系
        List<BizLabelReceiptRel> receiptRelList = bizLabelReceiptRelDataWrap.findByBin(outputHeadId, outputItemId, binId);
        if (!CollectionUtils.isEmpty(receiptRelList)) {
            List<Long> relIdList = receiptRelList.stream().map(BizLabelReceiptRel::getId).collect(Collectors.toList());
            bizLabelReceiptRelDataWrap.deleteByIdList(relIdList, EnumDeleteFlag.ROLLBACK.getIntValue());
        }
        List<BizReceiptOutputItem> outputItemList = bizReceiptOutputItemDataWrap.findByHead(outputHeadId);
        Set<Integer> itemStatusSet = new HashSet<>();
        boolean submitFlag = false;
        for (BizReceiptOutputItem bizReceiptOutputItem : outputItemList) {
            Long id = bizReceiptOutputItem.getId();
            if (id.equals(outputItemId)) {
                BigDecimal taskQty = bizReceiptOutputItem.getTaskQty();
                taskQty = taskQty.subtract(rollbackQty);
                BigDecimal finishQty = bizReceiptOutputItem.getFinishQty();
                finishQty.subtract(rollbackQty);
                Integer itemStatus = EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue();
                if (taskQty.compareTo(BigDecimal.ZERO) == 0) {
                    itemStatus = EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue();
                    submitFlag = true;
                }
                itemStatusSet.add(itemStatus);
                bizReceiptOutputItemDataWrap.updateStatusAndQtyById(bizReceiptOutputItem.getId(), itemStatus, taskQty, finishQty);
                continue;
            }
            itemStatusSet.add(bizReceiptOutputItem.getItemStatus());
        }
        if (itemStatusSet.size() == 1 && submitFlag) {
            bizReceiptOutputHeadDataWrap.updateStatusById(outputHeadId, EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
            return;
        }
        BizReceiptOutputHead outputHead = ctx.getContextData(Const.BIZ_CONTEXT_KEY_BIN_VO);
        Integer inTaskStatus = EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue();
        if (!inTaskStatus.equals(outputHead.getReceiptStatus())) {
            bizReceiptOutputHeadDataWrap.updateStatusById(outputHeadId, EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
        }
    }

    /**
     * 调用sap关闭预留信息，全部关闭
     */
    public void closeReservationListAll(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        // 过滤后重新设置上下文-headDTO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        String receiptCode = headDTO.getReceiptCode();
        Map<Long, List<BizReceiptOutputItemDTO>> reservedMap = itemDTOList.stream().collect(Collectors.groupingBy(BizReceiptOutputItemDTO::getReservedOrderCode));
        for (Long reservedOrderCode : reservedMap.keySet()) {
            List<BizReceiptOutputItemDTO> itemList = reservedMap.get(reservedOrderCode);
            ErpReturnObject returnObj = reserveReceiptService.erpCloseReservation(JSONArray.toJSONStringWithDateFormat(itemList, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
            log.debug("单据{}领用类型{}需要关闭预留的信息：{}", receiptCode, headDTO.getReceiveType() , reservedOrderCode);
            log.info("关闭预留返回的信息：{}", JSON.toJSONString(returnObj));
            if (!Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                log.error("单据{}关闭-删除预留失败,参数信息为:{}", receiptCode, reservedOrderCode);
                throw new WmsException(EnumReturnMsg.CLOSE_RESERVE_RECEIPT_FAIL);
            }
        }
    }

    /**
     * 领料出库过账必须填写实际领料人、以及保管员用户id
     * @param ctx
     * @since 2023-05-25
     */
    public void checkAndSaveActualReceiver(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
//        // 领料出库需要在过账前判断是否有实际领料人和保管员的签字信息
//        if (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(headDTO.getReceiptType())) {
//            if (UtilObject.isEmpty(headDTO.getMatReceiverSignature())) {
//                log.warn(StrUtil.format("出库单{}，过账操作缺少实际领料人信息", headDTO.getReceiptCode()));
//                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
//            }
//        }

        UpdateWrapper<BizReceiptOutputHead> uw = new UpdateWrapper<>();
        uw.lambda().set(BizReceiptOutputHead::getActualReceiverName, headDTO.getActualReceiverName())
                .set(BizReceiptOutputHead::getStoreKeeperUserId, ctx.getCurrentUser().getId())
                .eq(BizReceiptOutputHead::getId, headDTO.getId());
        bizReceiptOutputHeadDataWrap.update(uw);
        log.debug("出库单{}, 修改实际领料人为{}", headDTO.getReceiptCode(), headDTO.getActualReceiverName());
    }

    /**
     * 修改发料人员
     * @param ctx
     */
    public void updateSendUser(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        UpdateWrapper<BizReceiptOutputHead> uw = new UpdateWrapper<>();
        uw.lambda().set(BizReceiptOutputHead::getSendMatUserId, ctx.getCurrentUser().getId())
                .eq(BizReceiptOutputHead::getId, headDTO.getId());
        headDTO.setSendMatUserName(ctx.getCurrentUser().getUserName());
        bizReceiptOutputHeadDataWrap.update(uw);
        log.debug("出库单{}, 修改发领料人为{}", headDTO.getReceiptCode(), headDTO.getSendMatUserName());
    }

    /**
     * 根据业务方需求，增加出库过账批次信息的到期日期限制，所过账的物料要求批次信息的到期时间应在当前日期之后（即已到期的物资不可出库）
     * @param ctx
     */
    public void checkLifetimeDate(BizContext ctx) {
        BizReceiptOutputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        for (BizReceiptOutputItemDTO itemDTO : po.getItemDTOList()) {
            for (BizReceiptOutputBinDTO binDTO : itemDTO.getBinDTOList()) {

                if (UtilObject.isNotNull(binDTO.getBatchInfo().getLifetimeDate())
                        && binDTO.getBatchInfo().getLifetimeDate().compareTo(new Date()) <= 0) {
                    // 批次的到期日期不为空时进行判断
                    // 如果到期日期小于当前日期，则说明该物料已过期，不允许出库。应给出提示
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_INVALID_RECEIPT_ITEM,
                            StrUtil.format(" 第{}行物料{}的出库所选批次已于{}到期，无法直接过账出库，请联系库管员",
                                    itemDTO.getRid(), itemDTO.getMatName(), binDTO.getBatchInfo().getLifetimeDate()));
                }
            }
        }
    }

    public void checkSaveItemRemark(BizContext ctx) {
        ReceiptItemActionPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po == null || po.getHeadId() == null || po.getItemIds() == null || !(po.getItemIds().size() > 0) || po.getReceiptCode() == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    public void saveItemRemark(BizContext ctx) {
        ReceiptItemActionPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getHeadId();
        List<Long> itemIds = po.getItemIds();
        bizReceiptOutputItemDataWrap.update(new UpdateWrapper<BizReceiptOutputItem>()
        .lambda().eq(BizReceiptOutputItem::getHeadId, headId)
        .in(BizReceiptOutputItem::getId, itemIds)
        .set(BizReceiptOutputItem::getItemRemark, po.getItemRemark()));
    }

    public void updateLocation(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
//        //校验更新后的库存地点是否合理
//        List<DicStockLocationDTO> dicStockLocationDTOs = dicStockLocationDataWrap.getAll();
//        HashSet<String> locationNames = new HashSet<>();
//        for (DicStockLocationDTO  dicStockLocationDTO : dicStockLocationDTOs) {
//            locationNames.add(dicStockLocationDTO.getLocationName());
//        }
//        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
//            if (!locationNames.contains(itemDTO.getLocationName())) {
//                log.warn(StrUtil.format("行项目{}, 更新后的库存地点不存在", itemDTO));
//                throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "更新后的库存地点不存在，请检查");
//            }
//        }

        //性能优化，校验从前端取值的locationId在缓存中是否存在，若存在则继续，否则报错
        List<Long> locationIdList = itemDTOList.stream().map(obj -> obj.getLocationId()).distinct().collect(Collectors.toList());
        Collection<DicStockLocationDTO> locationCacheByIds = dictionaryService.getLocationCacheByIds(locationIdList);
        if (UtilCollection.isEmpty(locationCacheByIds)||locationCacheByIds.size()!=locationIdList.size()){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_LOCATION_EMPTY);
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * 查找符合条件的出库单，关闭预留<br/>
     * 1、创建时间大于30天
     * 2、单据状态为草稿
     * 3、出库单类型为领料出库
     */
    public List<BizReceiptOutputHeadDTO> getShouldClosedOutputReceipt() {
        // 获取可以被关闭的领料出库单列表
        // 应符合条件：1、创建时间大于30天，2、单据状态为草稿，3、出库单类型为领料出库
        List<BizReceiptOutputHead> outputHeadList = bizReceiptOutputHeadDataWrap.list(new QueryWrapper<BizReceiptOutputHead>().lambda()
                .lt(BizReceiptOutputHead::getCreateTime, UtilDate.plusDays(new Date(), -30))
                .eq(BizReceiptOutputHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                .eq(BizReceiptOutputHead::getReceiptType, EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue())
        );
        List<BizReceiptOutputHeadDTO> dtoList = UtilCollection.toList(outputHeadList, BizReceiptOutputHeadDTO.class);
        dataFillService.fillAttr(dtoList);

        return dtoList;
    }

}
