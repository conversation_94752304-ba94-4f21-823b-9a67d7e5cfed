<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.exchangerate.dao.DicExchangeRateMapper">

    <select id="selectPageVOList" resultType="com.inossem.wms.common.model.masterdata.exchangerate.vo.DicExchangeRatePageVO">
        SELECT
            der.id,
            der.year,
            der.month,
            der.usd_rate,
            der.cny_rate,
            der.pkr_rate,
            der.is_delete,
            der.create_time,
            der.modify_time,
            der.create_user_id,
            der.modify_user_id,
            cu.user_name as create_user_name,
            mu.user_name as modify_user_name
        FROM dic_exchange_rate der
            LEFT JOIN sys_user cu ON der.create_user_id = cu.id AND cu.is_delete = 0
            LEFT JOIN sys_user mu ON der.modify_user_id = mu.id AND mu.is_delete = 0
        WHERE der.is_delete = 0
        <if test="po.year != null">
            AND der.year = #{po.year}
        </if>
        <if test="po.month != null">
            AND der.month = #{po.month}
        </if>
        ORDER BY der.year DESC, der.month DESC
    </select>

</mapper>
