package com.inossem.wms.bizdomain.inspect.service.component;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.feign.input.InputFeignApi;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.biz.PalletSortingService;
import com.inossem.wms.bizbasis.spec.service.biz.BizSpecFeatureValueService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.inspect.service.component.movetype.InspectMoveTypeComponent;
import com.inossem.wms.bizdomain.inspect.service.component.movetype.InspectWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.enums.spec.EnumSpecClassifyType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectDeletePO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectUpdatePO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectWriteOffPO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectHeadVO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectPdaLabelVo;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectPreHeadVo;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizReceiptPalletSortingItemDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.print.label.LabelBatch;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购验收组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Component
@Slf4j
public class InspectComponent {

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected PurchaseReceiptService purchaseReceiptService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizSpecFeatureValueService bizSpecFeatureValueService;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    protected PalletSortingService palletSortingService;

    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    private BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private InspectMoveTypeComponent inspectMoveTypeComponent;

    @Autowired
    private InspectWriteOffMoveTypeComponent inspectWriteOffMoveTypeComponent;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private ErpPostingService erpPostingService;

    @Autowired
    private InputFeignApi inputFeignApi;

    /**
     * 页面初始化: 1、设置采购验收【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】 3、设置扩展功能【单据流】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"采购验收","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptInspectHeadDTO().setReceiptType(EnumReceiptType.PURCHASE_INSPECTION.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
            new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 采购验收单-分页
     *
     * @in ctx 入参 {@link BizReceiptInspectSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<BizReceiptInspectHeadVO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInspectSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装查询条件
        WmsQueryWrapper<BizReceiptInspectSearchPO> wrapper = this.setQueryWrapper(po);
        // 分页处理
        IPage<BizReceiptInspectHeadVO> page = po.getPageObj(BizReceiptInspectHeadVO.class);
        // 获取采购验收单
        bizReceiptInspectHeadDataWrap.getInspectList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 采购验收单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"采购验收单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取采购验收单
        BizReceiptInspectHead bizInspectNoticeHead = bizReceiptInspectHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInspectHeadDTO bizInspectNoticeHeadDTO =
            UtilBean.newInstance(bizInspectNoticeHead, BizReceiptInspectHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectNoticeHeadDTO);
        // 设置按钮组
        ButtonVO buttonVO = this.setInfoButton(bizInspectNoticeHeadDTO);
        // 设置单据流
        ExtendVO extendVO = this.setInfoExtendRelation(bizInspectNoticeHeadDTO);
        // 设置采购验收单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizInspectNoticeHeadDTO, extendVO, buttonVO));
    }

    /**
     * 设置批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO ("head":"采购验收单详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"采购验收详情及批次图片")}
     */
    public void setBatchImg(BizContext ctx) {
        // 入参上下文
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead())) {
            return;
        }
        // 设置批次图片
        this.setBatchImg(resultVO.getHead());
        // 设置采购验收单详情批次图片到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 设置批次特性
     *
     * @in ctx 入参 {@link BizResultVO ("head":"采购验收单详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"采购验收详情及批次特性")}
     */
    public void setSpecFeature(BizContext ctx) {
        // 入参上下文
        BizResultVO<BizReceiptInspectHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNotNull(resultVO.getHead()) && UtilCollection.isNotEmpty(resultVO.getHead().getItemList())) {
            // 获取验收特性
            bizSpecFeatureValueService.getSpecList(resultVO.getHead().getItemList(), BizReceiptInspectItemDTO.class,
                EnumSpecClassifyType.INSPECTED_TYPE.getValue());
            // 获取物料特性
            bizSpecFeatureValueService.getSpecList(resultVO.getHead().getItemList(), BizReceiptInspectItemDTO.class,
                EnumSpecClassifyType.QUALITY_TYPE.getValue());
        }
        // 设置采购验收单详情批次特性到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存-校验采购验收入参
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "采购验收单"}
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            log.warn("提交的出库单没有包含抬头信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            log.warn("提交的验收单没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 保存采购验收单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "保存采购验收单"}
     * @out ctx 出参 {@link BizReceiptInspectHeadDTO : "保存的采购验收单"}
     */
    public void saveInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String inspectCode = po.getReceiptCode();
        po.setCreateUserId(user.getId());
        po.setReceiptType(EnumReceiptType.PURCHASE_INSPECTION.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新采购验收单
            bizReceiptInspectHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteInspectItem(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            inspectCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_INSPECT.getValue());
            po.setReceiptCode(inspectCode);
            bizReceiptInspectHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存验收单head成功!单号{},主键{},操作人{}", inspectCode, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        List<BizBatchInfoDTO> saveBatchInfoDtoList = new ArrayList<>();
        for (BizReceiptInspectItemDTO itemDto : po.getItemList()) {
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
            BizBatchInfoDTO batchInfoDto = itemDto.getBizBatchInfoDTO();
            if (UtilObject.isNotNull(batchInfoDto)) {
                batchInfoDto.setMatId(itemDto.getMatId());
                batchInfoDto.setFtyId(itemDto.getFtyId());
                batchInfoDto.setCreateUserId(user.getId());
                saveBatchInfoDtoList.add(batchInfoDto);
            }
        }
        /* ******** 批量保存批次 ******** */
        bizBatchInfoService.multiSaveBatchInfo(saveBatchInfoDtoList);
        log.debug("批量保存验收单批次信息成功!批次号{},操作人{}", saveBatchInfoDtoList.get(0).getBatchCode(), user.getUserName());
        /* ******** 批量保存item ******** */
        po.getItemList().forEach(item -> {
            item.setId(null);
            item.setBatchId(item.getBizBatchInfoDTO().getId());
            item.setPrintNum(item.getPrintNum() == null
                ? EnumDbDefaultValueInteger.BIZ_RECEIPT_INSPECT_ITEM_PRINT_NUM.getValue() : item.getPrintNum());
            item.setPrintItemStatus(
                item.getPrintItemStatus() == null ? EnumRealYn.FALSE.getIntValue() : item.getPrintItemStatus());
            item.setTagType(item.getTagType() == null ? EnumTagType.GENERAL.getValue() : item.getTagType());
        });
        bizReceiptInspectItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("批量保存验收单item成功,code{},headId{},操作人{}", inspectCode, po.getId(), user.getUserName());
        /* ******** 保存单据流 ******** */
        this.saveReceiptTree(po);
        log.debug("保存验收单单据流成功!");
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, inspectCode);
        // 返回保存的单据信息
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
    }

    /**
     * 保存批次图片
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "保存的采购验收单"}
     */
    public void saveBizBatchImg(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        for (BizReceiptInspectItemDTO itemDto : headDTO.getItemList()) {
            if (UtilCollection.isNotEmpty(itemDto.getBizBatchImgDTOList())) {
                itemDto.getBizBatchImgDTOList().forEach(imgDTO -> {
                    imgDTO.setMatId(itemDto.getMatId());
                    imgDTO.setFtyId(itemDto.getFtyId());
                    imgDTO.setBatchId(itemDto.getBizBatchInfoDTO().getId());
                });
            } else {
                itemDto.setBizBatchImgDTOList(new ArrayList<>());
            }
        }
        // 批量保存验收单批次图片
        bizBatchImgService.multiSaveBizBatchImg(headDTO.getItemList().stream()
            .flatMap(item -> item.getBizBatchImgDTOList().stream()).collect(Collectors.toList()));
        log.debug("批量保存验收单批次图片成功!");
    }

    /**
     * 保存批次特性
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "保存的采购验收单"}
     */
    public void saveSpecFeature(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 保存验收特性
        bizSpecFeatureValueService.saveSpecList(headDTO.getItemList(), BizReceiptInspectItemDTO.class,
            EnumSpecClassifyType.INSPECTED_TYPE.getValue(), ctx.getCurrentUser());
        log.debug("保存验收特性成功!");
        // 保存物料特性
        bizSpecFeatureValueService.saveSpecList(headDTO.getItemList(), BizReceiptInspectItemDTO.class,
            EnumSpecClassifyType.QUALITY_TYPE.getValue(), ctx.getCurrentUser());
        log.debug("保存物料特性成功!");
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "要保操作日志的采购验收单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的验收单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "要保持附件的采购验收单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存验收单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            EnumReceiptType.PURCHASE_INSPECTION.getValue(), user.getId());
        log.debug("保存验收单附件成功!");
    }

    /**
     * 采购验收单据流
     *
     * @param headDTO {@link BizReceiptInspectHeadDTO : "要保持单据流的采购验收单"}
     */
    public void saveReceiptTree(BizReceiptInspectHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptInspectItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(headDTO.getReceiptType());
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(item.getPreReceiptType());
            dto.setPreReceiptHeadId(item.getPreReceiptHeadId());
            dto.setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 提交-校验采购验收入参
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "采购验收单"}
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验入参 ******** */
        this.checkSaveData(ctx);
        /* ******** 校验验收单行项目相关数量开始 ******** */
        // 装载订单数量为零的行项目序号
        List<String> referReceiptQtyList = new ArrayList<>(po.getItemList().size());
        // 可验收数量 > 订单数量的行项目序号
        List<String> errorQryList = new ArrayList<>(po.getItemList().size());
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            // 订单数量
            BigDecimal preReceiptQty = itemDTO.getPreReceiptQty();
            // 可验收数量
            BigDecimal arrivalQty = itemDTO.getArrivalQty();
            if (preReceiptQty.compareTo(BigDecimal.ZERO) == 0) {
                referReceiptQtyList.add(itemDTO.getRid());
            }
            if (arrivalQty.compareTo(preReceiptQty) > 0) {
                errorQryList.add(itemDTO.getRid());
            }
        }
        if (UtilCollection.isNotEmpty(referReceiptQtyList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ORDER_QTR_ZERO, errorQryList.toString());
        }
        if (UtilCollection.isNotEmpty(errorQryList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ARRIVAL_MAX_ORDER_QTR, errorQryList.toString());
        }
        /* ******** 校验验收单行项目相关数量结束 ******** */
    }

    /**
     * 提交采购验收单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "提交采购验收单"}
     */
    public void submitInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存采购验收单
        this.saveInspect(ctx);
        // 更新采购验收单head、item状态为验收中
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_INSPECTING.getValue(), null);
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link BizReceiptInspectDeletePO : "采购验收单删除入参"}
     * @out ctx 出参 {@link BizReceiptInspectItemDTO : "要删除的行项目"}
     */
    public void checkDeleteInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取采购验收单
        BizReceiptInspectHead bizInspectNoticeHead = bizReceiptInspectHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptInspectHeadDTO bizInspectNoticeHeadDTO =
            UtilBean.newInstance(bizInspectNoticeHead, BizReceiptInspectHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(bizInspectNoticeHeadDTO);
        /* ******** 校验验收单head ******** */
        if (UtilObject.isNull(bizInspectNoticeHeadDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_HEAD_EMPTY);
        }
        /* ******** 校验验收单item ******** */
        int receiptStatus = bizInspectNoticeHead.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() != receiptStatus
            && EnumReceiptStatus.RECEIPT_STATUS_INSPECTING.getValue() != receiptStatus) {
            // 不等于 草稿 且 不等于 验收中的 不可删除
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_INSPECTED);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(bizInspectNoticeHeadDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getId)
                .collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 装载要删除的行项目
        List<BizReceiptInspectItemDTO> targetItemDTOList = new ArrayList<>();
        // 装载已打印的行项目
        List<String> errorList = new ArrayList<>();
        for (Long targetItemId : po.getItemIds()) {
            bizInspectNoticeHeadDTO.getItemList().forEach(sourceItemDTO -> {
                if (targetItemId.equals(sourceItemDTO.getId())) {
                    if (EnumRealYn.TRUE.getIntValue().equals(sourceItemDTO.getPrintItemStatus())) {
                        errorList.add(sourceItemDTO.getRid());
                    }
                    targetItemDTOList.add(sourceItemDTO);
                }
            });
        }
        // 验收单行项目{0}已打印
        if (UtilCollection.isNotEmpty(errorList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_PRINTED, errorList.toString());
        }
        // 设置要删除的行项目到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ITEM_VO, targetItemDTOList);
        // 设置要删除的行项目到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, po.getHeadId());
    }

    /**
     * 开始删除采购验收单
     *
     * @in ctx 入参 {@link BizReceiptInspectDeletePO : "验收单行删除入参对象"}
     */
    public void deleteInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除验收单 ******** */
        if (po.isDeleteAll()) {
            // 删除采购验收单head
            bizReceiptInspectHeadDataWrap.removeById(po.getHeadId());
            // 删除采购验收单item
            UpdateWrapper<BizReceiptInspectItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptInspectItem::getHeadId, po.getHeadId());
            bizReceiptInspectItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.PURCHASE_INSPECTION.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除采购验收单item
            bizReceiptInspectItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 删除采购验收单单据流
     *
     * @in ctx 入参 {@link BizReceiptInspectDeletePO : "验收单行删除入参对象"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据流
            receiptRelationService.deleteReceiptTree(EnumReceiptType.PURCHASE_INSPECTION.getValue(), po.getHeadId());
        }
    }

    /**
     * 删除批次信息
     *
     * @in ctx 入参 {@link List<BizReceiptInspectItemDTO> : "要删除的验收单行项目信息"}
     */
    public void deleteBatchInfo(BizContext ctx) {
        // 入参上下文
        List<BizReceiptInspectItemDTO> itemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ITEM_VO);
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 批次主键集合
            List<Long> batchIdList = new ArrayList<>(itemDTOList.size());
            itemDTOList.forEach(itemDTO -> batchIdList.add(itemDTO.getBatchId()));
            // 逻辑删除批次信息
            bizBatchInfoService.multiDeleteBatchInfo(batchIdList);
        }
    }

    /**
     * 删除批次图片
     *
     * @in ctx 入参 {@link List<BizReceiptInspectItemDTO> : "要删除的验收单行项目信息"}
     */
    public void deleteBatchInfoImg(BizContext ctx) {
        // 入参上下文
        List<BizReceiptInspectItemDTO> itemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ITEM_VO);
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 批次主键集合
            List<Long> batchIdList = new ArrayList<>(itemDTOList.size());
            itemDTOList.forEach(itemDTO -> batchIdList.add(itemDTO.getBatchId()));
            // 逻辑删除批批次图片
            bizBatchImgService.multiDeleteByBatchIdList(batchIdList);
        }
    }

    /**
     * 删除批单据单据附件
     *
     * @in ctx 入参 {@link BizReceiptInspectDeletePO : "验收单行删除入参对象"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据附件
            receiptAttachmentService.deleteBizReceiptAttachment(po.getHeadId(),
                EnumReceiptType.PURCHASE_INSPECTION.getValue());
        }
    }

    /**
     * 添加物料查询-基于采购订单
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO<BizReceiptInspectPreHeadVo> :"采购订单head结果集"}
     */
    public void purchaseReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (EnumReceiptType.PURCHASE_RECEIPT.getValue().equals(po.getPreReceiptType())) {
            // 装载返回对象
            MultiResultVO<BizReceiptInspectPreHeadVo> returnVo = new MultiResultVO<>();
            // 调用SAP查询采购订单
            List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList = purchaseReceiptService
                .getErpPurchaseReceiptItemList(po.setIsReturnFlag(EnumRealYn.FALSE.getIntValue()), user);
            // 上下文返回参数
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, this.purchaseDataFormat(returnVo, purchaseReceiptItemVoList));
        }
    }

    /**
     * PDA端验收单详情查询
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizReceiptInspectHeadDTO :"验收单详情"}
     */
    public void getPdaInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 获取采购验收单
        BizReceiptInspectHead bizInspectNoticeHead = bizReceiptInspectHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInspectHeadDTO bizInspectNoticeHeadDTO =
            UtilBean.newInstance(bizInspectNoticeHead, BizReceiptInspectHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(bizInspectNoticeHeadDTO);
        if (UtilObject.isNotNull(bizInspectNoticeHeadDTO)
            && UtilCollection.isNotEmpty(bizInspectNoticeHeadDTO.getItemList())) {
            // 设置【待验收行项目、已完成行项目、待码盘行项目、标签数据】
            this.setItemLabel(bizInspectNoticeHeadDTO);
            // 设置已码盘数据
            bizInspectNoticeHeadDTO.setPalletSortingItemList(this.setPalletSortingItemList(bizInspectNoticeHeadDTO));
        }
        // 设置采购验收单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizInspectNoticeHeadDTO, new ExtendVO(), new ButtonVO()));
    }

    /**
     * PDA端打印校验
     *
     * @in ctx 入参 {@link BizReceiptInspectUpdatePO : "采购验收单"}
     */
    public void checkPdaPrint(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验验收单是否删除
        this.checkReceiptDelete(po);
        // 校验验收单可验收数量
        this.checkQty(po.getItemList());
        // 非补打--校验验收单行项目是否打印
        this.checkReceiptItemPrint(po);
    }

    /**
     * PDA端打印
     *
     * @in ctx 入参 {@link BizReceiptInspectUpdatePO : "采购验收修改入参"}
     * @out ctx 出参 {@link BizReceiptInspectPdaLabelVo :"打印标签数据"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void pdaPrint(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (UtilObject.isNotNull(po) && UtilCollection.isNotEmpty(po.getItemList())) {
            // 装载要保存的标签数据集合
            List<BizLabelDataDTO> labelDataList = new ArrayList<>();
            // 装载要更新的批次信息
            List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
            // 装载打印机打印数据
            List<LabelBatch> labelBatchList = new ArrayList<>();
            for (BizReceiptInspectItemDTO inspectNoticeItemDTO : po.getItemList()) {
                // 行项目打印状态
                Integer printItemStatus = inspectNoticeItemDTO.getPrintItemStatus();
                // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
                Integer tagType = inspectNoticeItemDTO.getBizBatchInfoDTO().getTagType();
                // 单品/批次 0批次 1单品
                Integer isSingle = inspectNoticeItemDTO.getBizBatchInfoDTO().getIsSingle();
                // 未打印
                if (EnumRealYn.FALSE.getIntValue().equals(printItemStatus)) {
                    // 设置要更新的采购验收单数据
                    inspectNoticeItemDTO.setPrintItemStatus(EnumRealYn.TRUE.getIntValue());
                    inspectNoticeItemDTO.setIsSingle(isSingle);
                    inspectNoticeItemDTO.setTagType(tagType);
                    // 设置要更新的批次信息数据
                    inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectHeadId(inspectNoticeItemDTO.getHeadId());
                    inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectItemId(inspectNoticeItemDTO.getId());
                    inspectNoticeItemDTO.getBizBatchInfoDTO()
                        .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
                    inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectCode(inspectNoticeItemDTO.getReceiptCode());
                    inspectNoticeItemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
                    bizBatchInfoDTOList.add(inspectNoticeItemDTO.getBizBatchInfoDTO());
                    // 批次 + 普通标签不需要生成rfid,其他生成rfid
                    if (!(EnumRealYn.FALSE.getIntValue().equals(isSingle)
                        && EnumTagType.GENERAL.getValue().equals(tagType))) {
                        for (BizLabelDataDTO label : inspectNoticeItemDTO.getBizLabelDataDTOList()) {
                            // 生成标签编码
                            String labelCode =
                                bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());
                            BigDecimal qty = label.getQty();
                            // 设置标签数据
                            label = UtilBean.newInstance(inspectNoticeItemDTO, label.getClass());
                            label.setId(null);
                            label.setLabelCode(labelCode);
                            label.setSnCode(labelCode);
                            label.setQty(qty);
                            label.setLabelType(inspectNoticeItemDTO.getTagType());
                            label.setReceiptHeadId(inspectNoticeItemDTO.getHeadId());
                            label.setReceiptItemId(inspectNoticeItemDTO.getId());
                            label.setReceiptType(inspectNoticeItemDTO.getReceiptType());
                            label.setPreReceiptHeadId(inspectNoticeItemDTO.getPreReceiptHeadId());
                            label.setPreReceiptItemId(inspectNoticeItemDTO.getPreReceiptItemId());
                            label.setPreReceiptType(inspectNoticeItemDTO.getPreReceiptType());
                            labelDataList.add(label);
                            // 设置rfid标签打印数据
                            this.setPrintData(labelBatchList, label, inspectNoticeItemDTO, user);
                        }
                    } else {
                        // 设置普通标签打印数据
                        this.setPrintData(labelBatchList, null, inspectNoticeItemDTO, user);
                    }
                }
            }
            /* *** 更新采购验收单【行项目打印状态、标签类型、打印份数】 *** */
            bizReceiptInspectItemDataWrap.updateBatchDtoById(po.getItemList());
            log.info("采购验收-PDA端-打印-更新单据行项目打印状态成功");
            /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
            if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
                bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
                log.info("采购验收-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
            }
            /* *** 插入标签数据及关联属性 *** */
            if (UtilCollection.isNotEmpty(labelDataList)) {
                labelDataService.saveBatchDto(labelDataList);
                log.info("采购验收-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
                List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
                for (BizLabelDataDTO label : labelDataList) {
                    BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                    bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                    bizLabelReceiptRel.setId(null);
                    bizLabelReceiptRel.setLabelId(label.getId());
                    bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                    bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                    bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                    bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                    bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                    bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                    bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_CODE.getValue());
                    bizLabelReceiptRelList.add(bizLabelReceiptRel);
                }
                labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
                log.info("采购验收-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
            }
            // 推送MQ - 调用物料标签打印
            if (UtilCollection.isNotEmpty(labelBatchList)) {
                ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_MAT_LABEL, labelBatchList);
                RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
            }
        }
    }

    /**
     * PDA端验收校验
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "采购验收单"}
     */
    public void checkPdaInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验验收单是否删除 ******** */
        this.checkReceiptDelete(new BizReceiptInspectUpdatePO().setItemList(po.getItemList()).setId(po.getId()));
        /* ******** 校验验收数量 ******** */
        this.checkQty(po.getItemList());
        // 设置要删除的行项目到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, po.getId());
    }

    /**
     * PDA端验收【更新验收数据、生成入库单】
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "采购验收单"}
     */
    public void pdaInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 合格数量 = 到货数量 - 不合格数量
        po.getItemList().forEach(item -> item.setQty(item.getArrivalQty().subtract(item.getUnqualifiedQty())));
        // 更新采购验收单行item状态为已验收
        this.updateStatus(null, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_INSPECTED.getValue(), null);
    }

    /**
     * 生成验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "采购验收单"}
     */
    public void genInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        Boolean inspected = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INSPECTED);
        if (null == po) {
            return;
        }
        // 校验全部行项目都等于已完成时的处理
        if (inspected != null && inspected) {
            // 填充父子属性和关联属性,获取全部验收行项目
            dataFillService.fillAttr(po);
            // 装载验收入库单head
            BizReceiptInputHeadDTO inspectInputHead = new BizReceiptInputHeadDTO();
            // 装载验收入库单item
            List<BizReceiptInputItemDTO> inspectInputItemList = new ArrayList<>(po.getItemList().size());
            /* ******** 入库单head设置 ******** */
            inspectInputHead.setReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue());
            /* ******** 入库单item设置 ******** */
            for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
                BizReceiptInputItemDTO inspectInputItem = new BizReceiptInputItemDTO();
                inspectInputItem = UtilBean.newInstance(itemDTO, inspectInputItem.getClass());
                inspectInputItem.setId(null);
                inspectInputItem.setHeadId(null);
                inspectInputItem.setPreReceiptHeadId(po.getId());
                inspectInputItem.setPreReceiptItemId(itemDTO.getId());
                inspectInputItem.setPreReceiptType(EnumReceiptType.PURCHASE_INSPECTION.getValue());
                inspectInputItem.setMatDocCode(null);
                inspectInputItem.setMatDocRid(null);
                inspectInputItem.setMatDocYear(null);
                inspectInputItem.setPostingDate(null);
                inspectInputItem.setDocDate(null);
                inspectInputItem.setIsPost(null);
                inspectInputItemList.add(inspectInputItem);
            }
            inspectInputHead.setItemList(inspectInputItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, inspectInputHead);
            ctxInput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成验收入库单
            ProducerMessageContent message =
                ProducerMessageContent.messageContent(TagConst.GEN_INSPECT_INPUT_STOCK, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * PDA端撤校验
     *
     * @in ctx 入参 {@link BizReceiptInspectUpdatePO : "采购验收修改入参"}
     */
    public void checkPdaRevoke(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    /**
     * PDA端撤销
     *
     * @in ctx 入参 {@link BizReceiptInspectUpdatePO : "采购验收修改入参"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void pdaRevoke(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectUpdatePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取采购验收单
        BizReceiptInspectHead bizInspectNoticeHead = bizReceiptInspectHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptInspectHeadDTO headDTO = UtilBean.newInstance(bizInspectNoticeHead, BizReceiptInspectHeadDTO.class);
        /* ******** 更新验收单为验收中、同时更新打印状态 ******** */
        this.updateStatus(headDTO, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_INSPECTING.getValue(),
            EnumRealYn.FALSE.getIntValue());
        /* ******** 删除验收单关联数据 ******** */
        List<Long> labelIds = new ArrayList<>();
        // 单据号+单据行项目号集合
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if (UtilCollection.isNotEmpty(itemDTO.getBizLabelDataDTOList())) {
                labelIds = itemDTO.getBizLabelDataDTOList().stream().map(BizLabelDataDTO::getLabelId)
                    .collect(Collectors.toList());
            }
        }
        // 删除标签数据、标签单据关联表数据、码盘数据
        if (UtilCollection.isNotEmpty(labelIds)) {
            labelDataService.multiDeleteLabelData(labelIds);
            labelReceiptRelService.multiDeleteLabelReceiptRel(labelIds);
            palletSortingService.multiDeleteReceiptPalletSorting(po.getId());
        }
    }

    /**
     * PDA生成码盘数据
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "验收表单"}
     */
    public void savePalletSorting(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilCollection.isEmpty(po.getUnPalletSortingItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 生成码盘数据
        palletSortingService.insertPalletSorting(po);
    }

    /**
     * 设置待码盘数据
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "验收表单"}
     * @out ctx 出参 {@link BizReceiptInspectPdaLabelVo : "待码盘数据集合"}
     */
    public void setUnPalletSortingItem(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载待码盘数据
        BizReceiptInspectPdaLabelVo vo = new BizReceiptInspectPdaLabelVo();
        // 获取行项目状态为已完成 + 行项目打印状态为已打印的行项目明细
        QueryWrapper<BizReceiptInspectItem> queryWrapper = new QueryWrapper<>();
        // 验收中与 草稿状态不可码盘
        queryWrapper.lambda().eq(BizReceiptInspectItem::getHeadId, headDTO.getId())
            .notIn(BizReceiptInspectItem::getItemStatus, this.getCantPalletStatus())
            .eq(BizReceiptInspectItem::getPrintItemStatus, EnumRealYn.TRUE.getIntValue())
            .eq(BizReceiptInspectItem::getCreateUserId, ctx.getCurrentUser().getId());
        List<BizReceiptInspectItem> itemList = bizReceiptInspectItemDataWrap.list(queryWrapper);
        List<BizReceiptInspectItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptInspectItemDTO.class);
        itemDTOList = itemDTOList.stream().filter(item -> !EnumTagType.GENERAL.getValue().equals(item.getTagType()))
            .collect(Collectors.toList());
        Map<String, List<BizLabelDataDTO>> labelMap = new HashMap<>(itemDTOList.size());
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            labelMap = this.getItemLabel(itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_UN_CODE.getValue(),
                headDTO.getReceiptType());
        }
        List<BizReceiptInspectItemDTO> resultItemList = new ArrayList<>();
        for (BizReceiptInspectItemDTO itemDTO : itemDTOList) {
            String strFlag = StrUtil.format("{}-{}-{}", itemDTO.getHeadId(), itemDTO.getId(), headDTO.getReceiptType());
            if (labelMap.get(strFlag) != null && labelMap.get(strFlag).size() > 0) {
                // 设置行项目标签数据
                itemDTO.setBizLabelDataDTOList(labelMap.get(strFlag));
                resultItemList.add(itemDTO);
            }
        }
        vo.setUnPalletSortingItemDTOList(resultItemList);
        // 回填待码盘数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * 设置已码盘数据
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "验收表单"}
     */
    public void setPalletSortingItem(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载码盘数据
        BizReceiptInspectPdaLabelVo vo = new BizReceiptInspectPdaLabelVo();
        List<BizReceiptPalletSortingItemDTO> palletSortingItemDTOList = this.setPalletSortingItemList(headDTO);
        if (UtilCollection.isNotEmpty(palletSortingItemDTOList)) {
            vo.setPalletSortingItemDTOList(palletSortingItemDTOList);
            // 回填待码盘数据到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
        }
    }

    /**
     * 删除码盘单前校验
     *
     * @in ctx 入参 {@link BizReceiptPalletSortingItemDTO : "码盘单"}
     */
    public void checkDeletePalletSorting(BizContext ctx) {
        // 入参上下文
        BizReceiptPalletSortingItemDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isEmpty(po.getBizLabelReceiptRelDTOList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 查询码盘单是否已过门，已过门不能删除
        boolean res =
            palletSortingService.delCheckPalletSortingOrUnDoor(po, EnumReceiptStatus.RECEIPT_STATUS_ACCESS.getValue());
        if (!res) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_PASSED_NOT_DELETE);
        }
    }

    /**
     * 删除码盘单
     *
     * @in ctx 入参 {@link BizReceiptPalletSortingItemDTO : "码盘单"}
     */
    public void deletePalletSorting(BizContext ctx) {
        // 入参上下文
        BizReceiptPalletSortingItemDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 删除码盘单及标签关联数据
        palletSortingService.deletePalletSorting(po);
    }

    /**
     * 设置标签打印数据
     *
     * @param labelBatchList 装载打印机打印数据
     * @param labelDataDTO 标签数据
     * @param itemDTO 要打印的采购验收单行项目
     * @param user 当前用户
     */
    private void setPrintData(List<LabelBatch> labelBatchList, BizLabelDataDTO labelDataDTO,
        BizReceiptInspectItemDTO itemDTO, CurrentUser user) {
        // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
        Integer tagType = itemDTO.getBizBatchInfoDTO().getTagType();
        // 单品/批次 0批次 1单品
        Integer isSingle = itemDTO.getBizBatchInfoDTO().getIsSingle();
        LabelBatch labelBatch = new LabelBatch().setMatCode(itemDTO.getMatCode()).setMatName(itemDTO.getMatName())
            .setPurchaseOrder(itemDTO.getReceiptCode()).setMaterialNeeds("采矿部李楼采矿场").setQrCode("1000000001 2")
            .setSupplier(itemDTO.getSupplierName()).setReceiver(user.getUserName()).setPrinterIp(itemDTO.getPrinterIp())
            .setPrinterPort(itemDTO.getPrinterPort()).setPrinterIsDefault(itemDTO.getPrinterDefault())
            .setPrinterIsPortable(itemDTO.getPrinterIsPortable());
        if (!(EnumRealYn.FALSE.getIntValue().equals(isSingle) && EnumTagType.GENERAL.getValue().equals(tagType))) {
            labelBatch.setRfidCode(labelDataDTO.getLabelCode());
            labelBatch.setQty(labelDataDTO.getQty());
        } else {
            labelBatch.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
            labelBatch.setQty(itemDTO.getQty());
        }
        for (int pNum = 0; pNum < itemDTO.getPrintNum(); pNum++) {
            labelBatchList.add(labelBatch);
        }
    }

    /**
     * 获取已码盘数据
     *
     * @param headDTO 采购验收单
     * @return 已码盘数据
     */
    private List<BizReceiptPalletSortingItemDTO> setPalletSortingItemList(BizReceiptInspectHeadDTO headDTO) {
        List<BizReceiptPalletSortingItemDTO> palletSortingItemList = new ArrayList<>();
        if (UtilObject.isNotNull(headDTO)) {
            // TODO: 2021/4/17 修改为MQ
            palletSortingItemList = palletSortingService.selectPalletSortingListByReceipt(headDTO.getId());
        }
        return palletSortingItemList;
    }

    /**
     * PDA端详情 设置【待验收行项目、已完成行项目、待码盘行项目、标签数据】
     *
     * @param headDTO 验收单行信息
     */
    private void setItemLabel(BizReceiptInspectHeadDTO headDTO) {
        // 装载待验收行项目
        List<BizReceiptInspectItemDTO> unInspectItemList = new ArrayList<>();
        // 装载已验收行项目
        List<BizReceiptInspectItemDTO> inspectedItemList = new ArrayList<>();
        // 装载待码盘行项目
        List<BizReceiptInspectItemDTO> unPalletSortingItemList = new ArrayList<>();
        // 获取验收单行项目
        List<BizReceiptInspectItemDTO> itemList = headDTO.getItemList();
        // 获取验收单标签数据
        Map<String, List<BizLabelDataDTO>> labelMap = this.getItemLabel(itemList, null, headDTO.getReceiptType());
        for (BizReceiptInspectItemDTO itemDTO : itemList) {
            // 标签数据key
            String strFlag = StrUtil.format("{}-{}-{}", itemDTO.getHeadId(), itemDTO.getId(), headDTO.getReceiptType());
            // 设置行项目标签数据
            itemDTO.setBizLabelDataDTOList(labelMap.get(strFlag));
            // 待验收行项目
            if (EnumReceiptStatus.RECEIPT_STATUS_INSPECTING.getValue().equals(itemDTO.getItemStatus())) {
                unInspectItemList.add(itemDTO);
            }
            // 设置已验收行项目
            if (!this.getCantPalletStatus().contains(itemDTO.getItemStatus())) {
                inspectedItemList.add(itemDTO);
            }
            // 设置待码盘行项目 (行项目打印状态是已打印 && 行项目状态是已完成 && 行项目不是普通标签)
            if (EnumRealYn.TRUE.getIntValue().equals(itemDTO.getPrintItemStatus())
                && !this.getCantPalletStatus().contains(itemDTO.getItemStatus())
                && !EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType())) {
                BizReceiptInspectItemDTO unPalletSortingItem = new BizReceiptInspectItemDTO();
                unPalletSortingItem = UtilBean.newInstance(itemDTO, unPalletSortingItem.getClass());
                if (UtilCollection.isNotEmpty(unPalletSortingItem.getBizLabelDataDTOList())) {
                    // 保留待码盘数据
                    List<BizLabelDataDTO> unBizLabelDataDTOList = unPalletSortingItem.getBizLabelDataDTOList().stream()
                        .filter(k -> k.getStatus() == EnumReceiptStatus.RECEIPT_STATUS_UN_CODE.getValue().intValue())
                        .collect(Collectors.toList());
                    if (UtilCollection.isNotEmpty(unBizLabelDataDTOList)) {
                        unPalletSortingItem.setBizLabelDataDTOList(unBizLabelDataDTOList);
                        unPalletSortingItemList.add(unPalletSortingItem);
                    }
                }
            }
        }
        // 待验收行项目
        headDTO.setUnInspectItemList(JSONObject.parseArray(
            JSON.toJSONString(unInspectItemList, SerializerFeature.DisableCircularReferenceDetect),
            BizReceiptInspectItemDTO.class));
        // 已验收行项目
        headDTO.setInspectedItemList(JSONObject.parseArray(
            JSON.toJSONString(inspectedItemList, SerializerFeature.DisableCircularReferenceDetect),
            BizReceiptInspectItemDTO.class));
        // 待码盘行项目
        headDTO.setUnPalletSortingItemList(JSONObject.parseArray(
            JSON.toJSONString(unPalletSortingItemList, SerializerFeature.DisableCircularReferenceDetect),
            BizReceiptInspectItemDTO.class));
    }

    /**
     * 获取标签数据
     *
     * @param itemList 采购验收单行项目
     * @param status 状态
     * @param receiptType 单据类型
     * @return 验收单标签数据
     */
    private Map<String, List<BizLabelDataDTO>> getItemLabel(List<BizReceiptInspectItemDTO> itemList, Integer status,
        Integer receiptType) {
        if (UtilCollection.isEmpty(itemList)) {
            return new HashMap<>();
        }
        // 获取验收单标签数据
        return labelReceiptRelService.getLabelReceiptRefBatch(status, receiptType,
            itemList.stream().map(BizReceiptInspectItemDTO::getId).collect(Collectors.toList()));
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return WmsQueryWrapper<BizReceiptInspectSearchPO>
     */
    private WmsQueryWrapper<BizReceiptInspectSearchPO> setQueryWrapper(BizReceiptInspectSearchPO po) {
        if (null == po) {
            po = new BizReceiptInspectSearchPO();
        }
        Date postCreateTime = null;
        if (UtilObject.isNotNull(po.getPostCreateTime())) {
            postCreateTime = UtilLocalDateTime.getStartTime(po.getPostCreateTime());
        }
        Date postEndTime = null;
        if (UtilObject.isNotNull(po.getPostEndTime())) {
            postEndTime = UtilLocalDateTime.getEndTime(po.getPostEndTime());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptInspectSearchPO> wrapper = new WmsQueryWrapper<>();
        // 单据号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInspectSearchPO::getReceiptCode,
            BizReceiptInspectHead.class, po.getReceiptCode());
        // 单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
            BizReceiptInspectSearchPO::getReceiptStatus, BizReceiptInspectHead.class, po.getReceiptStatusList());
        // 凭证创建时间
        wrapper.lambda().between((UtilObject.isNotNull(postCreateTime)), BizReceiptInspectSearchPO::getDocDate,
            BizReceiptInspectItem.class, postCreateTime, postEndTime);
        // 前线单据号(采购订单)
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getPreReceiptCode()),
            BizReceiptInspectSearchPO::getReceiptCode, ErpPurchaseReceiptHead.class, po.getPreReceiptCode());
        return wrapper.setEntity(po);
    }

    /**
     * 准备更新验收单状态
     *
     * @param headDTO 采购验收单head
     * @param itemDTOList 采购验收单item
     * @param printItemStatus 行项目打印状态
     */
    public void updateStatus(BizReceiptInspectHeadDTO headDTO, List<BizReceiptInspectItemDTO> itemDTOList,
        Integer status, Integer printItemStatus) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> {
                item.setItemStatus(status);
                if (UtilObject.isNotNull(printItemStatus)) {
                    item.setPrintItemStatus(printItemStatus);
                }
            });
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新采购验收单head状态
     *
     * @param headDto 采购验收单head
     */
    private void updateHead(BizReceiptInspectHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptInspectHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新采购验收单item状态
     *
     * @param itemDtoList 采购验收单item
     */
    private void updateItem(List<BizReceiptInspectItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptInspectItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 删除采购验收单行项目
     *
     * @param po 采购验收
     */
    public void deleteInspectItem(BizReceiptInspectHeadDTO po) {
        UpdateWrapper<BizReceiptInspectItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInspectItem::getHeadId, po.getId());
        bizReceiptInspectItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 设置详情页按钮权限
     *
     * @param headDTO 采购验收单
     * @return 按钮组对象
     */
    private ButtonVO setInfoButton(BizReceiptInspectHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (null == receiptStatus) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_INSPECTING.getValue().equals(receiptStatus)) {
            // 验收中 -【删除】
            buttonVO.setButtonDelete(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_INSPECTED.getValue().equals(receiptStatus)) {
            buttonVO.setButtonPost(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            buttonVO.setButtonPost(true);
        } else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            buttonVO.setButtonWriteOff(true);
        }
        return buttonVO;
    }

    /**
     * 设置详情页单据流
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单据流信息","extend":"扩展功能开启单据流")}
     */
    public ExtendVO setInfoExtendRelation(BizReceiptInspectHeadDTO headDTO) {
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
        headDTO.setRelationList(receiptRelationService.getReceiptTree(headDTO.getReceiptType(), headDTO.getId(), null));
        return extendVO;
    }

    /**
     * 设置批次图片信息
     *
     * @param headDTO {@link BizReceiptInspectHeadDTO ("head":"采购验收单详情")}
     */
    public void setBatchImg(BizReceiptInspectHeadDTO headDTO) {
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            Set<Long> batchIdSet =
                headDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getBatchId).collect(Collectors.toSet());
            // 获取批次图片
            Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
            if (imgMap.isEmpty()) {
                return;
            }
            // 全部行项目赋值批次图片
            headDTO.getItemList().forEach(itemDTO -> {
                if (UtilNumber.isNotEmpty(itemDTO.getBatchId())
                    && UtilCollection.isNotEmpty(imgMap.get(itemDTO.getBatchId()))) {
                    itemDTO.setBizBatchImgDTOList(imgMap.get(itemDTO.getBatchId()));
                }
            });
            // 已验收行项目赋值批次图片
            if (UtilCollection.isNotEmpty(headDTO.getInspectedItemList())) {
                headDTO.getInspectedItemList().forEach(inspectedItemDTO -> {
                    if (UtilNumber.isNotEmpty(inspectedItemDTO.getBatchId())
                        && UtilCollection.isNotEmpty(imgMap.get(inspectedItemDTO.getBatchId()))) {
                        inspectedItemDTO.setBizBatchImgDTOList(imgMap.get(inspectedItemDTO.getBatchId()));
                    }
                });
            }
        }
    }

    /**
     * 采购订单查询参转换
     *
     * @param returnVo 要设置的返回数据
     * @param purchaseReceiptItemVoList 采购订单信息
     * @return 物料信息
     */
    private MultiResultVO<BizReceiptInspectPreHeadVo> purchaseDataFormat(
        MultiResultVO<BizReceiptInspectPreHeadVo> returnVo, List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(purchaseReceiptItemVoList)) {
            // 装载返回数据
            List<BizReceiptInspectPreHeadVo> headInfoList = new ArrayList<>();
            // 根据采购订单号分组
            LinkedHashMap<String, List<ErpPurchaseReceiptItemDTO>> purchaseMap =
                purchaseReceiptItemVoList.stream().collect(Collectors
                    .groupingBy(ErpPurchaseReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
            // 查询未过账的验收单，根据采购订单itemId分组
            QueryWrapper<BizReceiptInspectItem> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(BizReceiptInspectItem::getIsPost, 0);
            List<BizReceiptInspectItem> bizReceiptInspectItemList = bizReceiptInspectItemDataWrap.list(wrapper);
            Map<Long, List<BizReceiptInspectItem>> inspectMap = bizReceiptInspectItemList.stream()
                .collect(Collectors.groupingBy(BizReceiptInspectItem::getReferReceiptItemId));
            Set<String> keys = purchaseMap.keySet();
            for (String key : keys) {
                // 装载返回数据head
                BizReceiptInspectPreHeadVo headInfo = new BizReceiptInspectPreHeadVo();
                // 装载返回数据item
                List<BizReceiptInspectItemDTO> itemInfoList = new ArrayList<>();
                List<ErpPurchaseReceiptItemDTO> purchaseItemList = purchaseMap.get(key);
                for (int i = 0; i < purchaseItemList.size(); i++) {
                    ErpPurchaseReceiptItemDTO purchaseDTO = purchaseItemList.get(i);
                    BizReceiptInspectItemDTO itemInfo = new BizReceiptInspectItemDTO();
                    /* ******** 设置head列字段 ******** */
                    if (i == 0) {
                        headInfo = UtilBean.newInstance(purchaseDTO, headInfo.getClass());
                        headInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                    }
                    /* ******** 设置item列字段 ******** */
                    itemInfo = UtilBean.newInstance(purchaseDTO, itemInfo.getClass());
                    itemInfo.setId(null);
                    itemInfo.setHeadId(null);
                    itemInfo.setRid(Const.STRING_EMPTY);
                    itemInfo.setReceiptCode(Const.STRING_EMPTY);
                    itemInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                    itemInfo.setReferReceiptRid(purchaseDTO.getRid());
                    // 未过账的数量
                    BigDecimal noPostQty = BigDecimal.ZERO;
                    if (inspectMap.containsKey(purchaseDTO.getId())) {
                        List<BizReceiptInspectItem> bizReceiptInspectItems = inspectMap.get(purchaseDTO.getId());
                        noPostQty = bizReceiptInspectItems.stream().map(BizReceiptInspectItem::getQty)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    // 数量 = 订单数量-已入库数量-未过账的数量
                    itemInfo
                        .setQty(purchaseDTO.getReceiptQty().subtract(purchaseDTO.getSubmitQty()).subtract(noPostQty));
                    itemInfo.setArrivalQty(itemInfo.getQty());
                    itemInfo.setPreReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setPreReceiptItemId(purchaseDTO.getId());
                    itemInfo.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setPreReceiptQty(purchaseDTO.getReceiptQty());
                    itemInfo.setReferReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setReferReceiptItemId(purchaseDTO.getId());
                    itemInfo.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setPrintItemStatus(EnumRealYn.FALSE.getIntValue());
                    itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                    // 获取缓存中仓库信息
                    itemInfo.setWhId(dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()).getWhId());
                    /* ******** 设置批次信息 ******** */
                    BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                    batchInfoDTO = UtilBean.newInstance(itemInfo, batchInfoDTO.getClass());
                    batchInfoDTO.setId(null);
                    batchInfoDTO.setPurchaseReceiptHeadId(purchaseDTO.getHeadId());
                    batchInfoDTO.setPurchaseReceiptItemId(purchaseDTO.getId());
                    batchInfoDTO.setPurchaseReceiptRid(purchaseDTO.getRid());
                    batchInfoDTO.setPurchaseReceiptCode(purchaseDTO.getReceiptCode());
                    batchInfoDTO.setBatchErp(purchaseDTO.getBatchErp());
                    batchInfoDTO.setSpecStock(purchaseDTO.getSpecStock());
                    batchInfoDTO.setSpecStockCode(purchaseDTO.getSpecStockCode());
                    batchInfoDTO.setSpecStockName(purchaseDTO.getSpecStockName());
                    itemInfo.setBizBatchInfoDTO(batchInfoDTO);
                    itemInfoList.add(itemInfo);
                }
                headInfo.setChildren(itemInfoList);
                headInfoList.add(headInfo);
                // 获取验收特性
                bizSpecFeatureValueService.getSpecList(itemInfoList, BizReceiptInspectItemDTO.class,
                    EnumSpecClassifyType.INSPECTED_TYPE.getValue());
                // 获取物料特性
                bizSpecFeatureValueService.getSpecList(itemInfoList, BizReceiptInspectItemDTO.class,
                    EnumSpecClassifyType.QUALITY_TYPE.getValue());
            }
            returnVo.setResultList(headInfoList);
        }
        return returnVo;
    }

    /**
     * 验证采购验收行项目是否全部已PDA验收
     *
     * @param headId 采购验收单headId
     * @return 行项目全部验收 - true or 行项目未全部验收 - false
     */
    private boolean checkAllItemStatus(Long headId) {
        QueryWrapper<BizReceiptInspectItem> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptInspectItem::getHeadId, headId);
        // 根据headId获取全部采购验收单行项目
        List<BizReceiptInspectItem> itemAllList = bizReceiptInspectItemDataWrap.list(wrapper);
        // 过滤掉行项目为已完成的数据
        List<BizReceiptInspectItem> emptyList = itemAllList.stream()
            .filter(
                k -> k.getItemStatus().intValue() != EnumReceiptStatus.RECEIPT_STATUS_INSPECTED.getValue().intValue())
            .collect(Collectors.toList());
        // 如果全部行项目已完成
        return UtilCollection.isEmpty(emptyList);
    }

    /**
     * 校验验收单行项目是否打印
     *
     * @param po 采购验收修改入参
     */
    private void checkReceiptItemPrint(BizReceiptInspectUpdatePO po) {
        List<Long> itemIds =
            po.getItemList().stream().map(BizReceiptInspectItemDTO::getId).collect(Collectors.toList());
        QueryWrapper<BizReceiptInspectItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptInspectItem::getPrintItemStatus, EnumRealYn.TRUE.getIntValue());
        queryWrapper.lambda().in(BizReceiptInspectItem::getId, itemIds);
        List<BizReceiptInspectItem> itemList = bizReceiptInspectItemDataWrap.list(queryWrapper);
        if (UtilCollection.isNotEmpty(itemList)) {
            List<String> ridStrings = itemList.stream().map(BizReceiptInspectItem::getRid).collect(Collectors.toList());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_PRINTED, ridStrings.toString());
        }
    }

    /**
     * 校验验收单是否删除
     *
     * @param inspectNoticeUpdatePO 采购验收修改入参
     */
    private void checkReceiptDelete(BizReceiptInspectUpdatePO inspectNoticeUpdatePO) {
        // 验收单抬头主键是否为空
        if (UtilNumber.isEmpty(inspectNoticeUpdatePO.getId())) {
            log.warn("验收单抬头主键否为空。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 验收单行项目数据是否为空
        if (UtilCollection.isEmpty(inspectNoticeUpdatePO.getItemList())) {
            log.warn("验收单没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ******** 检验验收单抬头 ******** */
        BizReceiptInspectHead head = bizReceiptInspectHeadDataWrap.getById(inspectNoticeUpdatePO.getId());
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_HEAD_EMPTY);
        }
        /* ******** 检验验收行项目 ******** */
        List<Long> itemIds = inspectNoticeUpdatePO.getItemList().stream().map(BizReceiptInspectItemDTO::getId)
            .collect(Collectors.toList());
        List<BizReceiptInspectItem> itemList = bizReceiptInspectItemDataWrap.listByIds(itemIds);
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_ITEM_EMPTY);
        }
        if (itemIds.size() > itemList.size()) {
            List<Long> newItemIds = itemList.stream().map(BizReceiptInspectItem::getId).collect(Collectors.toList());
            itemIds.removeAll(newItemIds);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_NOTICE_ITEM_EMPTY_RID);
        }
    }

    /**
     * 校验验收单可验收数量
     *
     * @param itemList 验收单行项目
     */
    private void checkQty(List<BizReceiptInspectItemDTO> itemList) {
        List<String> errorRidList = new ArrayList<>(itemList.size());
        itemList.forEach(itemDTO -> {
            // 校验合格数量
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorRidList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_QTR_ZERO, errorRidList.toString());
        }
    }

    /**
     * 将验收单更新为已验收状态
     * 
     * @param ctx ctx
     */
    public void updateInspected(BizContext ctx) {
        // 入参上下文
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 获取验收单
        BizReceiptInspectHead bizInspectNoticeHead = bizReceiptInspectHeadDataWrap.getById(id);
        // 转DTO
        BizReceiptInspectHeadDTO bizInspectNoticeHeadDTO =
            UtilBean.newInstance(bizInspectNoticeHead, BizReceiptInspectHeadDTO.class);
        if (UtilObject.isNotNull(bizInspectNoticeHeadDTO)) {
            // 如果行项目全部已完成
            if (this.checkAllItemStatus(bizInspectNoticeHeadDTO.getId())) {
                // 更新采购验收单head状态为已验收
                this.updateStatus(bizInspectNoticeHeadDTO, null, EnumReceiptStatus.RECEIPT_STATUS_INSPECTED.getValue(),
                    null);
                // 设置已验收单据到上下文
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO, bizInspectNoticeHeadDTO);
                // 设置 已全部验收完成
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_INSPECTED, true);
            }
        }
    }

    /**
     * 验收单全部验收完城时 将验收单更新为已完成
     * 
     * @param ctx ctx
     */
    public void updateCompletedWithInspected(BizContext ctx) {
        // 入参上下文 是否全部验收完
        Boolean inspected = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INSPECTED);
        BizReceiptInspectHeadDTO bizInspectNoticeHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        if (inspected != null && inspected) {
            // 更新采购验收单head状态为已完成
            this.updateStatus(bizInspectNoticeHeadDTO, bizInspectNoticeHeadDTO.getItemList(),
                EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue(), null);
            // 设置已验收单据到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO, bizInspectNoticeHeadDTO);
        }
    }

    /**
     * 获取可码盘状态
     * 
     * @return 可码盘状态
     */
    private List<Integer> getCantPalletStatus() {
        List<Integer> cantPalletStatus = new ArrayList<>();
        cantPalletStatus.add(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        cantPalletStatus.add(EnumReceiptStatus.RECEIPT_STATUS_INSPECTING.getValue());
        return cantPalletStatus;
    }

    /**
     * 验收单 全部验收完成时 过账
     * 
     * @param ctx ctx
     */
    public void postWithInspected(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO bizInspectNoticeHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        // 入参上下文 是否全部验收完
        Boolean inspected = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INSPECTED);
        if (inspected != null && inspected) {
            // 填充父子属性和关联属性,获取全部验收行项目
            dataFillService.fillAttr(bizInspectNoticeHeadDTO);
            this.generateInsDocToPost(ctx);
            this.postInspectToSap(ctx);
            this.postInspectToIns(ctx);
        }
    }

    /**
     * 生成凭证
     * 
     * @param ctx ctx
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = inspectMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * SAP过账
     * 
     * @param ctx ctx
     */
    public void postInspectToSap(BizContext ctx) {
        // 入参上下文 - 验收单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        List<BizReceiptInspectItemDTO> itemListNotSync = headDTO.getItemList().stream()
            .filter(e -> EnumRealYn.FALSE.getIntValue().equals(e.getIsPost())).collect(Collectors.toList());
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 设置验收过账账期 ******** */
            this.setInPostDate(itemListNotSync, user);
            /* ******** 调用sap ******** */
            returnObj = erpPostingService.posting(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyy-MM-dd",
                SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptInspectItemDTO inspectItemDTO : itemListNotSync) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                            .filter(item -> item.getReceiptCode().equals(inspectItemDTO.getReceiptCode())
                                && item.getReceiptRid().equals(inspectItemDTO.getRid()))
                            .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        inspectItemDTO.setMatDocCode(currentReturnObject.getMatDocCode());
                        inspectItemDTO.setMatDocRid(currentReturnObject.getMatDocRid());
                        inspectItemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        inspectItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        // 过账成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                if (insDocBatch.getPreReceiptItemId().equals(inspectItemDTO.getId())) {
                                    insDocBatch.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBatch.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBatch.setPostingDate(inspectItemDTO.getPostingDate());
                                    insDocBatch.setDocDate(inspectItemDTO.getDocDate());
                                    insDocBatch.setMatDocYear(
                                        UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                                if (insDocBin.getPreReceiptItemId().equals(inspectItemDTO.getId())) {
                                    insDocBin.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBin.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBin.setMatDocYear(
                                        UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新验收单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                    this.updateItem(itemListNotSync);
                }
                // 更新验收单状态 - 已记账
                this.updateStatus(headDTO, itemListNotSync, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue(), null);
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("验收单{}SAP过账失败", headDTO.getReceiptCode());
                // 更新验收单head、item状态-未同步
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue(),
                    null);
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync = headDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getMatDocCode)
                .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    /**
     * 修改库存
     * 
     * @param ctx ctx
     */
    public void postInspectToIns(BizContext ctx) {
        // 入参上下文-ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 入参上下文-验收单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        try {
            // 修改库存
            stockCommonService.modifyStock(insMoveTypeDTO);
            // 更新验收单状态 - 已记账
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue(), null);
            // 单据日志 - 过账
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("验收单{}ins过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 失败时更新验收单及行项目为未同步
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue(), null);
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param inspectItemDTOList 未同步sap验收单行项目
     * @param user 当前用户
     */
    private void setInPostDate(List<BizReceiptInspectItemDTO> inspectItemDTOList, CurrentUser user) {
        if (UtilCollection.isEmpty(inspectItemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = inspectItemDTOList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        for (BizReceiptInspectItemDTO inspectItemDTO : inspectItemDTOList) {
            if (EnumRealYn.FALSE.getIntValue().equals(inspectItemDTO.getIsWriteOff())) {
                inspectItemDTO.setDocDate(UtilDate.getNow());
                inspectItemDTO.setPostingDate(postingDate);
            } else {
                inspectItemDTO.setWriteOffDocDate(UtilDate.getNow());
                inspectItemDTO.setWriteOffPostingDate(postingDate);
            }
        }
    }

    /**
     * 冲销前校验
     * 
     * @param ctx ctx
     */
    public void checkInspectInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getHeadId()) && UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取验收单行项目
        List<BizReceiptInspectItem> inspectItemList = bizReceiptInspectItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptInspectItemDTO> inspectItemDTOList =
            UtilCollection.toList(inspectItemList, BizReceiptInspectItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(inspectItemDTOList);
        // 校验行项目单据类型及状态
        Set<String> ridSet = inspectItemDTOList.stream().filter(e -> !this.itemCanWriteOff(e))
            .map(BizReceiptInspectItemDTO::getRid).collect(Collectors.toSet());
        if (ridSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        // 冲销标识等于1或者过账标识等于0
        Set<String> isWriteOff = inspectItemDTOList.stream()
            .filter(e -> EnumRealYn.TRUE.getIntValue().equals(e.getIsWriteOff())
                || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
            .map(BizReceiptInspectItemDTO::getRid).collect(Collectors.toSet());
        if (isWriteOff.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, isWriteOff.toString());
        }
        // 验证只有对应入库单行项目为已冲销状态时，对应的验收单行项目才可以冲销
        List<BizReceiptInputItemDTO> inputItemList = inputFeignApi.getInputItemList(inspectItemDTOList);
        if (inputItemList != null && inputItemList.size() > 0) {
            for (BizReceiptInputItemDTO inputItem : inputItemList) {
                if (inputItem.getIsWriteOff().equals(EnumRealYn.FALSE.getIntValue())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_WRITE_OFF_ITEM_EXCEPTION);
                }
            }
        }
        // 获取验收单
        BizReceiptInspectHead inspectHead = bizReceiptInspectHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptInspectHeadDTO inputHeadDTO = UtilBean.newInstance(inspectHead, BizReceiptInspectHeadDTO.class);
        // 设置冲销标识
        inspectItemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        inputHeadDTO.setItemList(inspectItemDTOList);
        // 设置要冲销的验收验收单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputHeadDTO);
    }

    /**
     * 冲销行项目校验
     *
     * @param inspectItemDTO BizReceiptInspectItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptInspectItemDTO inspectItemDTO) {
        Integer receiptStatus = inspectItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus));
    }

    /**
     * 生成ins凭证
     * 
     * @param ctx ctx
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = inspectWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * sap验收冲销
     * 
     * @param ctx ctx
     */
    public void writeOffInputToSap(BizContext ctx) {
        // 入参上下文 - 验收单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        // 未同步sap行项目
        List<BizReceiptInspectItemDTO> itemListNotSync = headDTO.getItemList().stream()
            .filter(e -> !StringUtils.hasText(e.getWriteOffMatDocCode()) && StringUtils.hasText(e.getMatDocCode()))
            .collect(Collectors.toList());
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 设置冲销账期 ******** */
            this.setInPostDate(itemListNotSync, user);
            /* ******** 调用sap ******** */
            returnObj = erpPostingService.posting(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyy-MM-dd",
                SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    for (BizReceiptInspectItemDTO inputItemDTO : itemListNotSync) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                            .filter(item -> item.getReceiptCode().equals(inputItemDTO.getReceiptCode())
                                && item.getReceiptRid().equals(inputItemDTO.getRid()))
                            .findFirst().orElse(null);
                        if (null == currentReturnObject) {
                            continue;
                        }
                        inputItemDTO.setWriteOffMatDocCode(currentReturnObject.getMatDocCode());
                        inputItemDTO.setWriteOffMatDocRid(currentReturnObject.getMatDocRid());
                        inputItemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        // 冲销成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch dto : insMoveTypeDTO.getInsDocBatchList()) {
                                if (dto.getPreReceiptItemId().equals(inputItemDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setPostingDate(inputItemDTO.getWriteOffPostingDate());
                                    dto.setDocDate(inputItemDTO.getWriteOffDocDate());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin dto : insMoveTypeDTO.getInsDocBinList()) {
                                if (dto.getPreReceiptItemId().equals(inputItemDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新验收单行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】
                    this.updateItem(itemListNotSync);
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("验收单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        } else {
            // 已同步sap行项目物料凭证号
            Set<String> itemMatDocCodeSync =
                headDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getWriteOffMatDocCode)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            // 已经过账成功的
            returnObj.setMatDocCode(itemMatDocCodeSync.toString());
            returnObj.setSuccess(Const.ERP_RETURN_TYPE_S);
        }
    }

    /**
     * ins验收冲销
     * 
     * @param ctx ctx
     */
    public void writeOffInputToIns(BizContext ctx) {
        // 入参上下文 - 验收单
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - 凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        try {
            /* ***** 修改库存 ***** */
            stockCommonService.modifyStock(insMoveTypeDTO);
            /* ***** 更新item状态-已冲销 ***** */
            this.updateStatus(null, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue(),
                null);
            /* ***** 更新head状态-已完成 ***** */
            List<Integer> itemStatusList = this.getItemStatusList(headDTO.getId());
            if (bizCommonService.getCompletedItemStatusSet().containsAll(itemStatusList)) {
                // 所有行项目状态都是【已完成】或【冲销中】或【已冲销】时，修改单据状态为已完成
                this.updateStatus(headDTO, null, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue(), null);
            }
            /* ***** 单据日志 - 冲销 ***** */
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("验收单{}ins冲销过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 获取行项目状态集合
     *
     * @param headId 单据id
     * @return 状态列表
     */
    public List<Integer> getItemStatusList(Long headId) {
        // 获取验收单行项目
        List<BizReceiptInspectItem> inputItemList = bizReceiptInspectItemDataWrap
            .list(new QueryWrapper<BizReceiptInspectItem>().lambda().eq(BizReceiptInspectItem::getHeadId, headId));
        // 行项目状态集合
        return inputItemList.stream().map(BizReceiptInspectItem::getItemStatus).collect(Collectors.toList());
    }

}