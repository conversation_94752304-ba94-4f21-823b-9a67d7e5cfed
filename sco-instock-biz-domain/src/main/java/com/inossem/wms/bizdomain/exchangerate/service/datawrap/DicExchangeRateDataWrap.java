package com.inossem.wms.bizdomain.exchangerate.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.exchangerate.dao.DicExchangeRateMapper;
import com.inossem.wms.common.model.masterdata.exchangerate.entity.DicExchangeRate;
import com.inossem.wms.common.model.masterdata.exchangerate.po.DicExchangeRateSearchPO;
import com.inossem.wms.common.model.masterdata.exchangerate.vo.DicExchangeRatePageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 汇率主数据数据访问包装类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class DicExchangeRateDataWrap extends BaseDataWrap<DicExchangeRateMapper, DicExchangeRate> {

    /**
     * 分页查询汇率主数据列表
     *
     * @param page 分页对象
     * @param po   查询参数
     * @return 汇率主数据分页列表
     */
    public List<DicExchangeRatePageVO> selectPageVOList(IPage<DicExchangeRatePageVO> page, DicExchangeRateSearchPO po) {
        return this.baseMapper.selectPageVOList(page, po);
    }
}
