package com.inossem.wms.bizdomain.input.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizdomain.input.dao.BizReceiptInputHeadMapper;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 入库单表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-15
 */
@Service
public class BizReceiptInputHeadDataWrap extends BaseDataWrap<BizReceiptInputHeadMapper, BizReceiptInputHead> {
    @Autowired
    private DataFillService dataFillService;


    /**
     * 零价值入库-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getWorthlessInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                              WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                              EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getWorthlessInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 零价值入库单-列表
     *
     * @param listWrapper  查新条件
     * @param dataFillType 填充类型
     * @return List<BizReceiptInputHeadVO>
     */
    public List<BizReceiptInputHeadVO> getWorthlessInputList(WmsQueryWrapper<BizReceiptInputSearchPO> listWrapper, EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getWorthlessInputList(null, listWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return inputList;
    }

    /**
     * 获取采购入库-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getPurchaseInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                             WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                             EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getPurchaseInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 获取采购入库-列表
     *
     * @param listWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public List<BizReceiptInputHeadVO> getPurchaseInputList(WmsQueryWrapper<BizReceiptInputSearchPO> listWrapper, EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getPurchaseInputList(null, listWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return inputList;
    }

    /**
     * 获取生产入库-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getProductionInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                               WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                               EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getProductionInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 获取生产入库-列表
     *
     * @param listWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public List<BizReceiptInputHeadVO> getProductionInputList(WmsQueryWrapper<BizReceiptInputSearchPO> listWrapper, EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getProductionInputList(null, listWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return inputList;
    }

    /**
     * 获取验收入库-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getInspectInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                            WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                            EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getInspectInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }
    /**
     * 获取验收入库-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getInspectInputListUnitized(IPage<BizReceiptInputHeadVO> pageData,
                                                            WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                            EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getInspectInputListUnitized(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }
    /**
     * 获取验收入库-列表
     *
     * @param listWrapper  查新条件
     * @param dataFillType 填充类型
     * @return List<BizReceiptInputHeadVO>
     */
    public List<BizReceiptInputHeadVO> getInspectInputList(WmsQueryWrapper<BizReceiptInputSearchPO> listWrapper, EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getInspectInputList(null, listWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return inputList;
    }

    /**
     * 获取临时入库-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getTempInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                         WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                         EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getTempInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }


    /**
     * 获取入库冲销-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getInputWriteOffList(IPage<BizReceiptInputHeadVO> pageData,
                                                            WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                            EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getInputWriteOffList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 获取成套设备入库冲销-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getUnitizedInputWriteOffList(IPage<BizReceiptInputHeadVO> pageData,
                                                             WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                             EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getUnitizedInputWriteOffList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 获取临时入库-列表
     *
     * @param listWrapper  查新条件
     * @param dataFillType 填充类型
     * @return List<BizReceiptInputHeadVO>
     */
    public List<BizReceiptInputHeadVO> getTempInputList(WmsQueryWrapper<BizReceiptInputSearchPO> listWrapper, EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getTempInputList(null, listWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return inputList;
    }

    /**
     * 获取其他入库-分页
     *
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getOtherInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                          WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                          EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getOtherInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    public IPage<BizReceiptInputHeadVO> selectRepairPageVoListByPo(IPage<BizReceiptInputHeadVO> page, BizReceiptInputSearchPO po) {
        return page.setRecords(this.baseMapper.selectRepairPageVoListByPo(page, po));
    }

    /**
     * 获取其他入库-分页
     *
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return IPage<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> geRepairInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                          WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                          EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getOtherInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 获取其他入库-分页
     *
     * @param listWrapper  查新条件
     * @param dataFillType 填充类型
     * @return List<BizReceiptInputHeadVO>
     */
    public List<BizReceiptInputHeadVO> getOtherInputList(WmsQueryWrapper<BizReceiptInputSearchPO> listWrapper, EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getOtherInputList(null, listWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return inputList;
    }

    /**
     * 获取工器具入库-分页
     *
     * @param pageWrapper  查新条件
     * @param dataFillType 填充类型
     * @return List<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getToolInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                         WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                         EnumDataFillType dataFillType) {

        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getToolInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }

    /**
     * 获取其工具归还-分页
     *
     * @param page 分页数据
     * @param po 查新条件
     * @return List<BizReceiptInputHeadVO>
     */
    public IPage<BizReceiptInputHeadVO> getBorrowInputPageVoList(IPage<BizReceiptInputHeadVO> page, BizReceiptInputSearchPO po) {
        return page.setRecords(this.baseMapper.selectBorrowInputPageVoList(page, po));
    }

    /**
     * 根据headId查询工器具入库单信息
     * @param headId
     */
    public List<BizReceiptInputItemDTO> selectToolInfoByHeadId(Long headId, String specStock) {
        return this.baseMapper.queryToolInfoByHeadId(headId,specStock);
    }

    /**
     * 获取废旧物资入库单信息-分页
     * @param pageData
     * @param pageWrapper
     * @param dataFillType
     */
    public IPage<BizReceiptInputHeadVO> getWasterMaterialsInputList(IPage<BizReceiptInputHeadVO> pageData,
                                                                    WmsQueryWrapper<BizReceiptInputSearchPO> pageWrapper,
                                                                    EnumDataFillType dataFillType) {
        List<BizReceiptInputHeadVO> inputList = this.baseMapper.getWasterMaterialsInputList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }


}
